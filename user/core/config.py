from functools import lru_cache
import secrets
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Defines application settings, loaded from environment variables or a .env file."""
    SERVER_PORT: int = 8000
    
    API_V2_STR: str = ""
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8

    ACCESS_TOKEN_URL = "/api/token"

    BACKEND_CORS_ORIGINS: list = ["*"]

    PROJECT_NAME: str

    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    EXCHANGE_NAME: str

    JWT_SECRET: str
    JWT_ALGORITHM: str
    
    UNIPILE_SUBDOMAIN : str
    UNIPILE_PORT : str
    UNIPILE_API_KEY : str

    STRIPE_SECRET_KEY: str
    STRIPE_PUBLISHABLE_KEY: str
    class Config:
        """Pydantic configuration to load settings from a .env file."""
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    """Returns a cached instance of the Settings object."""
    return Settings()

# Global settings object for the application.
settings = get_settings()