from utils.migrate import run_migration
from sqlalchemy_utils import database_exists, create_database
from database.database import POSTGRES_DATABASEURL, SessionFactory, get_session
from database.models import Model, SystemPrompt, PromptType
from schema.ai_config_schema import EMAIL_GENERATION_V1, EMAIL_GENERATION_V2
import asyncio
from sqlalchemy import insert, text
from loguru import logger

async def init_data():
    async with SessionFactory() as db:
        model_to_create = Model(
            model_id = 1,
            model_name = 'gpt-4o-mini',
            display_name = 'gpt-4o-mini',
            provider = 'OpenAI',
            max_token = 2048,
            price_in = 0.00015,
            price_out = 0.0006,
        )
        db.add(model_to_create)
        await db.commit()

async def init_prompt():
    async for session in get_session():
        # new_prompt = SystemPrompt(
        #     prompt_name = 'email_generation_v1',
        #     prompt_content = EMAIL_GENERATION_V1,
        #     prompt_type = PromptType.EMAIL_GEN,
        #     is_disabled = True,
        # )
        # session.add(new_prompt)
        # await session.commit()

        stmt = insert(SystemPrompt).values([
            {'prompt_name': "email_generation_v1", 'prompt_content': EMAIL_GENERATION_V1, 'prompt_type': PromptType.EMAIL_GEN, 'is_disabled': True},
            {'prompt_name': "email_generation_v2", 'prompt_content': EMAIL_GENERATION_V2, 'prompt_type': PromptType.EMAIL_GEN, 'is_disabled': False}
        ])     
        await session.execute(stmt)
        await session.commit()

async def init_function():
    # try:
    #     logger.info('Init model data...')
    #     await init_data()
    #     logger.success('Done!')
    # except:
    #     logger.error('Init model data failed!')

    try:
        logger.info('Init prompt data...')
        await init_prompt()
        logger.success('Done!')
    except:
        logger.error('Init prompt data failed!')
        

if __name__=='__main__':
    if not database_exists(POSTGRES_DATABASEURL):
        create_database(POSTGRES_DATABASEURL)
    run_migration()
    try:
        asyncio.run(init_function())
        asyncio.run(init_data())
    except:
        pass