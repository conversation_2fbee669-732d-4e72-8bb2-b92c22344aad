import React from "react";
import { SectionWrapper } from "./SectionWrapper";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "../ui/accordion";

const faqs = [
  {
    question: "How does BritAI work?",
    answer:
      "Simply upload your mortgage or finance agreement PDF (or other relevant documents) that your bank or lender provided. Our AI will analyze the document for potential overpayments or mis-sold products. You’ll receive a summary of the findings, and can pay to download the full detailed report.",
  },
  {
    question: "What types of documents can I upload?",
    answer:
      "You can upload mortgage statements, loan agreements, vehicle finance contracts, or any official documents provided by your bank or lender. PDF format is preferred for best results.",
  },
  {
    question: "Is my data secure?",
    answer:
      "Yes, your documents are processed securely and confidentially. We never share your data with third parties.",
  },
  {
    question: "Do I have to pay upfront?",
    answer:
      "No, you can upload your documents and view a summary of the AI’s findings for free. Payment is only required if you wish to download the full detailed report.",
  },
  {
    question: "How long does the analysis take?",
    answer:
      "Most documents are analyzed within a few minutes. You’ll be notified as soon as your results are ready.",
  },
];

export function FAQSection() {
  return (
    <SectionWrapper id="faq" className="py-16 bg-white">
      <h2 className="text-3xl md:text-4xl font-bold text-[#3d4b3d] mb-10 text-center">
        Frequently Asked Questions
      </h2>
      <div className="w-full flex justify-center">
        <div className="w-[500px] max-w-full">
          <Accordion type="single" collapsible>
            {faqs.map((faq, idx) => (
              <AccordionItem key={idx} value={`faq-${idx}`}>
                <AccordionTrigger className="text-lg text-[#6ca16e] font-semibold">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-[#4d5c4d]">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </SectionWrapper>
  );
}
