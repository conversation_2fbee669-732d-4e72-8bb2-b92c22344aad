from core.config import settings
from utils.migrate import run_migration
from shared_state import app_state
from message_bus.task_consumer import start_task_consumers
from message_bus.rpc_server import start_rpc_server
from routes.v1.chat import chat_routes
from routes.v1.usage import usage_routes
from routes.v1.campaign import campaign_routes
from routes.v1.company import company_routes
from routes.v1.outreach import outreach_routes
from routes.v1.webhooks import webhooks_routes
from routes.v1.linkedin_outreach import linkedin_outreach_routes
from message_bus.rabbitmq_connection import RabbitMQConnectionPool
from task_scheduler.scheduler import start_scheduler

from fastapi import FastAPI
import uvicorn
import asyncio
from contextlib import asynccontextmanager
from loguru import logger

RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

@asynccontextmanager
async def lifespan(app: FastAPI):
    app_state["rabbitmq_conn_pool"] = RabbitMQConnectionPool(RABBIT_MQ_DSN)
    logger.info("Starting task consumers...")
    #declare rabbitmq task consumer
    consumer_task = asyncio.create_task(start_task_consumers(connection_pool=app_state["rabbitmq_conn_pool"]))
    #declare rpc server
    rpc_server = asyncio.create_task(start_rpc_server())
    #declare scheduler
    scheduler_task = asyncio.create_task(start_scheduler())

    yield
    consumer_task.cancel()
    rpc_server.cancel()
    scheduler_task.cancel()

    try:
        await consumer_task
    except asyncio.CancelledError:
        pass 

    try:
        await rpc_server
    except asyncio.CancelledError:
        pass

    try:
        await scheduler_task
    except asyncio.CancelledError:
        logger.info("Scheduler task canceled.")

    if "rabbitmq_conn_pool" in app_state:
        try:
            await app_state["rabbitmq_conn_pool"].close()
            logger.info("Closed RabbitMQ connection pool.")   
        except Exception as e:
            logger.error(f"Error while closing RabbitMQ connection pool: {e}")

app = FastAPI(lifespan=lifespan)

 
# Add routes
app.include_router(chat_routes)
app.include_router(usage_routes)
app.include_router(campaign_routes)
app.include_router(company_routes)
app.include_router(outreach_routes)
app.include_router(webhooks_routes)
app.include_router(linkedin_outreach_routes)



@app.get('/api/health')
async def health_check():
    return {'status': 'ok'}



if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    run_migration()
    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment