import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import whiteLogo from "../../assets/img/roboman-logo.png"; 

const AuthHeader = () => {
  const navigate = useNavigate();
  const location = useLocation();

  return (
    <header
      className="w-full h-20 flex justify-between items-center px-6 md:px-12
        sticky top-0 z-50 bg-gradient-to-r from-[#192038]/95 via-[#121829]/95 to-[#162347]/95
        border-b border-blue-200/30 shadow-md backdrop-blur-md"
      style={{
        letterSpacing: 0.2,
        fontFamily: "Inter, Segoe UI, sans-serif",
        fontWeight: 500,
      }}
    >
      {/* 🔹 Logo (Click → Home) */}
      <img
        src={whiteLogo}
        alt="Roboman Logo"
        className="w-[110px] md:w-[140px] h-[40px] object-contain cursor-pointer transition-transform duration-300 hover:scale-105"
        onClick={() => navigate("/")}
        style={{ filter: "drop-shadow(0 1px 8px #2557fa33)" }}
      />

      {/* 🔹 Navigation */}
      <div className="flex items-center gap-6 text-sm md:text-base text-gray-300 font-medium">
        {/* ✅ Highlight current page */}
        <button
          onClick={() => navigate("/login")}
          className={`transition ${
            location.pathname === "/login" ? "text-blue-400" : "hover:text-blue-400"
          }`}
        >
          Login
        </button>
        <button
          onClick={() => navigate("/signup")}
          className={`transition ${
            location.pathname === "/signup" ? "text-blue-400" : "hover:text-blue-400"
          }`}
        >
          Sign Up
        </button>
        <button
          onClick={() => navigate("/forgotpw")}
          className={`transition ${
            location.pathname === "/forgotpw" ? "text-blue-400" : "hover:text-blue-400"
          }`}
        >
          Forgot Password
        </button>
      </div>
    </header>
  );
};

export default AuthHeader;
