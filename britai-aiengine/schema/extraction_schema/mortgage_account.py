from pydantic import BaseModel, Field
from typing import Optional, List

class MortgageSubAccount(BaseModel):
    """Details for a sub-account within a mortgage offer."""
    sub_account_number: str = Field(..., description="The unique identifier for this specific mortgage account (sub-account).")
    sub_account_description: str = Field(None, description="A descriptive name or product type associated with this sub-account (e.g., 'Secured Loan', 'Unsecured Loan', 'Main Mortgage').")

class MortgageSubAccountList(BaseModel):
    """A list of mortgage sub-accounts extracted from one or more documents."""
    sub_accounts: List[MortgageSubAccount] = Field(..., description="A list of mortgage sub-accounts.")