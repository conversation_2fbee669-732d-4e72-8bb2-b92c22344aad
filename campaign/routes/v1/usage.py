from database.database import get_session
from message_bus.user_info_client import User<PERSON>n<PERSON>r<PERSON><PERSON>

from fastapi import API<PERSON><PERSON>er, <PERSON><PERSON>, Depends
from fastapi import status
from datetime import date
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import text
from loguru import logger
import json
from collections import defaultdict


router = APIRouter(
    prefix = "/api",
    tags=['Usage'],
    responses={404: {'description': 'Not found'}},
)


@router.get("/usage/assistant/{start_date}/{end_date}", status_code=status.HTTP_200_OK)
async def get_total_assistant_usage(
    start_date: date,
    end_date: date,
    db: AsyncSession = Depends(get_session),
    request_user_id: str = Header(None)
):
    raw_sql = text(f'''
        WITH assistant_statistics AS (
            SELECT
                conversations.system_prompt_id,
                SUM((conversation_messages.message_data->>'total_cost')::numeric) AS total_cost, 
                SUM((conversation_messages.message_data->>'total_token')::numeric) AS total_token, 
                COUNT(*) AS total_request
            FROM
                conversation_messages
            JOIN
                conversations
            ON
                conversations.conversation_id = conversation_messages.conversation_id
            WHERE
                DATE(conversation_messages.created_at) BETWEEN '{start_date}' AND '{end_date}'
            GROUP BY
                conversations.system_prompt_id
        )
                   
        SELECT
            system_prompts.chosen_position,
            assistant_statistics.total_cost,
            assistant_statistics.total_token,
            assistant_statistics.total_request
        FROM 
            assistant_statistics
        JOIN
            system_prompts
        ON
            assistant_statistics.system_prompt_id = system_prompts.prompt_id;
    ''')

    assistant_data = await db.execute(raw_sql)
    assistant_data = assistant_data.mappings().all()

    return assistant_data


@router.get("/usage/llm/{start_date}/{end_date}/{date_type}", status_code=status.HTTP_200_OK)
async def get_total_llm_usage(
    start_date: date,
    end_date: date,
    date_type: str,
    db: AsyncSession = Depends(get_session),
    request_user_id: str = Header(None)
):
    raw_sql = text(f'''
        SELECT
            conversation_messages.model_id,
            DATE_TRUNC('{date_type}', conversation_messages.created_at) AS date_group,
            SUM((conversation_messages.message_data->>'total_cost')::numeric) AS total_cost, 
            SUM((conversation_messages.message_data->>'total_token')::numeric) AS total_token, 
            COUNT(conversation_messages.chat_message_id) AS total_request
        FROM
            conversation_messages
        WHERE
            DATE(conversation_messages.created_at) BETWEEN '{start_date}' AND '{end_date}'
            AND EXTRACT(DOW FROM conversation_messages.created_at) NOT IN (0, 6)
            AND conversation_messages.model_id IN (3, 7, 8, 9, 10, 11)
        GROUP BY
            DATE_TRUNC('{date_type}', conversation_messages.created_at),
            conversation_messages.model_id
        ORDER BY
            date_group ASC;
    ''')

    llm_data = await db.execute(raw_sql)
    llm_data = llm_data.mappings().all()

    grouped_data = defaultdict(list)

    for entry in llm_data:
        date_group = entry["date_group"]
        model_data = {
            "model_id": entry["model_id"],
            "total_cost": entry["total_cost"],
            "total_request": entry["total_request"],
            "total_token": entry["total_token"],
        }
        grouped_data[date_group].append(model_data)

    usage_data = [{"date_group": date, "data": data} for date, data in grouped_data.items()]

    return {"usage_data": usage_data}


@router.get("/usage/user/{start_date}/{end_date}/{model_id}/{page}/{page_limit}", status_code=status.HTTP_200_OK)
async def get_total_user_usage(
    start_date: date,
    end_date: date,
    model_id: int,
    page: int,
    page_limit:int,
    db: AsyncSession = Depends(get_session),
    request_user_id: str = Header(None)
):
    if model_id == 0:
        model_id = '3, 7, 8, 9, 10, 11'

    raw_sql = text(f'''
        SELECT
            user_id,
            SUM((message_data->>'total_cost')::numeric) AS total_cost,
            SUM((message_data->>'total_token')::numeric) AS total_token,
            COUNT(*) AS total_request
        FROM
            conversation_messages
        WHERE
            DATE(conversation_messages.created_at) BETWEEN '{start_date}' AND '{end_date}'
            AND model_id IN ({model_id})
        GROUP BY
            user_id
        ORDER BY
            total_cost DESC
        LIMIT {page_limit} OFFSET {(page - 1) * page_limit}
    ''')

    user_data = await db.execute(raw_sql)
    user_data = user_data.mappings().all()

    message = [
        {
            "user_id": str(data["user_id"]),
            "total_cost": float(data["total_cost"]),
            "total_token": float(data["total_token"]),
            "total_request": data["total_request"]
        }
    for data in user_data]

    user_rpc = await UserInforClient().connect()
    response = await user_rpc.call(message)

    return json.loads(response.decode())
    

usage_routes = router