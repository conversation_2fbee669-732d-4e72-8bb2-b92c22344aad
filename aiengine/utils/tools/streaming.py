from llama_config.aiservice import token_counter
from schema.ai_config_schema import LLM
from schema.input_message_schema import InputDataCreate
from core.config import settings

from starlette.background import BackgroundTasks
from loguru import logger
import asyncio
import time
import aiohttp
import async_timeout


async def astreamer(
        resp,
        model_name,
        user_id,
        input_data: InputDataCreate,
        bg_tasks: BackgroundTasks = None
):
    """
    Asynchronous function to stream LLM response tokens and track usage metrics.
    
    Args:
        resp: Response object containing the token generator
        model_name: Name of the LLM model being used
        user_id: ID of the user making the request
        input_data: User input data including request message and conversation ID
        bg_tasks: FastAPI background tasks handler for post-processing
        
    Yields:
        Individual tokens from the LLM response for streaming
    """
    # Record start time for performance tracking
    start_time = time.time()

    # Initialize empty response string to accumulate tokens
    response = ''
    try:
        # Stream each token from the response generator
        for token in resp.response_gen:
            response += token
            yield token
            await asyncio.sleep(0.01)  # Small delay to control streaming rate
        
    except asyncio.CancelledError as e:
        # Handle streaming cancellation (e.g., client disconnection)
        logger.error(e)
        logger.error('Canceled!!!!!')

    # Record end time for performance tracking
    end_time = time.time()

    # Get token counts from the token counter
    prompt_token = token_counter.prompt_llm_token_count
    completion_token = token_counter.completion_llm_token_count

    # Calculate costs based on token usage and model pricing
    cost_in = (LLM[model_name].get('price_in') * prompt_token) / 1000
    cost_out = (LLM[model_name].get('price_out') * completion_token) /1000
    total_cost = cost_in + cost_out

    # Prepare data for conversation history storage
    data = {
        "model_id": input_data.model_id,
        "message_data": {
            "user": input_data.request_message,
            "assistant": response,
            "prompt_token": prompt_token,
            "completion_token": completion_token,
            "total_token": prompt_token + completion_token,
            "cost_in": cost_in,
            "cost_out": cost_out,
            "total_cost": total_cost,
            "processing_time": end_time - start_time,
        },
        "user_id": f"{user_id}",
        "conversation_id": f"{input_data.conversation_id}"
    }
    # Reset token counter for next request
    token_counter.reset_counts()

    # Add task to send conversation data to storage in background
    bg_tasks.add_task(send_chat_data, data, user_id)


async def send_chat_data(data, user_id):
    """
    Send conversation data to the campaign service for storage.
    
    Args:
        data: Dictionary containing conversation data and metrics
        user_id: ID of the user for request authentication
    """
    # Set HTTP method and endpoint URL for the API call
    method = 'post'
    url = settings.CAMPAIGN_SERVICE_URL + '/api/conversation/history'
    
    # Set request headers with user ID for authentication
    headers = {'request-user-id': user_id}

    # Make HTTP request with timeout protection
    with async_timeout.timeout(60):
        async with aiohttp.ClientSession() as session:
            request = getattr(session, method)
            await request(url, json=data, headers=headers)

