import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import AuthHeader from "./AuthHeader"; // ✅ import header

const Signup = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  const handleSignup = async (event) => {
    event.preventDefault();

    if (!email || !password || !confirmPassword) {
      Swal.fire({
        title: "Error!",
        text: "All fields are required.",
        icon: "error",
        confirmButtonColor: "#2563eb",
      });
      return;
    }

    if (password !== confirmPassword) {
      Swal.fire({
        title: "Error!",
        text: "Passwords do not match.",
        icon: "error",
        confirmButtonColor: "#2563eb",
      });
      return;
    }

    try {
      const response = await fetch(`${roboman_api}/signup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      if (response.ok) {
        Swal.fire({
          title: "Success!",
          text: "Account created successfully. Please log in.",
          icon: "success",
          confirmButtonColor: "#2563eb",
        }).then(() => {
          navigate("/login"); // redirect to login page
        });
      } else {
        const errorData = await response.json();
        const messages = Array.isArray(errorData.detail)
          ? errorData.detail.map((item) => item.msg).join(", ")
          : errorData.detail;

        Swal.fire({
          title: "Error!",
          text: messages,
          icon: "error",
          confirmButtonColor: "#2563eb",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error!",
        text: "Something went wrong. Please try again.",
        icon: "error",
        confirmButtonColor: "#2563eb",
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* ✅ Auth Header */}
      <AuthHeader />

      <div className="h-full w-full flex flex-1">
        {/* Left Section */}
        <div className="hidden md:flex w-1/2 bg-gradient-to-br from-blue-700 to-purple-600 text-white flex-col justify-center items-center p-12">
          <h1 className="text-4xl font-bold mb-4">
            Join <span className="text-yellow-400">Roboman</span>
          </h1>
          <p className="text-lg max-w-md text-center">
            Create your account to power your workflow with AI automation.
          </p>
          <img
            src="https://cdn-icons-png.flaticon.com/512/906/906343.png"
            alt="Illustration"
            className="mt-10 w-72"
          />
        </div>

        {/* Right Section (Form) */}
        <div className="flex w-full md:w-1/2 justify-center items-center bg-white shadow-lg">
          <div className="w-full max-w-md p-8">
            <div className="flex flex-col items-center mb-8">
              <h2 className="text-2xl font-bold mt-4">Sign Up</h2>
              <p className="text-gray-500">Create a new account</p>
            </div>

            {/* Signup Form */}
            <form className="space-y-4" onSubmit={handleSignup}>
              <input
                type="email"
                placeholder="Email Address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <input
                type="password"
                placeholder="Create Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <input
                type="password"
                placeholder="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
              />

              {/* Normal Signup Button */}
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg shadow-md hover:opacity-90 transition"
              >
                Sign Up
              </button>
            </form>

            {/* Divider */}
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-300"></div>
              <span className="px-4 text-gray-500 text-sm">or continue with</span>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            {/* Social Login Buttons - Circular + Horizontal */}
                        {/* ✅ Social Login - Circular + Horizontal */}
            <div className="flex justify-center space-x-6">
              {/* Google */}
              <button disabled
                onClick={() => {
                  const url = `${roboman_api}/auth/google`;
                  console.log("Redirecting to Google:", url);
                  window.location.href = url; // browser will handle redirect
                }}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
                >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/300/300221.png"
                  alt="Google"
                  className="w-6 h-6"
                />
              </button>



              {/* LinkedIn */}
              <button disabled
                onClick={() => (window.location.href = `${roboman_api}/login/linkedin`)}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
              >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/174/174857.png"
                  alt="LinkedIn"
                  className="w-6 h-6"
                />
              </button>

              {/* Microsoft */}
              <button disabled
                onClick={() => (window.location.href = `${roboman_api}/login/microsoft`)}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
              >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/732/732221.png"
                  alt="Microsoft"
                  className="w-6 h-6"
                />
              </button>
            </div>

            {/* Redirect to Login */}
            <p className="text-center text-gray-600 mt-6">
              Already have an account?{" "}
              <button
                type="button"
                onClick={() => navigate("/login")}
                className="text-purple-600 font-semibold hover:underline"
              >
                Sign In
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
