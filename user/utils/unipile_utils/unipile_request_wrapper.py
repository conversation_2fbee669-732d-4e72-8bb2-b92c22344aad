import requests
from core.config import settings

# Base URL for all Unipile API requests, constructed from settings.
BASE_URL = f"https://{settings.UNIPILE_SUBDOMAIN}.unipile.com:{settings.UNIPILE_PORT}/api/v1"


def unipile_get(endpoint_path: str) -> requests.Response:
    """
    Performs a GET request to a specified Unipile API endpoint.

    Args:
        endpoint_path (str): The path for the API endpoint (e.g., "/accounts").

    Returns:
        requests.Response: The response object from the GET request.
    """
    url = BASE_URL + endpoint_path
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    # Sends the GET request with the constructed URL and headers.
    response = requests.get(url=url, headers=headers)
    return response

def unipile_post(endpoint_path: str, form_data: str = None, json: dict = None) -> requests.Response:
    """
    Performs a POST request to a specified Unipile API endpoint.
    The payload can be either form_data or a JSON dictionary.

    Args:
        endpoint_path (str): The path for the API endpoint (e.g., "/chats").
        form_data (str, optional): A string payload for multipart/form-data requests.
        json (dict, optional): A dictionary payload for application/json requests.

    Returns:
        requests.Response: The response object from the POST request.
    """
    url = BASE_URL + endpoint_path
    
    if form_data is not None and json is not None:
        raise ValueError("Provide either form_data or json, not both.")
    
    if form_data is None and json is None:
        raise ValueError("Either form_data or json must be provided as payload.")
    
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    
    # Handle multipart/form-data requests
    if form_data is not None:
        headers["content-type"] = "multipart/form-data; boundary=---011000010111000001101001"
        response = requests.post(url=url, headers=headers, data=form_data)
    # Handle application/json requests
    else:
        headers["content-type"] = "application/json"
        response = requests.post(url=url, headers=headers, json=json)
    
    return response
    
def unipile_delete(endpoint_path: str) -> requests.Response:
    """
    Performs a DELETE request to a specified Unipile API endpoint.

    Args:
        endpoint_path (str): The path for the API endpoint (e.g., "/accounts/{account_id}").

    Returns:
        requests.Response: The response object from the DELETE request.
    """
    url = BASE_URL + endpoint_path
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    # Sends the DELETE request with the constructed URL and headers.
    response = requests.delete(url=url, headers=headers)
    return response