from celery import Celery

from core.config import settings  # assuming you have a config class with RABBITMQ_URL, etc.

RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

celery_app = Celery(
    "brit_aiengine",
    broker=RABBIT_MQ_DSN
)

celery_app.conf.update(
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    timezone="UTC",
    enable_utc=True,
)

from celery_app import tasks