import json

def postprocess(email_string):
    """
    Extract subject line and HTML content from an email string.
    
    Args:
        email_string (str): Raw email string containing subject and HTML content
        
    Returns:
        dict: Dictionary with 'head' (subject) and 'content' (HTML body)
    """
    # Split the string into lines for processing
    lines = email_string.splitlines()
    
    # Extract the subject line from the header
    head = None
    for line in lines:
        if line.startswith("Subject"):
            head = line.split(":")[-1].strip()
            break
    
    # Extract the HTML content between DOCTYPE and closing HTML tags
    content_start = email_string.find("<!DOCTYPE html>")
    content_end = email_string.find("</html>") + len("</html>")
    content = email_string[content_start:content_end]
    
    # Return structured data with subject and HTML content
    return {
        "head": head,
        "content": content.replace("\n","")  # Remove newlines from HTML
    }