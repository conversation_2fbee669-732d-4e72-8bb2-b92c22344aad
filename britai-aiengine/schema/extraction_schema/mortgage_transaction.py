from pydantic import BaseModel, Field
from typing import Optional, List

class PaymentYearList(BaseModel):
    """The list of the year of the payment history in the mortgage document."""
    list_of_payment_year: List[str] = Field(..., description="A list of years for which payment history is available.")

class TransactionDetail(BaseModel):
    """Details for a single transaction in the mortgage history."""
    effective_date: Optional[str] = Field(None, description="The date on which the transaction officially takes effect. Format as YYYY-MM-DD.")
    post_date: Optional[str] = Field(None, description="The date the transaction was recorded or posted by the lender. Format as YYYY-MM-DD.")
    description: str = Field(..., description="A text description of the transaction (e.g., 'ADV', 'INT', 'MRP', 'DDP', 'Capitalised Interest').")
    debit_amount: Optional[float] = Field(None, description="The amount of money debited from the account (e.g., charges, fees, new advances).")
    credit_amount: Optional[float] = Field(None, description="The amount of money credited to the account (e.g., repayments, overpayments, refunds).")

class AccountTransactionRecord(BaseModel):
    """A record of transactions for a single mortgage account."""
    mortgage_account_number: str = Field(..., description="The main identifier for this specific mortgage account.")
    transactions: List[TransactionDetail] = Field(default_factory=list, description="A list of individual financial transactions for this mortgage account.")

class MortgageTransactionHistory(BaseModel):
    """
    Pydantic schema for extracting comprehensive details from UK mortgage transaction history documents.
    This schema focuses on extracting transaction logs for potentially multiple accounts within a single document.
    """
    account_holder_name: Optional[str] = Field(None, description="The name of the primary account holder for these transactions, if available at a document level.")
    statement_report_date: Optional[str] = Field(None, description="The date the transaction history report was generated or extracted, if available at a document level. Format as YYYY-MM-DD.")
    account_records: List[AccountTransactionRecord] = Field(default_factory=list, description="A list of transaction records, each pertaining to a distinct mortgage account found in the document.")