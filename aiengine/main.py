from core.config import settings
from routes.v1.nlp_engine import llm_routes
from routes.v1.llm import model_routes
from utils.migrate import run_migration
# from message_bus.task_consumer import start_task_consumers
from message_bus.rpc_server import start_rpc_server
from shared_state import app_state
from message_bus.rabbitmq_connection import RabbitMQConnectionPool
from create_db import init_prompt

from loguru import logger
from fastapi import FastAPI
from loguru import logger
import uvicorn
import asyncio
from contextlib import asynccontextmanager

RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

@asynccontextmanager
async def lifespan(app: FastAPI):
    # app_state["rabbitmq_conn_pool"] = RabbitMQConnectionPool(RABBIT_MQ_DSN)
    print("Starting task consumers...")
    # consumer_task = asyncio.create_task(start_task_consumers(connection_pool=app_state["rabbitmq_conn_pool"]))
    rpc_server = asyncio.create_task(start_rpc_server())
    yield
    # consumer_task.cancel()
    rpc_server.cancel()
    # try:
    #     await consumer_task
    # except asyncio.CancelledError:
    #     pass 

    try:
        await rpc_server
    except asyncio.CancelledError:
        pass

    # if "rabbitmq_conn_pool" in app_state:
    #     try:
    #         await app_state["rabbitmq_conn_pool"].close()
    #         logger.info("Closed RabbitMQ connection pool.")   
    #     except Exception as e:
    #         logger.error(f"Error while closing RabbitMQ connection pool: {e}")

app = FastAPI(lifespan=lifespan)

# Add routes
app.include_router(llm_routes)
app.include_router(model_routes)


@app.get('/api/health')
async def health_check():
    return {'status': 'ok'}

if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    run_migration()
    try:
        logger.info("Init prompt...")
        asyncio.run(init_prompt())
        logger.success("Done!")
    except Exception as e:
        logger.error(f"Init prompt failed!: {str(e)}")
    logger.success("Done!")
    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment
