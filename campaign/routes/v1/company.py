from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.elasticsearch_utils.elastic_query import advanced_string_matching_search
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPC<PERSON>lient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    TestScheduleJob, CompanyMatching)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, 
                                    RespondEmail)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json


router = APIRouter(
    prefix = "/api",
    tags=['Campaign Management'],
    responses={404: {'description': 'Not found'}},
)


#region CAMPAIGN-COMPANY
@router.post("/campaign/{campaign_id}/companies/add-multiple")
async def add_companies(
    company_matching: CompanyMatching,
    campaign_id: uuid.UUID,
    request_user_id: str = Header(None),
    db : AsyncSession = Depends(get_session),
    es : Elasticsearch = Depends(get_elastic)
):
    """
    Adds multiple companies to a campaign by searching an Elasticsearch index.

    Args:
        company_matching: The search criteria for finding companies.
        campaign_id: The ID of the campaign to add companies to.
        request_user_id: The ID of the user making the request.
        db: The database session.
        es: The Elasticsearch client.
    """
    # Validate the search size to be within a reasonable range.
    if company_matching.search_size < 1 or company_matching.search_size > 300:
        raise HTTPException(
            status_code=400,
            detail="search_size must be between 1 and 300"
        )
    try:
        # Get existing company URLs in the campaign to avoid adding duplicates.
        query = select(Company.company_linkedin).where(Company.campaign_id==campaign_id)
        results = await db.execute(query)
        existing_company_urls = results.scalars().all()
        # Perform an advanced search in Elasticsearch based on the provided criteria.
        search_results = advanced_string_matching_search(
            es_client=es, 
            search_size=company_matching.search_size,
            industries=company_matching.industries,
            countries=company_matching.countries,
            cities_or_states=company_matching.cities_or_states,
            continents=company_matching.continents,
            existing_company_urls=existing_company_urls
        )

        # Fetch the campaign to link the new companies to.
        query = select(Campaign).where(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()

        # Create Company objects from the search results.
        companies_to_add = []
        for company in search_results:
            new_company = Company(
                campaign_id = campaign.campaign_id,
                company_name = company['Company Name'],
                industry = company['Company Industry'],
                company_email = None,
                company_linkedin = company['Company Linkedin Url'],
            )
            companies_to_add.append(new_company)

        # Add all new companies to the database in a single transaction.
        db.add_all(companies_to_add)
        await db.commit()
        
        # Create Representative objects for each new company.
        reps_to_add = []
        for company in search_results:
            # Retrieve the newly created company's ID.
            query = select(Company.company_id).filter(Company.company_linkedin==company['Company Linkedin Url'], Company.campaign_id==campaign_id)
            results = await db.execute(query)
            company_id = results.scalars().first()

            new_rep = Representative(
                company_id = company_id,
                rep_name = company['Full name'].title(),
                rep_email = company['Emails'].split(',')[-1].strip() if company['Emails'] else None,
                rep_linkedin_address = company['LinkedIn Url'],
                primary_contact = True,
            )
            reps_to_add.append(new_rep)

        # Add all new representatives to the database.
        db.add_all(reps_to_add)
        await db.commit()

        return {
            'status': 'success',
            'detail': f'found {len(companies_to_add)} matching companies'
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code = 500,
            detail = str(e)
        )


@router.post("/campaign/{campaign_id}/companies/add")
async def add_company_manual(
    campaign_id : uuid.UUID,
    company_add : CompanyAddManual,
    db: AsyncSession = Depends(get_session)
):
    """
    Manually adds a single company and its representative to a campaign.

    Args:
        campaign_id: The ID of the campaign to add the company to.
        company_add: The data for the new company and its representative.
        db: The database session.
    """
    query = select(Campaign).where(Campaign.campaign_id==campaign_id)
    results = await db.execute(query)
    existing_campaign = results.scalars().first()
    if existing_campaign:
        # Create and save the new company.
        new_company = Company(
            campaign_id = campaign_id,
            company_name = company_add.company_name,
            company_email = company_add.company_email,
            company_linkedin = company_add.company_linkedin,
            industry = company_add.industry,
        )

        db.add(new_company)
        await db.commit()
        await db.refresh(new_company)

        # Create and save the new representative for the company.
        new_rep = Representative(
            company_id = new_company.company_id,
            rep_name = company_add.rep_name,
            rep_email = company_add.rep_email,
            rep_linkedin_address = company_add.rep_linkedin_address,
            primary_contact = True
        )
        db.add(new_rep)
        await db.commit()

        return {
            'status': 'success',
            'detail': {
                'campaign': f"{existing_campaign.campaign_name}:{existing_campaign.campaign_id}"
            }
        }
    else:
        raise HTTPException(
            status_code=404,
            detail = 'campaign not found'
        )

@router.post("/campaign/{campaign_id}/companies/add/file")
async def ingest_companies(
    campaign_id : uuid.UUID,
    uploaded_files: UploadFile = File(...),
    db: AsyncSession = Depends(get_session),
):
    """
    Adds companies and their representatives to a campaign from an uploaded CSV file.

    Args:
        campaign_id: The ID of the campaign to add companies to.
        uploaded_files: The CSV file containing company and representative data.
        db: The database session.
    """
    try:
        max_file_size = 10 * 1024 * 1024
        # Validate file type and size.
        if not uploaded_files.filename.endswith('.csv'):
            raise HTTPException(status_code=415, detail="File must be a CSV")
        
        if uploaded_files.size > max_file_size:
            raise HTTPException(status_code=413, detail="File size exceeds the 5MB limit")
        
        # Read the uploaded file into a pandas DataFrame.
        contents = await uploaded_files.read()
        # Decode bytes to string
        str_file = contents.decode()
        # Create a StringIO object for pandas to read
        csv_file = StringIO(str_file)

        df = pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False)    
        
        new_company_count = 0
        # Iterate through each row in the DataFrame.
        for i in range(df.shape[0]):
            rep_name = df.loc[i]["Full name"]
            rep_email = df.loc[i]["Email"] if len(df.loc[i]["Email"]) > 0 else None
            rep_linkedin = df.loc[i]["LinkedIn Url"] if len(df.loc[i]["LinkedIn Url"]) > 0 else None
            company_name = df.loc[i]["Company Name"]
            company_industry = df.loc[i]["Company Industry"]
            company_linkedin = df.loc[i]["Company Linkedin Url"] if len(df.loc[i]["Company Linkedin Url"]) > 0 else None

            # Add the company only if a representative email or LinkedIn is provided.
            if rep_email is not None or rep_linkedin is not None:
                new_company = Company(
                    campaign_id = campaign_id,
                    company_name = company_name,
                    company_linkedin = company_linkedin,
                    industry = company_industry,
                    # email_outreach_stats = email_outreach_stats
                )       

                db.add(new_company)
                await db.commit()
                await db.refresh(new_company)

                new_rep = Representative(
                    company_id = new_company.company_id,
                    rep_name = rep_name,
                    rep_email = rep_email,
                    rep_linkedin_address = rep_linkedin,
                    primary_contact = True
                )
                db.add(new_rep)
                await db.commit()
                new_company_count += 1
                
        return {
            "status": "success",
            "detail": f"added {new_company_count} companies"
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/{campaign_id}/companies/count")
async def get_company_count(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Gets the total number of companies in a specific campaign.

    Args:
        campaign_id: The ID of the campaign.
        db: The database session.
    """
    try:
        query = select(func.count(Company.company_id)).where(Company.campaign_id==campaign_id)
        results = await db.execute(query)
        company_count = results.scalar()
        return {
            "company_count": company_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.get("/campaign/{campaign_id}/companies/{page}/{page_size}")
async def get_companies_by_campaign_id(
    campaign_id: uuid.UUID,
    page: int = 1,
    page_size: int = 10,    
    db: AsyncSession = Depends(get_session)
):
    """
    Retrieves a paginated list of companies and their primary representatives for a campaign.

    Args:
        campaign_id: The ID of the campaign.
        page: The page number to retrieve.
        page_size: The number of companies per page.
        db: The database session.
    """
    # Ensure page and page_size are valid.
    if page < 1:
        page = 1
    if page_size < 1:
        page_size = 1

    try:
        # Calculate the offset for pagination.
        offset = (page - 1) * page_size # Join Company and Representative tables to get all necessary data in one query.
        company_alias = aliased(Company)
        representative_alias = aliased(Representative)    
        query = (
            select(
                company_alias.company_id,
                company_alias.company_name,
                company_alias.industry,
                company_alias.content_subject,
                company_alias.content,
                company_alias.email_confirmation_status_id,
                company_alias.linkedin_message_status_id,
                company_alias.outreach_progress,
                company_alias.linkedin_outreach_progress,
                company_alias.auto_outreach,
                representative_alias.rep_id,
                representative_alias.rep_name,
                representative_alias.rep_email,
                representative_alias.rep_linkedin_address,
                representative_alias.personality_analysis,
                representative_alias.analysis_status
            )
            .join(representative_alias, company_alias.company_id == representative_alias.company_id)
            .filter(representative_alias.primary_contact == True, company_alias.campaign_id==campaign_id)
            .order_by(company_alias.company_name)
            .limit(page_size)
            .offset(offset)
        )        
        results = await db.execute(query)
        records = results.fetchall()        

        # Format the query results into a list of dictionaries.
        companies_list = [ # Conditionally access personality analysis to avoid errors if it's None.
            {
                "company_id": row.company_id,
                "company_name": row.company_name,
                "industry": row.industry,
                "content_subject": row.content_subject,
                "content": row.content,
                "email_confirmation_status_id": row.email_confirmation_status_id,
                "linkedin_message_status_id": row.linkedin_message_status_id,
                "outreach_progress": row.outreach_progress,
                "linkedin_outreach_progress": row.linkedin_outreach_progress,                
                "auto_outreach": row.auto_outreach,
                "rep_id": row.rep_id,
                "rep_name": row.rep_name,
                "rep_email": row.rep_email,
                "rep_linkedin_address": row.rep_linkedin_address,
                "ocean_label": row.personality_analysis['summary']['ocean']['label'] if row.personality_analysis else None,
                "disc_label": row.personality_analysis['summary']['disc']['label'] if row.personality_analysis else None,
                "analysis_status": row.analysis_status,                
            }
            for row in records
        ]

        print(len(companies_list))

        return companies_list



    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@router.put("/campaign/companies/update-email-status")
async def update_company_email(
    company_email_update: CompanyEmailUpdate, 
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
): 
    """
    Updates the email outreach status for a specific company.

    Args:
        company_email_update: The data containing the company ID and new status.
        db: The database session.
    """
    try:
        query = select(Company).filter(Company.company_id==company_email_update.company_id)
        results = await db.execute(query)
        company_to_update = results.scalars().first()
        if company_to_update:
            company_name = company_to_update.company_name
            data = {key: value for key, value in company_email_update.dict().items() if value is not None}
            del data['company_id']
            stmt = (
                update(Company)
                .filter(Company.company_id==company_to_update.company_id)
                .values(**data)
                .execution_options(synchronize_session="fetch")
            )   
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'updated successfully',
                'detail': {
                    'company': f"{company_name}:{company_email_update.company_id}",
                    'campaign': f"{company_to_update.campaign_id}"
                }
            }
        else:
            await db.rollback()
            raise HTTPException(
                status_code = 404,
                detail = 'company not found'
            )                                      
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    
    
@router.put("/campaign/companies/update-linkedin-msg-status")
async def update_company_linkedin_msg(
    company_linkedin_update: CompanyLinkedInMsgUpdate, 
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Updates the LinkedIn message outreach status for a specific company.

    Args:
        company_linkedin_update: The data containing the company ID and new status.
        db: The database session.
    """
    try:
        query = select(Company).filter(Company.company_id==company_linkedin_update.company_id)
        results = await db.execute(query)
        company_to_update = results.scalars().first()
        if company_to_update:
            company_name = company_to_update.company_name
            data = {key: value for key, value in company_linkedin_update.dict().items() if value is not None}
            del data['company_id']
            stmt = (
                update(Company)
                .filter(Company.company_id==company_to_update.company_id)
                .values(**data)
                .execution_options(synchronize_session="fetch")
            )   
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'updated successfully',
                'detail': {
                    'company': f"{company_name}:{company_linkedin_update.company_id}",
                    'campaign': f"{company_to_update.campaign_id}"
                }
            }
        else:
            raise HTTPException(
                status_code = 404,
                detail = 'company not found'
            )                             
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) 

@router.delete("/campaign/companies/{company_id}/delete")
async def delete_company_by_id(
    company_id: uuid.UUID,
    request_user_id : str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Deletes a company and its associated data from a campaign.

    Args:
        company_id: The ID of the company to delete.
        db: The database session.
    """
    try:
        query = select(Company).filter(Company.company_id==company_id)
        results = await db.execute(query)
        company_to_delete = results.scalars().first()
        if company_to_delete:
            # Delete the company record from the database.
            company_name = company_to_delete.company_name
            stmt = delete(Company).where(Company.company_id == company_id)
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'deleted successfully',
                'detail': {
                    'company': f"{company_name}:{company_id}",
                    'campaign': f"{company_to_delete.campaign_id}"
                }
            }
        else:
            raise HTTPException(
                status_code = 404,
                detail = 'company not found'
            )                                       
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )           

@router.put("/campaign/companies/{company_id}/representatives/{rep_id}/humantic/update")
async def update_humantic_profile(
    company_id: uuid.UUID,
    rep_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Updates a representative's profile with data from Humantic AI.

    If a cached profile exists, it updates the representative's record.
    Otherwise, it triggers a background task to fetch the profile.

    Args:
        company_id: The ID of the company the representative belongs to.
        rep_id: The ID of the representative to update.
        db: The database session.
    """
    try:
        return {
            "status": "success",
            "detail": "This endpoint is no longer available."
        }
    #     query = select(Representative).filter(Representative.rep_id==rep_id)
    #     query_results = await db.execute(query)
    #     rep = query_results.scalars().first()

    #     rep_linkedin_address = rep.rep_linkedin_address # Extract LinkedIn username from the URL.
    #     rep_linkedin_address = rep_linkedin_address[:-1] if rep_linkedin_address.endswith('/') else rep_linkedin_address        
    #     linkedin_username = rep_linkedin_address.split('/')[-1]

    #     query = select(HumanticProfile).filter(HumanticProfile.linkedin_username==linkedin_username) # Check for a cached Humantic profile.
    #     query_results = await db.execute(query)
    #     humantic_profile = query_results.scalars().first()

    #     if humantic_profile:
    #         # If a cached profile exists, update the representative's record.
    #         results = humantic_profile.results
    #         metadata = humantic_profile.meta_data

    #         profile_image = results['user_profile_image']
    #         user_description = results['user_description']
    #         work_history = results['work_history']
    #         location = results['location']
    #         skills = results['skills']
    #         followers = results['followers']
    #         prographics = results['prographics']
    #         education = results['education']
    #         cold_calling_advice = results['persona']['sales']['cold_calling_advice']
    #         communication_advice = results['persona']['sales']['communication_advice']
    #         email_personalization = results['persona']['sales']['email_personalization']
    #         hiring_behavioural_factors = results['persona']['hiring']['behavioural_factors']
    #         personal_analysis = results['personality_analysis']
    #         sales_profile_url = results['persona']['sales']['profile_url']
    #         analysis_status = metadata['analysis_status']
    #         analysis_confidence = metadata['confidence']            

    #         stmt = update(Representative).where(Representative.rep_id == rep_id).values(
    #             profile_image = profile_image,
    #             user_description = user_description,
    #             work_history = work_history,
    #             location = location,
    #             skills = skills,
    #             followers = followers,
    #             prographics = prographics,
    #             education = education,                       
    #             personality_analysis = personal_analysis,
    #             cold_calling_advice = cold_calling_advice,
    #             communication_advice = communication_advice,
    #             email_personalization = email_personalization,
    #             hiring_behavioural_factors = hiring_behavioural_factors,
    #             sales_profile_url = sales_profile_url,
    #             analysis_status = analysis_status,
    #             analysis_confidence = analysis_confidence
    #         )            
    #         await db.execute(stmt)    
    #         await db.commit()
    #         return {
    #             'status': 'success',
    #             'detail': f"updated {rep.rep_name}"
    #         }

    #     else:
    #         # If no cached profile, set status to "IN_PROGRESS" and trigger a fetch task.
    #         stmt = update(Representative).where(Representative.rep_id == rep_id).values(
    #             analysis_status = "IN_PROGRESS"
    #         )
    #         await db.execute(stmt)
    #         await db.commit()
    #         message = {
    #             "rep_linkedin": rep_linkedin_address,
    #             "company_id": str(company_id),
    #             "auto_outreach": False
    #         }
    #         await publish_task("update_humantic_profile", message, connection_pool=app_state["rabbitmq_conn_pool"])            
    #         return {
    #             'status': 'success',
    #             'detail': "humantic profile not readily available, it will be fetched from Humantic AI"
    #         }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )  

@router.get("/campaign/companies/{company_id}/representative/")
async def get_company_representative(
    company_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Gets the primary representative for a given company.

    Args:
        company_id: The ID of the company.
        db: The database session.
    """
    try:
        query = select(Representative).filter(Representative.company_id==company_id, Representative.primary_contact==True)
        results = await db.execute(query)
        reps = results.scalars().first()
        return reps
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/companies/representative/update")
async def update_company_representative(
    rep_update: RepUpdate,
    db: AsyncSession = Depends(get_session)
):
    """
    Updates the details of a company representative.

    Args:
        rep_update: The data for updating the representative.
        db: The database session.
    """
    try:
        query = select(Representative).filter(Representative.rep_id==rep_update.rep_id)
        results = await db.execute(query)
        rep_to_update = results.scalars().first()
        if rep_to_update:
            data = {key: value for key, value in rep_update.dict().items() if value is not None}
            stmt = (
                update(Representative)
                .filter(Representative.rep_id==rep_update.rep_id)
                .values(**data)
                .execution_options(synchronize_session="fetch")
            )
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'updated successfully',
                'detail': {
                    'rep': f"{rep_to_update.rep_name}:{rep_update.rep_id}",
                    'company': f"{rep_to_update.company_id}"
                }
            }
        else:
            raise HTTPException(
                status_code=404,
                detail='representative not found'
            )
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    
@router.delete("/campaign/companies/representative/{rep_id}/delete")
async def delete_company_representative(
    rep_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Deletes a company representative.

    Args:
        rep_id: The ID of the representative to delete.
        db: The database session.
    """
    try:
        query = select(Representative).filter(Representative.rep_id==rep_id)
        results = await db.execute(query)
        rep_to_delete = results.scalars().first()
        if rep_to_delete:
            rep_name = rep_to_delete.rep_name
            stmt = delete(Representative).where(Representative.rep_id==rep_id)
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'deleted successfully',
                'detail': {
                    'rep': f"{rep_name}:{rep_id}",
                    'company': f"{rep_to_delete.company_id}"
                }
            }
        else:
            raise HTTPException(
                status_code=404,
                detail='representative not found'
            )
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    
@router.get("/campaign/humantic/profiles")
async def get_humantic_cached_profiles(
    db: AsyncSession = Depends(get_session)
):
    """
    Retrieves all cached Humantic AI profiles from the database.

    Args:
        db: The database session.
    """
    try:
        query = select(HumanticProfile)
        results = await db.execute(query)
        profiles = results.scalars().all()
        return profiles
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion



company_routes = router
