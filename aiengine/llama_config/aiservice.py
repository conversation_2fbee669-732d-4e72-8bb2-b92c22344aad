from schema.ai_config_schema import EMBEDDING
from schema.ai_config_schema import LLM
from core.config import settings
from llama_index.multi_modal_llms.openai import OpenAIMultiModal
from llama_index.core.callbacks import Token<PERSON>ounting<PERSON>and<PERSON>
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.callbacks import CallbackManager
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.agent.openai import OpenAIAgent
from llama_index.core.agent import ReActAgent

from llama_index.llms.anthropic import Anthropic
from llama_index.llms.openai import OpenAI
from llama_index.llms.gemini import Gemini

from typing import Optional, List
import tiktoken

import openai
openai.verify_ssl_certs = False

# Initialize OpenAI embedding model for vector embeddings
embed_model = OpenAIEmbedding(
    model=EMBEDDING['embedv3-small']['model'],
    dimensions=1536,
    api_key=settings.OPENAI_API_KEY
)

# Set up token counter for tracking token usage
token_counter = TokenCountingHandler(
    tokenizer=tiktoken.get_encoding("cl100k_base").encode
)

# Initialize helper LLM for lightweight tasks
helper_llm = OpenAI(
    model = LLM["gpt-3.5-turbo"]['model'],
    api_key=settings.OPENAI_API_KEY
)

# Create callback manager with token counter
callback_manager = CallbackManager([token_counter])

# Initialize chat memory buffer with token limit
memory = ChatMemoryBuffer.from_defaults(token_limit=50000)


def get_llm(model_name: str):
    """
    Factory function to create LLM instances based on model name.
    
    Args:
        model_name (str): Name of the LLM model to initialize
        
    Returns:
        LLM instance: Configured LLM object from llama-index
    """
    if model_name in ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4']:
        llm = OpenAI(
            model=LLM[model_name]['model'],
            temperature=0,
            max_tokens=LLM[model_name]['max_tokens'],
            callback_manager=callback_manager,
            api_key=settings.OPENAI_API_KEY
        )
    elif model_name in ['gemini-pro', 'gemini-flash']:
        llm = Gemini(
            model_name=LLM[model_name]['model'],
            max_tokens=LLM[model_name]['max_tokens'],
            callback_manager=callback_manager
        )
    elif model_name in ['claude-pro', 'claude-fast', 'claude-medium']:
        llm = Anthropic(
            model=LLM[model_name]['model'],
            max_tokens=LLM[model_name]['max_tokens'],
            callback_manager=callback_manager
        )

    return llm


def run_agent(
        model_name: str,
        system_prompt: str,
        tools: Optional[List] = None,
        history: Optional[List] = None,
):
    """
    Create and configure an agent with tools based on model type.
    
    Args:
        model_name (str): Name of the LLM model to use
        system_prompt (str): System prompt for the agent
        tools (Optional[List]): List of tools for the agent to use
        history (Optional[List]): Chat history for context
        
    Returns:
        Agent: Configured agent instance (OpenAIAgent or ReActAgent)
    """
    if model_name in ['gpt-4o-mini', 'gpt-4-turbo', 'gpt-4']:
        agent = OpenAIAgent.from_tools(
            tools=tools,
            system_prompt=system_prompt,
            chat_history=history,
            llm=OpenAI(
                model=LLM[model_name]['model'],
                temperature=0,
                max_tokens=LLM[model_name]['max_tokens'],
                callback_manager=callback_manager,
                api_key=settings.OPENAI_API_KEY
            ),
            verbose=False
        )

    elif model_name in ['claude-pro', 'claude-fast', 'claude-medium']:
        agent = ReActAgent.from_tools(
            tools=tools,
            system_prompt=system_prompt,
            chat_history=history,
            llm=Anthropic(
                model=LLM[model_name]['model'],
                max_tokens=LLM[model_name]['max_tokens'],
                callback_manager=callback_manager
            ),
            verbose=False
        )

    elif model_name in ['gemini-pro', 'gemini-flash']:
        agent = ReActAgent.from_tools(
            tools=tools,
            system_prompt=system_prompt,
            chat_history=history,
            llm=Gemini(
                model_name=LLM[model_name]['model'],
                max_tokens=LLM[model_name]['max_tokens'],
                callback_manager=callback_manager
            ),
            verbose=False
        )
    
    return agent

# Initialize multimodal LLM for image and text processing
openai_mm_llm = OpenAIMultiModal(
    model="gpt-4o-2024-08-06", max_new_tokens=4096
)