from loguru import logger
from core.config import settings
from message_bus.rabbitmq_manager import RabbitMQManager

RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

TASK_QUEUES = [
    ("update_humantic_profile", "update_humantic_profile"),
    ("receive_reply_webhooks", "receive_reply_webhooks"),
    ("send_email_outreach", "send_email_outreach"),
    ("send_response", "send_response"),
    ("ingest_data", "ingest_data"),
    ("create_drafts", "create_drafts"),
    ("send_linkedin_outreach", "send_linkedin_outreach")
]

async def setup_rabbitmq_infrastructure():
    """
    Declare necessary RabbitMQ exchanges and queues.
    Safe to call multiple times — declarations are idempotent.
    """
    logger.info("[RABBITMQ] Setting up infrastructure...")

    rabbitmq_manager = RabbitMQManager(RABBIT_MQ_DSN)
    await rabbitmq_manager.connect()

    await rabbitmq_manager.declare_exchange("task_exchange", exchange_type="direct")



    for queue_name, routing_key in TASK_QUEUES:
        await rabbitmq_manager.declare_queue(queue_name)
        await rabbitmq_manager.bind_queue(queue_name, "task_exchange", routing_key)

    logger.info("[RABBITMQ] Infrastructure setup complete.")
    await rabbitmq_manager.close()
