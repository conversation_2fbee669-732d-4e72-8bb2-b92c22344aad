from database.models import ConversationMessage, Conversation
from crud.crud_base import CRUDBase
from schema.conversation_chatmessage_schema import (
    ChatMessageCreate, ChatMessageUpdate
)

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import uuid
import tiktoken
from loguru import logger


def num_tokens_from_string(string: str, encoding_name: str = "cl100k_base") -> int:
    """Returns the number of tokens in a text string."""
    encoding = tiktoken.get_encoding(encoding_name)
    num_tokens = len(encoding.encode(string))
    return num_tokens


async def create_and_add_message(
        db: AsyncSession,
        content,
        message_type,
        model_id, 
        conversation_id, 
        user_id,
        price_in: float,
        price_out: float,
        response = None, 
        parent_message_id = None,
):
    token_in = num_tokens_from_string(content)
    token_out = num_tokens_from_string(response)
    total_token = token_in + token_out
    cost_in = (price_in * token_in) / 1000
    cost_out = (price_out * token_out) / 1000

    message_data = {
        "user": content,
        "assistant": response,
        "message_token": token_in,
        "completion_token": token_out,
        "total_token": total_token,
        "cost_in": cost_in,
        "cost_out": cost_out,
        "total_cost": cost_in + cost_out,
        "processing_time": 0
    }

    message_to_add = ConversationMessage(
        message_data = message_data,
        conversation_id = conversation_id,
        parent_message_id = parent_message_id,
        model_id = model_id,
        message_type = message_type,
        user_id = user_id,
    )
    try:
        db.add(message_to_add)
        await db.commit()
    
    except Exception as e:
        logger.error(e)
        await db.rollback()


async def get_lastest_messages(db: AsyncSession, conversation_id):
    all_messages = await db.execute(
        select(
            ConversationMessage.message_data
        ).join(
            Conversation,
            Conversation.conversation_id == ConversationMessage.conversation_id
        ).filter(
            Conversation.conversation_id == conversation_id,
        ).order_by(
            ConversationMessage.created_at.asc()
        ).limit(30)
    )
    all_messages = all_messages.scalars().all()
    
    history = [message for message in all_messages] if all_messages else []

    return history


class CRUDChatMessage(CRUDBase[ConversationMessage, ChatMessageCreate, ChatMessageUpdate]):
    async def get_by_user_id(self, db: AsyncSession, *, usr_id: uuid.UUID) -> Optional[List[ConversationMessage]]:
        result = await db.execute(select(ConversationMessage).filter(ConversationMessage.user_id == usr_id))
        return result.scalars().all()


    async def get_by_conv_id(self, db: AsyncSession, *, conv_id: uuid.UUID) -> Optional[List[ConversationMessage]]:
        result = db.execute(select(ConversationMessage).filter(ConversationMessage.conversation_id == conv_id))
        return result.scalars().all()


crud_messages = CRUDChatMessage(ConversationMessage)
