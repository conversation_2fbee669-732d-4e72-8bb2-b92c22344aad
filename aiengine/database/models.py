from database.database import Base

import uuid
import pgvector
from pgvector.sqlalchemy import Vector
from sqlalchemy import SmallInteger, String, Column, Text, TIMESTAMP, Integer, Float, Index, Boolean, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import enum

########## LLM MODELS ##########
class Model(Base):
    """
    Database model for LLM model configurations.
    Stores information about available language models including pricing and capabilities.
    """
    __tablename__ = 'models'

    # Column mappings
    model_id = Column(
        SmallInteger,
        primary_key=True,
        index=True,
        autoincrement=True,
    )

    model_name = Column(String(255), nullable=False)
    display_name = Column(String(255), nullable=True)
    model_type = Column(String(255), nullable=True)
    provider = Column(String(255), nullable=True)
    reference = Column(Text, nullable=True)
    max_token = Column(Integer, nullable=True)
    price_in = Column(Float, nullable=True)
    price_out = Column(Float, nullable=True)
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )


class FAQuestion(Base):
    """
    Database model for frequently asked questions with vector embeddings.
    Enables semantic search using vector similarity.
    """
    __tablename__ =  "fa_questions"

    question_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,
    )

    question_text = Column(
        String,
        nullable=False
    )

    question_embedding = Column(
        Vector(1536), 
        nullable=False
    )

    answer_text = Column(
        String,
        nullable=False
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )    



    # Define HNSW index (this is ignored when creating tables, so we handle it separately)
    __table_args__ = (
        Index("idx_question_embedding_hnsw", 
              "question_embedding", 
              postgresql_using="hnsw",
              postgresql_ops={"question_embedding": "vector_cosine_ops"},),
    )    
class PromptType(enum.Enum):
    """
    Enumeration of available prompt types for system prompts.
    """
    EMAIL_GEN = "email_gen"
    RESPONSE_GEN = "response_gen"
    

class SystemPrompt(Base):
    """
    Database model for system prompts used by LLM models.
    Stores different prompt templates for various AI tasks.
    """
    __tablename__ = 'system_prompts'
    # Column mappings
    prompt_id = Column(
        UUID(as_uuid=True), primary_key=True, nullable=False,
        default=uuid.uuid4, index=True,
    )
    prompt_type = Column(Enum(PromptType), nullable=False)
    prompt_name = Column(String, nullable=False, unique=True)
    prompt_content = Column(String, nullable=False)
    is_disabled = Column(Boolean, server_default='True')

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )