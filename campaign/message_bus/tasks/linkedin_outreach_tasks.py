import requests
import json
import re
from loguru import logger
from sqlalchemy import update, delete, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
from sqlalchemy.ext.asyncio import  AsyncSession
from datetime import datetime, timedelta, time, date

from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from database.database import SessionFactory, get_session
from database.models import SentEmail, ReceivedEmail, Company, Draft, Representative, SenderEmail, Campaign, LinkedInConnection, SentMessage
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state

async def update_message_db(
    db: AsyncSession, 
    draft_message: Draft, 
    account_id: str, 
    message_content: str, 
    own_profile: dict,
    prospect_profile: dict,
    message_id: str = None, 
    chat_id: str = None):
    """
    Update database after sending LinkedIn message.
    Marks draft as sent and creates SentMessage record.
    """
    # Mark draft as sent
    stmt = update(Draft).where(Draft.draft_id==draft_message.draft_id).values(linkedin_sent=True)
    await db.execute(stmt)

    # Create sent message record
    new_message = SentMessage(
        message_content = message_content,
        account_id = account_id,
        company_id = draft_message.company_id,
        chat_id = chat_id,
        unipile_id = message_id,
        draft_type = draft_message.draft_type,
        sender_username = own_profile["public_identifier"],
        sender_provider_id = own_profile["provider_id"],
        recipient_username = prospect_profile["public_identifier"],
        recipient_provider_id = prospect_profile["provider_id"]
    )
    db.add(new_message)
    
    # Update company status
    stmt = update(Company).filter(Company.company_id==draft_message.company_id).values(
        linkedin_message_status_id=2, 
        linkedin_outreach_progress=draft_message.draft_type,
        last_date_sent_linkedin = date.today(),
        last_time_sent_linkedin = datetime.now()
    )
    await db.execute(stmt)
    
async def get_user_info(user_id: str):
    """
    Get user information via RPC call.
    Verifies LinkedIn connection status.
    
    Returns:
        dict: User information including LinkedIn connection details
    """
    # Make RPC call to user service
    rpc_client = RPCClient()
    rpc = await rpc_client.connect()
    response = await rpc.call(service="user.*", function_name="get_user_info", params={"user_id": user_id})
    response_body = json.loads(response)
    result = response_body.get("result")
    
    # Validate response
    if result == "error":
        raise Exception(
            "Error from RPC server: " + response_body.get("detail")
        )
    if result.get("unipile_linkedin_id") is None:
        raise Exception("The account appears to be disconnected from the unipile service.") 
    
    if result.get("linkedin_connection_status") != "CONNECTED":
        raise Exception("The account appears to be disconnected from the unipile service.")
    
    return result 

async def send_linkedin_outreach(message):
    """
    Process LinkedIn outreach task from message queue.
    Sends LinkedIn message or invitation based on draft content.
    
    Args:
        message: RabbitMQ message containing company_id and user_id
    """
    await asyncio.sleep(1)
    try:
        # Parse message
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]
        user_id = message_body["user_id"]
        logger.info(f"[CONSUMER] Processing send_linkedin_outreach task")

        # Get user LinkedIn info
        user_info = await get_user_info(user_id=user_id)   
        async for db in get_session():
            # Get company info
            query = select(Company).filter(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()
            if company is None:
                raise Exception("Company not found")

            # Skip if company status indicates no message needed
            if company.linkedin_message_status_id in [4,5]:
                logger.info(f"[CONSUMER] stopped sending linkedin message to company {company_id}")
                return

            # Get calendly link from campaign
            query = select(Campaign.campaign_contact).filter(Campaign.campaign_id==company.campaign_id)
            results = await db.execute(query)
            campaign_contact = results.scalars().first()
            calendly_link = campaign_contact.get("calendly_link")

            # Get unsent drafts
            query = select(Draft).filter(Draft.company_id==company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
            result = await db.execute(query)
            drafts = result.scalars().all()
            if len(drafts) <= 0:
                logger.info(f"[CONSUMER] no more messages to company {company_id}")
            else:
                # Process first unsent draft
                draft = drafts[0]
                linkedin_client = LinkedInClient()
                linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
                
                # Get profiles
                own_profile = linkedin_client.retrieve_own_profile().json()
                if "provider_id" not in own_profile:
                    raise Exception("Failed to retrieve own profile to send linkedin message")
                prospect_profile = linkedin_client.retrieve_profile(draft.to_linkedin_address).json()
                
                # Format message content
                message_content = draft.body_plain
                message_content = message_content.strip()
                message_content = message_content.replace("email","message")
                message_content = message_content.replace("Email","Message")

                # Split into sentences and join with newlines
                pattern = r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|!)\s'
                sentences = re.split(pattern, message_content)
                sentences = [sentence.strip() for sentence in sentences if sentence.strip()]
                final_text = "\n".join(sentences)
                message_content = final_text.strip()

                # Add calendly link if available
                if calendly_link is not None:
                    message_content = message_content + f"\nBook a meeting on Calendly: {calendly_link}"

                rpc = await RPCClient().connect()

                # Try sending message, fall back to invitation if needed
                response = linkedin_client.send_message(message_body=message_content, recipient_profile_url=draft.to_linkedin_address)
                if response.status_code == 422:
                    # Summarize draft for invitation (shorter message)
                    function_name = "summarize_draft"
                    params = {
                        "draft_content": draft.body_plain
                    }
                    rpc_response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
                    rpc_response_body = json.loads(rpc_response)
                    result = rpc_response_body.get("result")
                    if result == "error":
                        raise Exception(
                            "Can't summarize draft. Error from RPC server: " + rpc_response_body.get("detail")
                        )
                    summarized_draft = result["summarized_content"].replace("\n"," ").strip()
                    logger.info(f"summarized draft contains: {len(summarized_draft)} characters")
                    response = linkedin_client.send_invitation(message_body=str(summarized_draft), recipient_profile_url=draft.to_linkedin_address)                

                if response.status_code not in [200,201]:
                    raise Exception(f"Failed to send linkedin message to {draft.to_linkedin_address}\n {response.text}")
                
                # Process response
                message_id = None
                chat_id = None
                if "message_id" in response.json() and "chat_id" in response.json():
                    # Direct message was sent
                    message_id = response.json()["message_id"]
                    chat_id = response.json()["chat_id"]
                    message_content = draft.body_plain
                    
                    # Check for existing connection
                    query = (
                        select(LinkedInConnection).select_from(LinkedInConnection)
                        .where(
                            LinkedInConnection.provider_id==own_profile["provider_id"],
                            LinkedInConnection.connected_provider_id==prospect_profile["provider_id"]
                        )
                    )
                    
                    result = await db.execute(query)
                    linkedin_connection = result.scalars().first()
                    if linkedin_connection is None:
                        # Create new connection record
                        new_connection = LinkedInConnection(
                            provider_id = own_profile["provider_id"],
                            connected_provider_id = prospect_profile["provider_id"]                            
                        )
                        db.add(new_connection)   
                else:
                    # Invitation was sent
                    message_content = summarized_draft

                # Update database
                await update_message_db(
                    db=db, 
                    draft_message=draft, 
                    account_id=user_info["unipile_linkedin_id"], 
                    message_id=message_id, 
                    message_content=message_content,
                    chat_id=chat_id,                    
                    own_profile=own_profile,
                    prospect_profile=prospect_profile,
                )

                # Verify connection record
                query = (
                    select(LinkedInConnection).select_from(LinkedInConnection)
                    .where(
                        LinkedInConnection.provider_id==own_profile["provider_id"],
                        LinkedInConnection.connected_provider_id==prospect_profile["provider_id"]
                    )
                )
                result = await db.execute(query)
                linkedin_connection = result.scalars().first()

                await db.commit()
                logger.info(f"[CONSUMER] Successfully sent linkedin draft {draft.draft_type} to {draft.to_linkedin_address}")

    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process send_linkedin_outreach task\n {str(e)}")
