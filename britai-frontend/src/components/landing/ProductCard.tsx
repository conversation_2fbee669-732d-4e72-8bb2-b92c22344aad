"use client";

import { useRouter } from "next/navigation";
import { useCallback } from "react";

interface ProductCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
}

export default function ProductCard({
  icon,
  title,
  description,
  href,
}: ProductCardProps) {
  const router = useRouter();

  const handleClick = useCallback(() => {
    // Save to localStorage
    localStorage.setItem("selectedProductTitle", title);

    // Navigate to the page
    router.push(href);
  }, [href, title, router]);

  return (
    <div className="bg-white rounded-xl shadow-lg flex flex-col items-center p-8 w-80 min-h-[340px] transition-transform hover:scale-105">
      <div className="mb-6">{icon}</div>
      <h2 className="text-xl font-semibold text-[#3d4b3d] mb-4 text-center">
        {title}
      </h2>
      <p className="text-[#4d5c4d] text-center mb-6 flex-1">{description}</p>
      <button
        onClick={handleClick}
        className="mt-auto bg-[#6ca16e] hover:bg-[#5a8c5c] text-white font-medium px-6 py-2 rounded-lg transition-colors"
      >
        Start my claim
      </button>
    </div>
  );
}
