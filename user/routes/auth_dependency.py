from core.config import settings
from core.security import db
from crud import crud_user
# from app.extensions.logger import backend_logger
from database.models import User
# Assuming UserAccount is a Pydantic schema or a type alias for the User model.
from schemas.user import UserAccount

from typing import Annotated
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer

# OAuth2 scheme setup for FastAPI.
# This defines how clients should provide the token (in this case, as a Bearer token).
# The `tokenUrl` points to the endpoint where clients can obtain an access token.
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V2_STR}{settings.ACCESS_TOKEN_URL}",
    ## We will use scope later, for third-party integration
    # scopes={
    #     'me': 'Read information about the current user.',
    #     'user': 'Start chat, manage user information and chats',
    #     'admin': 'Manage users, chats and propmpt behaviours',
    #     'vip': 'Access new features without restriction',
    # },
)

async def get_user_from_token(token: Annotated[str, Depends(oauth2_scheme)]):
    """
    FastAPI dependency that decodes a JWT token and returns the user payload.

    This function is used to get the raw user information embedded in the token
    without querying the database.

    Args:
        token (str): The OAuth2 bearer token from the request's Authorization header.

    Raises:
        HTTPException: 401 Unauthorized if the token is invalid or expired.

    Returns:
        dict: The payload of the decoded JWT token.
    """
    user_from_token = decode_jwt(token)
    if not user_from_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user_from_token


async def get_current_user_account(token: Annotated[str, Depends(oauth2_scheme)]) -> UserAccount:
    """
    FastAPI dependency that decodes a JWT token and retrieves the user from the database.

    This is the primary dependency for getting the authenticated user model instance.

    Args:
        token (str): The OAuth2 bearer token from the request's Authorization header.

    Raises:
        HTTPException: 401 Unauthorized if the token is invalid or expired.

    Returns:
        UserAccount: The user object from the database. `UserAccount` is likely a
                     type alias for the `User` model.
    """
    user_from_token = decode_jwt(token)
    if not user_from_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    # Fetch the user from the database using the user_id from the token.
    db_user = crud_user.get_by_user_id(db=db, usr_id=user_from_token.get("user_id"))
    return db_user


async def get_current_active_user(
        current_user: UserAccount = Depends(get_current_user_account),
) -> UserAccount:
    """
    FastAPI dependency to get the current active user.

    This depends on `get_current_user_account` and adds a check to ensure the
    user is active.

    Args:
        current_user (UserAccount): The user account object, injected by FastAPI
                                    from the `get_current_user_account` dependency.

    Raises:
        HTTPException: 400 Bad Request if the user is inactive.

    Returns:
        UserAccount: The active user object.
    """
    if not crud_user.is_active(current_user):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_active_superuser(
        current_user: UserAccount = Depends(get_current_user_account),
) -> UserAccount:
    """
    FastAPI dependency to get the current active superuser.

    This depends on `get_current_user_account` and adds a check to ensure the
    user has superuser privileges.

    Args:
        current_user (UserAccount): The user account object, injected by FastAPI
                                    from the `get_current_user_account` dependency.

    Raises:
        HTTPException: 400 Bad Request if the user is not a superuser.

    Returns:
        UserAccount: The active superuser object.
    """
    if not crud_user.is_superuser(current_user):
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user
