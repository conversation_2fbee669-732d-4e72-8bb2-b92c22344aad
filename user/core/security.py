from schema.user_schema import SignUser
from schema.token_schema import Token

from typing import Any
import jwt
import secrets
import time
import bcrypt


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verifies a plain-text password against a bcrypt hashed password."""
    return bcrypt.checkpw(plain_password.encode("utf-8"), hashed_password.encode("utf-8"))


def hash_password(password: str) -> str:
    """Hashes a password using bcrypt with a randomly generated salt."""
    # The password must be encoded to bytes before hashing.
    password = password.encode('utf-8')
    return bcrypt.hashpw(password, bcrypt.gensalt())


def random_password(password_length=8):
    """Generates a random URL-safe text string."""
    return secrets.token_urlsafe(password_length)
