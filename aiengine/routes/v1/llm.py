from database.database import get_session

from database.models import Model
from schema.input_message_schema import ModelCreate, ModelUpdate

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import APIRouter, Header, Depends
from fastapi import HTTPException, status
from fastapi import status
from sqlalchemy.future import select
from datetime import datetime


# Router for model management endpoints
router = APIRouter(
    prefix = "/api",
    tags=['Model Management'],
    responses={404: {'description': 'Not found'}},
)


@router.post("/model/new", status_code=status.HTTP_200_OK)
async def create_new_model(
    model_infor: ModelUpdate,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Create a new LLM model in the database.
    
    Args:
        model_infor (ModelUpdate): Model information including ID, name, and configuration
        request_user_id (str): User ID from request header for authentication
        db (AsyncSession): Database session dependency
        
    Returns:
        dict: Status message confirming model creation
    """
    model_to_create = Model(
        model_id = model_infor.model_id,
        model_name = model_infor.model_name,
        display_name = model_infor.display_name,
        model_type = model_infor.model_type,
        provider = model_infor.provider,
        reference = model_infor.reference,
        max_token = model_infor.max_token,
        price_in = model_infor.price_in,
        price_out = model_infor.price_out,
    )
    db.add(model_to_create)
    await db.commit()

    return {'Status': f'Successfully created new model {model_infor.model_name}'}


@router.get("/model/{model_id}", status_code=status.HTTP_200_OK)
async def get_model_information(
    model_id: int,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Retrieve information about a specific model by ID.
    
    Args:
        model_id (int): Unique identifier of the model to retrieve
        request_user_id (str): User ID from request header for authentication
        db (AsyncSession): Database session dependency
        
    Returns:
        Model: The requested model information
    """
    model = await db.execute(select(Model).where(Model.model_id == model_id))
    return model.scalar()


@router.get("/model", status_code=status.HTTP_200_OK)
async def get_all_model(
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Retrieve all available models from the database.
    
    Args:
        request_user_id (str): User ID from request header for authentication
        db (AsyncSession): Database session dependency
        
    Returns:
        List[Model]: List of all models ordered by provider
    """
    models = await db.execute(
        select(
            Model
        ).order_by(
            Model.provider.desc()
        )
    )
    return models.scalars().all()


@router.put("/model/edit", status_code=status.HTTP_200_OK)
async def edit_model_information(
    model_infor: ModelUpdate,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Update an existing model's information.
    
    Args:
        model_infor (ModelUpdate): Updated model information
        request_user_id (str): User ID from request header for authentication
        db (AsyncSession): Database session dependency
        
    Returns:
        dict: Status message confirming model update
        
    Raises:
        HTTPException: 404 error if model not found
    """
    model_to_update = await db.execute(select(Model).where(Model.model_id == model_infor.model_id))
    model_to_update = model_to_update.scalar()

    if model_to_update is not None:
        model_to_update.model_name = model_infor.model_name
        model_to_update.display_name = model_infor.display_name
        model_to_update.model_type = model_infor.model_type
        model_to_update.provider = model_infor.provider
        model_to_update.reference = model_infor.reference
        model_to_update.max_token = model_infor.max_token
        model_to_update.price_in = model_infor.price_in
        model_to_update.price_out = model_infor.price_out
        model_to_update.updated_at = datetime.now()
        db.add(model_to_update)
        await db.commit()

    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='Model not found'
        )
    
    return {'Status': f'Successfully updeted model {model_infor.model_id}'}


@router.delete("/model/{model_id}", status_code=status.HTTP_200_OK)
async def delete_model_by_id(
    model_id: int,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Delete a model from the database by its ID.
    
    Args:
        model_id (int): Unique identifier of the model to delete
        request_user_id (str): User ID from request header for authentication
        db (AsyncSession): Database session dependency
        
    Returns:
        dict: Status message confirming model deletion
        
    Raises:
        HTTPException: 404 error if model not found
    """
    model_to_delete = await db.execute(select(Model).where(Model.model_id == model_id))
    model_to_delete = model_to_delete.scalar()
    if model_to_delete is not None:
        await db.delete(model_to_delete)
        await db.commit()
        
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail='Model not found'
        )
    return {'Status': f'Successfully deleted model {model_id}'}


model_routes = router
