import json
from io import StringIO, Bytes<PERSON>
from pydantic import BaseModel

from procedures.utils import extract_entities_from_file
from schema.extraction_schema.mortgage_transaction import MortgageTransactionHistory, PaymentYearList

from core.config import settings

def mgt_payment_year_extraction(file_content: BytesIO):
    instruction_payment_year_list = """You are an advanced financial document analysis system specializing in UK mortgage payment history documents. Your core objective is to meticulously extract all specified details from these documents, regardless of their varying layouts or presentation formats, to support the auditing and analysis of mortgage accounts.
    ** Your task is to accurately and comprehensively extract the information about the year of the payment in each payment transaction history document and output them as a JSON object, strictly adhering to the `PaymentYearList` Pydantic schema. The mortgage document may include many small documents which the term of year was in many year. So we need to extract the list of year for mortgage management by year.**
    ** Instructions for Output:**
    1. **JSON Format:** Your final output *must* be a single, valid JSON object.
    2. **Schema Compliance:** Adhere strictly to the `PaymentYearList` Pydantic schema for field names, data types, and nesting.
    3. **Data Transformation: When encounter the date, only to select the year part and convert it into string format** 
    4. **Accuracy and Precision:** Ensure that all extracted values are as accurate and precise as indicated in the source document.
    5. **No Extraneous Content:** Your response must contain *only* the JSON object. Do not include any introductory text, conversational remarks, or markdown outside the JSON block.
    """    
    return extract_entities_from_file(
        instruction=instruction_payment_year_list, 
        response_schema=PaymentYearList, 
        file_content=file_content
    )


def mortgage_transaction_extraction_by_year(year: int, file_content: BytesIO):
    pdf_bytes = file_content.getvalue()

    instruction_payment_history = """You are an advanced financial document analysis system specializing in UK mortgage transaction history documents. Your core objective is to meticulously extract all specified details from these documents, regardless of their varying layouts or presentation formats, to support the auditing and analysis of mortgage accounts.

    **Your task is to accurately and comprehensively extract all relevant entities from the provided mortgage transaction within the year history document and output them as a JSON object, strictly adhering to the `MortgageTransactionHistory` Pydantic schema.**

    **Key Principles for Extraction:**

    1.  **Semantic Focus:** Understand that different lenders and document versions will have diverse layouts and phrasing. Your goal is to identify the *semantic meaning* of the information corresponding to the fields in the pre-defined JSON schema, rather than relying on exact wording or visual placement. Extract the underlying data even if it's expressed differently or located in unexpected sections.

    2.  **Crucial Information Categories:** Prioritize and diligently search for information within these essential categories:
        * **Document-Level Information:** Extract the `account_holder_name` and the `statement_report_date` if they apply to the entire document rather than a single account.
        * **Account-Specific Transaction Records:** The document may contain transaction histories for multiple distinct mortgage accounts. For each unique mortgage account identified, create a separate `AccountTransactionRecord` object and add it to the `account_records` list.
            * For each `AccountTransactionRecord`:
                * Identify the `mortgage_account_number`.
                * Extract all associated `transactions` for that specific account into a list of `TransactionDetail` objects.
                * For each `TransactionDetail`:
                    * `effective_date`: The date the transaction became active.
                    * `post_date`: The date the transaction was recorded (if different from effective date).
                    * `description`: The textual explanation of the transaction (e.g., 'ADV', 'INT', 'MRP', 'DDP', 'Capitalised Interest', 'Repayment from Nationwide Account').
                    * `debit_amount`: Amounts charged to the account.
                    * `credit_amount`: Amounts paid or credited to the account.

    **Instructions for Output:**

    1.  **JSON Format:** Your final output *must* be a single, valid JSON object.
    2.  **Schema Compliance:** Adhere strictly to the `MortgageTransactionHistory` Pydantic schema for field names, data types, and nesting.
    3.  **Handling Missing or Not Available Data:**
        * For any field that is `Optional` in the schema and whose corresponding information is **not found or explicitly stated as not applicable** in the document, you *must* set its value to `null` in the JSON output. Do not omit the field.
        * For fields marked as `required` (e.g., `mortgage_account_number`, `description`, `balance` within `TransactionDetail`), you *must* strive to find and extract a valid value. If a required field is genuinely unidentifiable or unequivocally absent, you should indicate this by providing a placeholder value like `0` (for numerical fields) or an empty string `""` (for string fields).
    4.  **Data Transformation:**
        * **Dates:** Convert all dates to the `YYYY-MM-DD` string format (e.g., "20/04/00" becomes "2000-04-20").
        * **Monetary Values:** Extract all monetary values as pure `float` numbers, strictly without currency symbols or thousands separators (e.g., "£150.75" becomes `150.75`, "1,234.56" becomes `1234.56`).
        * **Booleans:** Convert linguistic indicators (e.g., "Yes", "No", "Applies", "Not Applicable", "True", "False") to standard JSON booleans (`true` or `false`).
        * **Lists:** Ensure `account_records` and `transactions` are correctly represented as JSON arrays (`[]`). If no accounts are found, `account_records` should be an empty list.
    5.  **Accuracy and Precision:** Ensure that all extracted values are as accurate and precise as indicated in the source document.
    6.  **No Extraneous Content:** Your response must contain *only* the JSON object. Do not include any introductory text, conversational remarks, or markdown outside the JSON block."""

    final_instruction = instruction_payment_history + f"""

    **Critical Year Constraint:**
    You must strictly extract only transactions that fall within the year {year}. Exclude all transactions outside of {year}. 
    If a transaction has both an `effective_date` and `post_date`, use `effective_date` for filtering. If only `post_date` exists, use that.
    Transactions with no identifiable date must be excluded.

    Ensure your output JSON contains only transactions from {year}.
    """        

    result = extract_entities_from_file(
        instruction=final_instruction, 
        response_schema=MortgageTransactionHistory, 
        file_content=BytesIO(pdf_bytes)
    )

    return result