from core.config import settings

from procedures.mortgage_extraction.mortgage_offer_extraction import mortgage_offer_extraction, sub_accounts_extraction
from procedures.mortgage_extraction.mortgage_statement_extraction import mortgage_statement_extraction
from procedures.mortgage_extraction.mortgage_transactions_extraction import mgt_payment_year_extraction, mortgage_transaction_extraction_by_year
from procedures.utils import extract_entities_from_file

from celery_app.tasks import extract_mgt_transaction_years, extract_mortgage_offer_task
from celery.result import AsyncResult
from celery_app.celery_setup import celery_app

from utils.file_handling import merge_upload_files
from schema.extraction_schema.mortgage_offer import MortgageOffer

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from typing import List
import uuid
import pandas as pd
from io import StringIO, BytesIO
import requests
from loguru import logger
import asyncio
import base64

router = APIRouter(
    prefix = "/api",
    tags=['Document Extraction Management'],
    responses={404: {'description': 'Not found'}},
)

@router.post("/document/mortgage-sub-accounts/extract")
async def extract_mortgage_sub_accounts(
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        result = sub_accounts_extraction(file_obj)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage sub-accounts: {str(e)}"
        )

@router.post("/document/mortgage-offer/extract", response_model=MortgageOffer)
async def extract_mortgage_offer(
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        mortgage_offer_info = mortgage_offer_extraction(file_obj)
        result = MortgageOffer(**mortgage_offer_info)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage offer: {str(e)}"
        )
    
@router.post("/document/mortgage-offer/extract/test")
async def extract_mortgage_offer(
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        file_bytes = file_obj.getvalue()
        file_base64 = base64.b64encode(file_bytes).decode("utf-8")
        task = extract_mortgage_offer_task.delay(file_base64)        
        return {"task_id": task.id}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage offer: {str(e)}"
        )    

@router.post("/document/mortgage-transaction/year-list/extract")
async def extract_mortgage_transaction_year_list(
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        result = mgt_payment_year_extraction(file_obj)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage transaction year list: {str(e)}"
        )

@router.post("/document/mortgage-transaction/year-list/extract/test")
async def extract_mortgage_transaction_year_list(
    uploaded_files: List[UploadFile] = File(...)
):
    try:
        file_obj = await merge_upload_files(uploaded_files)
        file_bytes = file_obj.getvalue()
        file_base64 = base64.b64encode(file_bytes).decode("utf-8")

        # Send the base64-encoded data to Celery
        task = extract_mgt_transaction_years.delay(file_base64)        
        return {"task_id": task.id}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage transaction year list: {str(e)}"
        )        

@router.post("/document/mortgage-transaction/extract")
async def extract_mortgage_transaction_by_year(
    year: int = Query(..., description="The year to extract transactions for"),
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        mortgage_transaction_info = mortgage_transaction_extraction_by_year(
            year=year, 
            file_content=file_obj
        )
        return mortgage_transaction_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage transaction: {str(e)}"
        )

@router.post("/document/mortgage-statement/extract")
async def extract_mortgage_statement(
    uploaded_files: List[UploadFile] = File(...)
):
    file_obj = await merge_upload_files(uploaded_files)
    try:
        mortgage_statement_info = mortgage_statement_extraction(file_obj)
        return mortgage_statement_info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error extracting mortgage statement: {str(e)}"
        )

@router.post("/document/test-pdf-upload")
async def test_pdf_upload(
    uploaded_files: List[UploadFile] = File(...)
):
    """
    Test endpoint for multiple PDF file uploads.
    Reads the content of each file and returns basic information about them.
    """
    file_infos = []

    try:
        for uploaded_file in uploaded_files:
            # Read the binary content
            contents = await uploaded_file.read()

            # Get file information
            file_info = {
                "filename": uploaded_file.filename,
                "content_type": uploaded_file.content_type,
                "file_size_bytes": len(contents),
                "is_pdf": uploaded_file.content_type == "application/pdf" or 
                          (uploaded_file.filename and uploaded_file.filename.lower().endswith('.pdf'))
            }

            # Detect PDF header
            is_pdf_by_header = contents[:5].startswith(b'%PDF-')
            file_info["is_pdf_by_header"] = is_pdf_by_header

            file_infos.append(file_info)

        # Return result
        return {
            "status": "success",
            "message": f"{len(uploaded_files)} file(s) uploaded and read successfully",
            "files_info": file_infos
        }

    except Exception as e:
        logger.error(f"Error processing PDF uploads: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing files: {str(e)}"
        )

@router.get("/task/{task_id}/result")
async def get_task_result(task_id: str):
    """Get the result of a Celery task by task ID"""
    try:
        result = AsyncResult(task_id, app=celery_app)
        
        if result.ready():
            if result.successful():
                return {
                    "status": "SUCCESS",
                    "result": result.result
                }
            else:
                return {
                    "status": "FAILURE", 
                    "error": str(result.result)
                }
        else:
            return {
                "status": "PENDING",
                "message": "Task is still processing"
            }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving task result: {str(e)}"
        )

entity_extraction_routes = router