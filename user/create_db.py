from database.database import POSTGRES_DATABASEURL, engine, SessionFactory
from database.models import User, SystemRole, role_permission
from core.security import hash_password
from utils.migrate import run_migration
from sqlalchemy_utils import database_exists, create_database
from loguru import logger
import pandas as pd
import os


def init_data() -> None:
    """
    Initializes system data by reading from CSV files and inserting into the database.
    This function populates tables like 'system_role' and 'email_confirmation_status'
    with predefined data necessary for the application to run correctly.
    """
    session = SessionFactory()
    # Construct the path to the directory containing system data CSVs.
    init_data_path = os.path.join(os.path.dirname(__file__), "database/system_data")
    files = [
        "system_role.csv",
        "email_confirmation_status.csv",
    ]
    # Iterate over the list of CSV files to be loaded.
    for file in files:
        file_path = os.path.join(init_data_path, file)
        df = pd.read_csv(file_path, sep=",")
        logger.info(f"{file}  load successed")
        table_name = file.replace(".csv", "")
        # Use pandas to_sql to append data to the corresponding table.
        df.to_sql(table_name, engine, if_exists="append", index=False)

    session.commit()
    session.close()

def init_user() -> None:
    """
    Creates an initial admin user with a default password and assigns the admin role.
    This is useful for setting up the application for the first time.
    """
    session = SessionFactory()
    # Hash the default password for the admin user.
    hashed_password = hash_password('12345678')
    # Create a new User object for the admin.
    new_user = User(
        email = '<EMAIL>',
        password = hashed_password.decode("utf-8"),
    )

    # Query for the 'admin' system role (assuming role_id 2 is the admin role).
    user_role_id = session.query(
        SystemRole
    ).filter(
        SystemRole.role_id == 2
    ).first()
    
    # Assign the admin role to the new user.
    new_user.system_role.append(user_role_id)
    session.add(new_user)
    session.commit()
    session.close()


if __name__=='__main__':
    # This script is intended to be run directly to set up the database.
    # Check if the database exists, and create it if it doesn't.
    if not database_exists(POSTGRES_DATABASEURL):
        create_database(POSTGRES_DATABASEURL)
    # Run Alembic migrations to ensure the database schema is up to date.
    run_migration()
    try:
        # Wrap data initialization in a try-except block to handle potential errors,
        # such as data already existing.
        logger.info('Init role data......')
        init_data()
        logger.success('Done!')

        logger.info('Init admin user...')
        init_user()
        logger.success('Done!')
    except Exception as e:
        # Log any exceptions that occur during data initialization.
        logger.error(f'{e}')
        pass