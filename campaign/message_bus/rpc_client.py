import asyncio
import uuid
import logging
from typing import Dict
from aio_pika import Message, connect, connect_robust
from aio_pika.abc import AbstractIncomingMessage, AbstractQueue
import json

from core.config import settings

logger = logging.getLogger(__name__)

class RPCClient:
    def __init__(self):
        self.futures: Dict[str, asyncio.Future] = {}

    async def connect(self) -> "RPCClient":
        """Establish connection with RabbitMQ and declare a response queue."""
        self.connection = await connect_robust(
            f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"
        )
        self.channel = await self.connection.channel()
        
        # Declare a temporary queue for receiving responses
        self.callback_queue: AbstractQueue = await self.channel.declare_queue(exclusive=True)
        await self.callback_queue.consume(self.on_response, no_ack=True)

        return self

    async def on_response(self, message: AbstractIncomingMessage) -> None:
        """Handles incoming RPC responses."""
        if message.correlation_id is None:
            logger.error(f"Received message without correlation_id: {message!r}")
            return

        future: asyncio.Future = self.futures.pop(message.correlation_id)
        future.set_result(message.body)
        
    async def call(self, service: str, function_name: str, params: dict) -> dict:
        """Sends an RPC request and waits for a response."""
        correlation_id = str(uuid.uuid4())
        loop = asyncio.get_running_loop()
        future = loop.create_future()
        self.futures[correlation_id] = future

        request_body = {
            "function": function_name,
            "params": params
        }

        self.exchange = await self.channel.declare_exchange("rpc_exchange", type="direct", durable=True)
        await self.callback_queue.bind(self.exchange, routing_key=self.callback_queue.name)
        await self.exchange.publish(
            Message(
                body=json.dumps(request_body).encode(),
                correlation_id=correlation_id,
                reply_to=self.callback_queue.name,  # Specify where the response should go
            ),
            routing_key=service,
        )
        # logger.info(f"[RPC CLIENT] Sent rpc request {request_body}")
        response_body = await future  # Wait for response
        return response_body.decode()
