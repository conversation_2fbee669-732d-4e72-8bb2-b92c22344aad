// App.js
import React, { useState, useEffect } from 'react';
import { MdArrowBack, MdArrowForward } from 'react-icons/md'; 
import { FaStar, FaTwitter  } from 'react-icons/fa'
import { BrowserRouter as Router, Route, Routes, useNavigate } from 'react-router-dom';
import Login from './component/loginPage/Login';
import Signup from './component/loginPage/Signup';
import ForgotPassword from './component/loginPage/ForgotPassword';
import ResetPassword from './component/loginPage/ResetPassword';
import BillingPage from './component/loginPage/BillingPage';

import { AuthProvider } from './authorize/AuthContext';
import ProtectedRoute from './authorize/ProtectedRoute';
import { DataProvider } from './authorize/DataContext';

import ClassifyCustomer from './component/onboardingPage/OnboardingSelect'; //File not used
import OnboardingInputUserData from './component/onboardingPage/OnboardingUserData';
import CampaignInitialize from './component/onboardingPage/OnboardingSelectDataFill'; //File not used
import CampaignInitialize2 from './component/onboardingPage/OnboardingSelectDataFill2';
import CampManualFill from './component/onboardingPage/OnboardingCampManualFill';
// import UserAIChatUI from './component/onboardingPage/OnboardingAIChat';
import OnboardingSelectPlan from './component/onboardingPage/OnboardingSelectPlan'; //File not used
import OnboardingReviewCampaign from './component/onboardingPage/OnboardingReviewCampaign';
import OnboardingCampaignMatching from './component/onboardingPage/OnboardingMatching';
import UserAIChatUI from './component/onboardingPage/OnboardingAIChat3';

import Dashboard from './component/managingPage/Dashboard';
import MyCampaign from './component/managingPage/MyCampaign';
import NewCampaign from './component/managingPage/createCampaign';
import UpdateCampaign from './component/managingPage/updateCampaign';
import AddMatchingCompany from './component/managingPage/addMatchingCompany';

import AnalyzingUser from './component/mailPage/AnalyzingUser'; //File not used
import MailboxData from './component/mailPage/Mailbox'; //File not used
// import EmailStatus from './component/mailPage/emailStatus';
import EmailStatus from './component/mailPage/emailStatus2';
import MailReplyPage from './component/mailPage/MailReplyPage'; //File not used
// import ViewInsight from './component/mailPage/ViewInsight';
import ViewInsight from './component/mailPage/ViewInsight2';
import Unibox from './component/mailPage/Unibox';

import CompletePayment from './component/banking/completePayment';

import bannerImg from './assets/img/banner-img.png';
import virtualAssistant from './assets/img/virtual-assistant-300x300.png';
import newsletter from './assets/img/newsletter-300x300.png';
import multiChannel from './assets/img/multi-channel-300x300.png';
import bell from './assets/img/bell-300x300.png';
import solveImg from './assets/img/solve-img.png';
import chatAI from './assets/img/chat-ai-150x150.png';
import insight from './assets/img/insight-150x150.png';
import chat from './assets/img/chat-150x150.png';
import email from './assets/img/email-150x150.png';
import userImg2 from './assets/img/user-img-2.png';
import userImg3 from './assets/img/user-img-3.png';
import userImg4 from './assets/img/user-img-4.png';
import userImg5 from './assets/img/user-img-5.png';
import userImg6 from './assets/img/user-img-6.png';
import Logo from './assets/img/roboman-logo.png';

import { BiMap, BiPhoneCall, BiEnvelope } from 'react-icons/bi';
import Account from './component/account/account';
import OAuthCallback from './component/loginPage/OAuthCallback';

// import AOS from 'aos';
// import 'aos/dist/aos.css';

import AOS from 'aos';
import 'aos/dist/aos.css';

const LandingPage = () => {

  useEffect(() => {
    AOS.init();
  }, [])

  const testimonials = [
    {
      text:
        "Roboman.ai has transformed how we handle digital outreach. The ability to set campaign goals through a conversational AI interface saved us hours of manual setup. It analyzed our target audience using buyer intelligence and tailored emails and LinkedIn messages that actually resonated. Our response rates have doubled since we started using it—highly recommend this platform",
      author: 'Tomas Campbell',
    },
    {
      text:
        "As a small business owner, I needed a tool to connect with prospects effectively, and Roboman.ai delivered. The platform’s use of personality models like DISC and Big Five to customize messaging is a standout feature. It’s automated our outreach across multiple channels while keeping interactions personal and relevant. Setup was effortless, and the results speak for themselves—great tool",
      author: 'Adam Augustyn',
    },
    {
      text:
        "Roboman.ai takes outreach to the next level with its buyer intelligence and AI automation. We used it to target key accounts, and the platform’s psycholinguistic analysis helped craft messages that matched each prospect’s behavior and preferences. It’s like having a marketing team that never sleeps. Efficiency is up, and our engagement metrics are stronger than ever—worth checking out",
      author: 'Finley Stewart',
    },
    {
      text:
        "In a free hour, when our power of choice is untrammelled and when nothing prevents our being what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business",
      author: 'Fergus Douchebag',
    },
    {
      text:
        "In a free hour, when our power of choice is untrammelled and when nothing prevents our being what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business",
      author: 'Fergus Douchebag',
    },
    {
      text:
        "In a free hour, when our power of choice is untrammelled and when nothing prevents our being what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business",
      author: 'Fergus Douchebag',
    },
    {
      text:
        "In a free hour, when our power of choice is untrammelled and when nothing prevents our being what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business",
      author: 'Fergus Douchebag',
    },
    {
      text:
        "There are many variations of passage available, but the majority have suffered words which don't look even slightly believable.",
      author: 'Fergus Douchebag',
    },
  ];

  const userImages = [userImg2, userImg3, userImg4, userImg5, userImg6, userImg3, userImg2, userImg3];
  const navigate = useNavigate();

  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);

  const goToPrevious = () => {
    setCurrentTestimonialIndex((prevIndex) => (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1));
  };

  const goToNext = () => {
    setCurrentTestimonialIndex((prevIndex) => (prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1));
  };

  const currentTestimonial = testimonials[currentTestimonialIndex];
  const currentUserImage = userImages[currentTestimonialIndex];

  const handleButtonClick = () => {
    navigate('/login');
  };

  return (
    <div className="bg-black min-h-screen flex flex-col ">
      <header style={{ width: '100%', height: '7%' }} className="bg-white shadow-md fixed top-0 left-0 right-0 flex justify-between items-center px-5 py-4 z-50">
        <img 
          src={Logo} 
          alt="Brand Logo" 
          className="w-36 h-12 object-contain"
        />
        <button
          onClick={handleButtonClick}
          className="w-28 h-10 bg-blue-500 text-lg text-white font-semibold rounded-full cursor-pointer text-base hover:bg-blue-600 transition duration-300 ease-in-out"
        >
          Login
        </button>
      </header>

      <div className="min-h-screen flex flex-col items-center justify-center" style={{ backgroundColor: "transparent" }}>
        {/* Hero Section */}
        <div className="container mx-auto px-4 flex flex-wrap lg:flex-nowrap items-center text-center lg:text-left w-[70%]" style={{ backgroundColor: "rgba(255, 255, 255, 0)", borderRadius: "8px" }}>
          {/* Left Part */}
          <div className="w-full lg:w-1/2 text-white mb-6 md:mb-0 md:py-5">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4" data-aos="fade-up">
              Revolutionise Your Outreach with AI-Driven Conversations
            </h1>
            <p className="text-base md:text-lg mb-6" data-aos="fade-up">
              Effortless engagement, hyper-personalized messaging, and measurable results—all powered by Roboman.
            </p>
            <button
              onClick={handleButtonClick}
              className="bg-blue-500 hover:bg-blue-600 text-white text-sm md:text-lg font-semibold py-2 px-4 md:py-3 md:px-6 rounded-full transition duration-300 ease-in-out"
              data-aos="fade-up"
            >
              Get Started →
            </button>
          </div>

          {/* Right Part */}
          <div className="w-full lg:w-1/2 flex justify-center">
            <img
              src={bannerImg}
              alt="AI Banner"
              className="max-w-full h-auto opacity-90"
              data-aos="fade-up"
            />
          </div>
        </div>
      </div>

      {/* Game-Changer Section */}
      <section className=" mx-auto py-10 w-[70%]" style={{ backgroundColor: 'transparent' }}>
        {/* Grid Container */}
        <div className="grid grid-cols-1 gap-8">
          {/* Title and Description */}
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-5xl font-bold text-white mb-5" data-aos="fade-up">🔥 Why Roboman is a Game-Changer</h2>
            <p className="text-xl text-gray-300" data-aos="fade-up">
              Graphic Design Is The Profession And Academic Discipline Whose Activity Consists In Projecting.
            </p>
          </div>

          {/* Cards Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Card 1 */}
            <div className="card green-border p-6 text-center bg-black border-2 border-green-500 rounded-lg" data-aos="fade-up">
              <img src={virtualAssistant} alt="Messaging Icon" className="mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Hyper-Personalized Messaging</h3>
              <p className="text-gray-300">
                Leverages DISC & OCEAN personality models for tailored outreach.
              </p>
            </div>

            {/* Card 2 */}
            <div className="card yellow-border p-6 text-center bg-black border-2 border-yellow-500 rounded-lg" data-aos="fade-up">
              <img src={newsletter} alt="Messaging Icon" className="mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Conversational AI Onboarding</h3>
              <p className="text-gray-300">
                Simply talk to our AI Anna to set up campaigns. No complex data entry required.
              </p>
            </div>

            {/* Card 3 */}
            <div className="card red-border p-6 text-center bg-black border-2 border-red-500 rounded-lg" data-aos="fade-up">
              <img src={multiChannel} alt="Messaging Icon" className="mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Real-Time Engagement Alerts</h3>
              <p className="text-gray-300">
                Get notified only when genuine interest arises, saving your time.
              </p>
            </div>

            {/* Card 4 */}
            <div className="card blue-border p-6 text-center bg-black border-2 border-blue-500 rounded-lg" data-aos="fade-up">
              <img src={bell} alt="Messaging Icon" className="mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">Multichannel Mastery</h3>
              <p className="text-gray-300">
                Reach prospects via email, <strong>LinkedIn</strong>, <strong>SMS</strong>, and more.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* The Problem We Solve Section */}
      <div className="py-10 flex items-center justify-center" data-aos="fade-up">
        <div className=" mx-auto" style={{ backgroundColor: 'transparent' }}>
          <div className="flex flex-col lg:flex-row gap-8 items-center">
            {/* Image Column */}
            <div className="w-full lg:w-5/12" >
              <img src={solveImg} className="w-full rounded-lg shadow-lg" />
            </div>

            {/* Text Column */}
            <div className="w-full lg:w-7/12">
              <div className="flex items-center flex-wrap gap-4 mb-4">
                <div className="w-20 h-[2px] bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <span className="block font-medium text-white text-xl">
                  The Problem We Solve
                </span>
              </div>
              <h2 className="text-white text-5xl font-normal mb-4">
                Outreach is Broken. Roboman Fixes It.
              </h2>
              <p className="text-gray-300 mb-8 text-lg font-semibold">
                Common Pain Points of Traditional Outreach:
              </p>
              <ul className="list-none pl-0" data-cues="fadeIn">
                <li className="flex items-center gap-4 mb-6">
                  <span className="grid place-content-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm">
                    <i className="bi bi-arrow-up-right"></i>
                  </span>
                  <span className="block flex-grow font-normal text-gray-300 text-lg font-semibold">Complex & Hard to Use</span>
                </li>
                <li className="flex items-center gap-4 mb-6">
                  <span className="grid place-content-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm">
                    <i className="bi bi-arrow-up-right"></i>
                  </span>
                  <span className="block flex-grow font-normal text-gray-300 text-lg font-semibold">Requires Manual Effort & Expertise</span>
                </li>
                <li className="flex items-center gap-4 mb-6">
                  <span className="grid place-content-center w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm">
                    <i className="bi bi-arrow-up-right"></i>
                  </span>
                  <span className="block flex-grow font-normal text-gray-300 text-lg font-semibold">Low Response Rates & Inefficiencies</span>
                </li>
              </ul>
              <p className="text-gray-300 mt-5 mb-0 text-lg font-semibold">
                Roboman Solves These Issues with AI-Powered Automation & Buyer Intelligence.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* How It Works */}
      <div className="py-10 " data-aos="fade-up">
        <div className="container mx-auto w-[70%]" style={{ backgroundColor: 'transparent' }} >
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8">
            {/* Title and Description */}
            <div className="mb-12 text-center">
              <div className="flex justify-center items-center flex-wrap gap-4 mb-2">
                <div className="w-10 h-[2px] bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <span className="font-medium text-white text-xl">How It Works</span>
              </div>
              <h2 className="text-white text-5xl font-normal">From Goals to Results in Just a Few Steps</h2>
            </div>

            {/* Cards Section */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Card 1 */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-lg p-6 text-white">
                <div className="mb-4 text-blue-400 text-4xl">
                  <i className="bi bi-people"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">Talk to our AI</h3>
                <p className="text-gray-400 mb-4">Define goals & target audience.</p>
                <button
                  onClick={handleButtonClick}
                  href="#"
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 2 */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-lg p-6 text-white">
                <div className="mb-4 text-blue-400 text-4xl">
                  <i className="bi bi-translate"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">AI-Driven Outreach</h3>
                <p className="text-gray-400 mb-4">Automatically reaches prospects with personalized messaging.</p>
                <button
                  onClick={handleButtonClick}
                  href="#"
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 3 */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-lg p-6 text-white">
                <div className="mb-4 text-blue-400 text-4xl">
                  <i className="bi bi-gear-wide-connected"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">Engagement Alerts</h3>
                <p className="text-gray-400 mb-4">Get notified when a lead is interested.</p>
                <button
                  onClick={handleButtonClick}
                  href="#"
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 4 */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-lg p-6 text-white">
                <div className="mb-4 text-blue-400 text-4xl">
                  <i className="bi bi-gear-wide-connected"></i>
                </div>
                <h3 className="text-xl font-semibold mb-2">Actionable Insights</h3>
                <p className="text-gray-400 mb-4">AI analytics refine your strategy for better results.</p>
                <button
                  onClick={handleButtonClick}
                  href="#"
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Our AI Technology */}
      <div className="py-10" data-aos="fade-up">
        <div className="container mx-auto w-[70%]" style={{ backgroundColor: 'transparent' }} >
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8">
            {/* Section Title and Description */}
            <div className="mb-8">
              <div className="flex justify-center items-center flex-wrap gap-4 mb-4">
                <div className="w-10 h-[2px] bg-gradient-to-r from-blue-500 to-purple-500 "></div>
                <span className="font-medium text-white text-xl ">Our AI Technology</span>
              </div>
              <h2 className="text-white text-5xl font-normal text-center mt-5">Powered by Cutting-Edge AI & Buyer Intelligence</h2>
            </div>

            {/* AI Features Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Card 1: AI-Driven Conversations */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-2xl p-6 text-white w-[250px] h-[250px]">
                <h3 className="text-xl font-semibold mb-2 h-[60px]">AI-Driven Conversations</h3>
                <p className="text-gray-400 mb-4 h-[80px]">No more rigid templates, just natural, human-like interactions.</p>
                <button
                  onClick={handleButtonClick}
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 2: Hyper-Personalized Messaging */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-2xl p-6 text-white w-[250px] h-[250px]">
                <h3 className="text-xl font-semibold mb-2 h-[60px]">Hyper-Personalized Messaging</h3>
                <p className="text-gray-400 mb-4 h-[80px]">Uses DISC & OCEAN personality models.</p>
                <button
                  onClick={handleButtonClick}
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 3: Adaptive Outreach */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-2xl p-6 text-white w-[250px] h-[250px]">
                <h3 className="text-xl font-semibold mb-2 h-[60px]">Adaptive Outreach</h3>
                <p className="text-gray-400 mb-4 h-[80px]">AI learns from interactions and refines strategies automatically.</p>
                <button
                  onClick={handleButtonClick}
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>

              {/* Card 4: Complete Automation */}
              <div className="bg-gray-900 hover:bg-[#2E3B55] rounded-2xl p-6 text-white w-[250px] h-[250px]">
                <h3 className="text-xl font-semibold mb-2 h-[60px]">Complete Automation</h3>
                <p className="text-gray-400 mb-4 h-[80px]">AI handles everything, from outreach to follow-ups.</p>
                <button
                  onClick={handleButtonClick}
                  className="group border border-white rounded-full py-2 px-4 inline-flex items-center transition-colors duration-200 hover:bg-green-500 hover:text-white"
                >
                  <span>Get Started</span>
                  <span className="text-sm ml-2 group-hover:rotate-45 transition-transform duration-200">→</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* AI-Powered Solutions */}
      <div className="py-10" data-aos="fade-up">
        <div className="container mx-auto" style={{ backgroundColor: 'transparent' }}>
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8">
            {/* Title */}
            <div className="mb-12">
              <h2 className="text-3xl font-normal text-white">
                Explore Roboman’s Suite of AI-Powered Solutions
              </h2>
            </div>

            {/* Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Card 1 */}
              <div className="bg-[#1B253A] rounded-2xl p-6">
                <img src={chatAI} alt="Linked Pod" className="mb-4 w-16 h-16" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  Linked Pod – AI-Powered LinkedIn Engagement
                </h3>
                <ul className="list-none pl-0 text-gray-400">
                  <li className="mb-1">Automatically comments on LinkedIn profiles.</li>
                  <li className="mb-1">Boosts profile visits & engagement.</li>
                  <li>Turns connections into conversations effortlessly.</li>
                </ul>
              </div>

              {/* Card 2 */}
              <div className="bg-[#1B253A] rounded-2xl p-6">
                <img src={insight} alt="LinkedIn Outreach AI" className="mb-4 w-16 h-16" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  LinkedIn Outreach AI – Automated & Personalized Messaging
                </h3>
                <ul className="list-none pl-0 text-gray-400">
                  <li className="mb-1">AI-crafts unique LinkedIn messages for each prospect</li>
                  <li className="mb-1">Adaptive follow-ups to increase engagement.</li>
                  <li>Sentiment analysis ensures the best response timing.</li>
                </ul>
              </div>

              {/* Card 3 */}
              <div className="bg-[#1B253A] rounded-2xl p-6">
                <img src={chat} alt="Email Sending Platform" className="mb-4 w-16 h-16" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  Email Sending Platform – Intelligent Email Outreach
                </h3>
                <ul className="list-none pl-0 text-gray-400">
                  <li className="mb-1">AI-composed email sequences tailored to prospects.</li>
                  <li className="mb-1">Sentiment analysis for auto-replies.</li>
                  <li>Continuous follow-ups & real-time engagement alerts.</li>
                </ul>
              </div>

              {/* Card 4 */}
              <div className="bg-[#1B253A] rounded-2xl p-6">
                <img src={email} alt="Email Sending Platform" className="mb-4 w-16 h-16" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  Email Sending Platform – Intelligent Email Outreach
                </h3>
                <ul className="list-none pl-0 text-gray-400">
                  <li className="mb-1">AI-composed email sequences tailored to prospects.</li>
                  <li className="mb-1">Sentiment analysis for auto-replies.</li>
                  <li>Continuous follow-ups & real-time engagement alerts.</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Testimonial part */}
      <div className="py-10" data-aos="fade-up">
        <div className="container mx-auto" style={{ backgroundColor: 'transparent' }}>
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8">
            {/* Section Title */}
            <div className="mb-12 text-center">
              <div className="flex justify-center items-center flex-wrap gap-4 mb-2">
                <div className="w-10 h-[2px] bg-gradient-to-r from-blue-500 to-purple-500"></div>
                <span className="font-medium text-white text-xl">Real Businesses, Real Results</span>
              </div>
              <h2 className="text-white text-3xl font-normal">What Our Clients are Saying</h2>
            </div>

            {/* Testimonial Content */}
            <div className="relative">
              <div className="p-6 text-center">
                <p className="text-gray-300 italic mb-4 w-[700px]">{currentTestimonial.text}</p>
                <h6 className="text-white font-semibold">{currentTestimonial.author}</h6>
                <span className="text-gray-300 text-sm mb-2">Happy Customer</span>
                {/* Star Rating */}
                <ul className="flex justify-center gap-1 mt-2"> {/* flex class for inline arrangement */}
                  {Array(5)
                    .fill()
                    .map((_, i) => (
                      <li key={i}>
                        <span className="text-yellow-500">
                          <FaStar /> {/* Use React Icon star */}
                        </span>
                      </li>
                    ))}
                </ul>
              </div>

              {/* Navigation Buttons */}
              <div className="flex justify-center mt-4">
                <button onClick={goToPrevious} className="text-white hover:text-gray-300 px-2">
                  <MdArrowBack size={24} />  {/* Use React Icon */}
                </button>
                <div className="w-16 h-16 rounded-full overflow-hidden mx-4">
                  <img src={currentUserImage} alt="User" className="object-cover w-full h-full" />
                </div>
                <button onClick={goToNext} className="text-white hover:text-gray-300 px-2">
                  <MdArrowForward size={24} />  {/* Use React Icon */}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pricing and disclamer */}
      <div className="py-10" >
        <div className=" mx-auto w-[70%]" style={{ backgroundColor: 'transparent' }}>
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8" data-aos="fade-up">
            {/* Title and Description Section */}
            <div className="mb-2">
              <div className="text-center">
                <h2 className="text-white text-4xl font-normal py-4">Flexible Plans Tailored to Your Needs</h2>
                <div className='w-[60%] items-center justify-center mx-auto'>
                  <p className="text-gray-400 py-4 text-lg font-semibold text-center">
                    At Roboman, we blend hyper-personalized outreach with complete convenience—now including LinkedIn connections and follow-up messages alongside our bespoke email campaigns.
                  </p>
                  <p className="text-gray-400 font-semibold mb-4">Here’s what you can expect with every prospect:</p>
                </div>
              </div>

              {/* Feature Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 my-3">
                <div className="bg-[#1B253A] rounded-2xl p-6 text-white">
                  <h3 className="text-xl font-semibold mb-2">In-depth DiSC & OCEAN Personality Profiling</h3>
                  <p className="text-gray-400">
                    Each profile costs just £1.00, revealing valuable traits that help us craft five personalized emails and up to four unique LinkedIn messages for every prospect.
                  </p>
                </div>

                <div className="bg-[#1B253A] rounded-2xl p-6 text-white">
                  <h3 className="text-xl font-semibold mb-2">Hands-Free Campaign Management</h3>
                  <p className="text-gray-400">
                    We’ll identify and engage contacts that match your ideal customer profile. From writing personality-tailored outreach to automating follow-ups, we handle it all while you focus on what matters most—closing deals.
                  </p>
                </div>

                {/* Only 2 so reduce to 2 columns */}
                <div className="bg-[#1B253A] rounded-2xl p-6 text-white col-span-2 md:col-span-1">
                  <h3 className="text-xl font-semibold mb-2">Real-Time Notifications</h3>
                  <p className="text-gray-400">You’ll be alerted the instant a prospect shows positive interest, ensuring no opportunity is missed.</p>
                </div>
              </div>
            </div>

            {/* Pricing Tiers Section */}
            <div data-aos="fade-up">
              <p className="text-gray-400 text-center py-10 w-[700px] mx-auto">
                Our four pricing tiers are designed to suit different scales of outreach while maintaining the same level of personalization:
              </p>

              {/* Pricing Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 w-60%" >
                {/* Tier 1 */}
                <div className="bg-[#1B253A] rounded-2xl p-6 text-center">
                  <div className="bg-transparent text-white py-2 h-[50px]">
                    <span className="inline-flex items-center">
                    </span>
                  </div>
                  <h4 className="text-white text-xl">Starter Plan</h4>
                  <hr className="my-4 border-gray-700" />
                  <h2 className="text-white text-3xl mb-4">£49.99<span className="text-base font-normal"> Monthly</span></h2>
                  <p className="text-gray-400 text-lg">
                    A straightforward package for those wanting to explore the impact of hyper-personalized outreach.
                  </p>
                  <hr className="my-4 border-gray-700" />
                  <div>
                    <button
                      type="button"
                      onClick={handleButtonClick}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-4 rounded-full"
                    >
                      Get Started
                    </button>
                  </div>
                </div>

                {/* Tier 2 (Most Popular) */}
                <div className="bg-[#1B253A] rounded-2xl text-center overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white py-2 h-[50px]">
                    <span className="inline-flex items-center">
                      Most Popular
                    </span>
                  </div>
                  <div className="p-6">
                    <h4 className="text-white text-xl">Growth Plan</h4>
                    <hr className="my-4 border-gray-700" />
                    <h2 className="text-white text-3xl flex justify-center items-baseline mb-4">
                      <span className="text-base line-through mr-2 text-gray-400">£</span>
                      £119.99<span className="text-base font-normal"> Monthly</span>
                    </h2>
                    <p className="text-gray-400 text-lg">
                      Ideal for striking the right balance between budget and reach. Experience comprehensive LinkedIn and email engagement with a higher volume of prospects for maximum results.
                    </p>
                    <hr className="my-4 border-gray-700" />
                    <div>
                      <button
                        type="button"
                        onClick={handleButtonClick}
                        className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-4 rounded-full"
                      >
                        Get Started
                      </button>
                    </div>
                  </div>
                </div>

                {/* Tier 3 */}
                <div className="bg-[#1B253A] rounded-2xl p-6 text-center">
                  <div className="bg-transparent text-white py-2 h-[50px]">
                    <span className="inline-flex items-center">
                    </span>
                  </div>
                  <h4 className="text-white text-xl">Scale Plan</h4>
                  <hr className="my-4 border-gray-700" />
                  <h2 className="text-white text-3xl mb-4">£239.99<span className="text-base font-normal"> Monthly</span></h2>
                  <p className="text-gray-400 text-lg">
                    Expand your personalized approach to a larger audience, with more substantial coverage and messaging frequency.
                  </p>
                  <hr className="my-4 border-gray-700" />
                    <div>
                      <button
                        onClick={handleButtonClick}
                        type="button"
                        className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-4 rounded-full"
                      >
                        Get Started
                      </button>
                    </div>
                </div>
              </div>
            </div>

            {/* Small Disclaimer Text */}
            <div className= "h-[300px] flex justify-center items-center" style={{ backgroundColor: 'transparent' }}>
              <p className="w-[700px] text-white text-center" >
                No matter where you start, our transparent pricing and holistic approach ensure every interaction resonates at a personal level. For the best return on investment, choose our £1,000 tier combining robust outreach, valuable personality insights, and hands-free campaign management for truly meaningful connections
              </p>
            </div>
          </div>
        </div>
      </div>

      <section className="bg-[#1976D2] mt-20"> {/* Dark Background */}
        <div className=" mx-auto text-center py-10" style={{ backgroundColor: 'transparent' }}>
          {/* Grid Container */}
          <div className="grid grid-cols-1 gap-8">
            {/* Subheading */}
            <p className="text-gray-300 text-xl mb1 font-semibold">Ready to Revolutionize Your Outreach?</p>

            {/* Main Heading */}
            <h1 className="text-white text-4xl lg:text-5xl font-bold mb-8 leading-loose" data-aos="fade-up">
              You're one step away from <br />
              more conversations, better engagement, <br />
              and effortless lead generation
            </h1>

            {/* Call to Action Button */}
            <div className="flex justify-center items-center mb-4">
              <button
                onClick={handleButtonClick}
                className="bg-[#FFC107] text-[#000000] font-semibold py-3 rounded-md inline-block hover:bg-[#FFDA63] hover:text-black flex items-center justify-center w-[400px]"
              >
                Get Started Now!
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Roboman Info */}              
      <footer className="bg-white">
        <div className="py-20">
          <div className="mx-auto w-[70%]">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {/* Roboman Info */}
              <div className="text-left">
                <a href="/" className="inline-block mb-4">
                  <img src={Logo} alt="Roboman Logo" className="h-12" />
                </a>
                <p className="text-gray-700 text-sm font-semibold">
                  Transforming Outreach with AI-Powered Engagement, Personalised Conversations, and Higher Conversions for Your Business
                </p>
              </div>

              {/* Contact Info */}
              <div>
                <h5 className="text-gray-800 font-semibold mb-4">Contact Info</h5>
                
                {/* Address */}
                <p className="text-gray-700 text-sm flex items-start mb-2 font-semibold">
                  <span className="mr-2 text-lg">
                    <BiMap /> {/* Location Icon */}
                  </span>
                  402 Pinnacle Tower, 23 Fulton Road, <br />
                  Wembley, United Kingdom, England, HA9 0GB
                </p>
                
                {/* Phone */}
                <p className="text-gray-700 text-sm flex items-center mb-2">
                  <span className="mr-2 text-lg">
                    <BiPhoneCall /> {/* Phone Icon */}
                  </span>
                  <a href="tel:***********" className="text-black no-underline font-semibold">
                    ***********
                  </a>
                </p>
                
                {/* Email */}
                <p className="text-gray-700 text-sm flex items-center">
                  <span className="mr-2 text-lg">
                    <BiEnvelope /> {/* Email Icon */}
                  </span>
                  <a href="mailto:<EMAIL>" className="text-black no-underline font-semibold">
                    <EMAIL>
                  </a>
                </p>
              </div>

              {/* Quick Links */}
              <div>
                <h5 className="text-gray-800 font-semibold mb-4">Quick Links</h5>
                <ul className="list-none pl-0">
                  <li className="mb-2">
                    <div className="text-gray-700 hover:text-gray-900 text-sm font-semibold">Home</div>
                  </li>
                  <li className="mb-2">
                    <div className="text-gray-700 hover:text-gray-900 text-sm font-semibold">Contact</div>
                  </li> 
                  <li className="mb-2">
                    <div className="text-gray-700 hover:text-gray-900 text-sm font-semibold">Privacy Policy</div>
                  </li>
                  <li>
                    <div className="text-gray-700 hover:text-gray-900 text-sm font-semibold">Terms & Conditions</div>
                  </li>
                </ul>
              </div>

              {/* Follow Us */}
              <div>
                <h5 className="text-gray-800 font-semibold mb-4 font-semibold">Follow Us</h5>
                <ul className="list-none pl-0">
                  <li>
                    <a href="#" className="text-gray-700 hover:text-gray-900 text-sm">
                      <FaTwitter size={18} />
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="py-0">
          <div className="mx-auto">
            <p className="text-center text-gray-700 text-sm font-semibold">© 2025 Roboman. All rights reserved.</p>
          </div>
        </div>
      </footer>

    </div>
  )
};


const App = () => {

  useEffect(() => {
    if (!document.getElementById("hs-script-loader")) {
      const script = document.createElement("script");
      script.src = "//js-na2.hs-scripts.com/243630945.js";
      script.async = true;
      script.defer = true;
      script.id = "hs-script-loader";
      document.body.appendChild(script);
    }
    return () => {
      const existingScript = document.getElementById("hs-script-loader");
      if (existingScript) {
        document.body.removeChild(existingScript);
      }
    };
  }, []);

  const [userData, setUserData] = useState({
    firstName: 'Alice',
    lastName: 'Wonderland',
    email: '<EMAIL>',
    type: 'Premium Member',
    bio: 'Software engineer passionate about building beautiful and functional user interfaces. Enjoys hiking, photography, and exploring new technologies.',
    profilePicture: 'https://placehold.co/150x150/A78BFA/ffffff?text=AW',
    billingHistory: [
      { id: '1', date: '2024-07-01', amount: 19.99, description: 'Monthly Subscription', status: 'Paid' },
      { id: '2', date: '2024-06-01', amount: 19.99, description: 'Monthly Subscription', status: 'Paid' },
      { id: '3', date: '2024-05-01', amount: 19.99, description: 'Monthly Subscription', status: 'Paid' },
      { id: '4', date: '2024-04-01', amount: 19.99, description: 'Monthly Subscription', status: 'Paid' },
    ],
  });

  const handleProfileSave = (updatedData) => {
    setUserData(prevData => ({ ...prevData, ...updatedData }));
    console.log("Profile updated:", updatedData);
  };

  return (
    <AuthProvider>
      <DataProvider>
        <Router>
          <Routes>
            <Route path="/" element={<LandingPage />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/forgotpw" element={<ForgotPassword />} />
            <Route path="/resetpw" element={<ResetPassword />} />
            <Route path="/billing" element={<BillingPage />} />

            {/* <Route path="/onboarding-select" element={<ProtectedRoute><ClassifyCustomer /></ProtectedRoute>} /> */}
            <Route path="/onboarding-userinfo" element={<ProtectedRoute><OnboardingInputUserData /></ProtectedRoute>} />
            {/* <Route path="/campinitselect" element={<ProtectedRoute><CampaignInitialize /></ProtectedRoute>} /> */}
            <Route path="/campinitselect" element={<ProtectedRoute><CampaignInitialize2 /></ProtectedRoute>} />
            {/* <Route path="/useraichat" element={<ProtectedRoute><UserAIChatUI /></ProtectedRoute>} /> */}
            <Route path="/campfill" element={<ProtectedRoute><CampManualFill /></ProtectedRoute>} />
            
            {/* <Route path="/onboarding-plan" element={<ProtectedRoute><OnboardingSelectPlan /></ProtectedRoute>} /> */}
            <Route path="/onboardingcampmatch" element={<ProtectedRoute><OnboardingCampaignMatching /></ProtectedRoute>} />
            <Route path="/reviewinfo" element={<ProtectedRoute><OnboardingReviewCampaign /></ProtectedRoute>} />
            <Route path="/useraichat" element={<ProtectedRoute><UserAIChatUI /></ProtectedRoute>} />
            <Route path="/new-campaign" element={<ProtectedRoute><NewCampaign /></ProtectedRoute>} />

            <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
            <Route path="/my-campaign" element={<ProtectedRoute><MyCampaign /></ProtectedRoute>} />
            <Route path="/update-campaign" element={<ProtectedRoute><UpdateCampaign /></ProtectedRoute>} />

            {/* <Route path="/mailbox" element={<ProtectedRoute><MailboxData /></ProtectedRoute>} /> */}
            {/* <Route path="/analyzinguser" element={<ProtectedRoute><AnalyzingUser /></ProtectedRoute>} /> */}
            <Route path="/email-status" element={<ProtectedRoute><EmailStatus /></ProtectedRoute>} />
            {/* <Route path="/mail-reply" element={<ProtectedRoute><MailReplyPage /></ProtectedRoute>} /> */}
            <Route path="/viewinsight" element={<ProtectedRoute><ViewInsight /></ProtectedRoute>} />
            <Route path="/unibox" element={<ProtectedRoute><Unibox /></ProtectedRoute>} />
            <Route path="/addmatching" element={<ProtectedRoute><AddMatchingCompany /></ProtectedRoute>} />

            <Route path="/successpayment" element={<ProtectedRoute><CompletePayment /></ProtectedRoute>} />
              {/* User Detail Page Route */}
            <Route
              path="/settings" // This path correctly serves as the main entry to settings
              element={<Account user={userData} onProfileSave={handleProfileSave} />} // Use the renamed Account component
            />
              <Route path="/oauth/callback" element={<OAuthCallback />} />
          </Routes>
        </Router>
      </DataProvider>
    </AuthProvider>
  );
}

export default App;