"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

export default function ClaimCheckerPage() {
  const router = useRouter();
  const [form, setForm] = useState({
    title: "",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    referralCode: "",
    lender: "",
    specific_lender: "",
  });

  const lenderOptions = [
    { value: "", label: "Select Lender" },
    { value: "Other", label: "Other" },
    { value: "Abbey National - Santander UK plc", label: "Abbey National - Santander UK plc" },
    { value: "Abbey National (Santander)", label: "Abbey National (Santander)" },
    { value: "Accord Mortgages", label: "Accord Mortgages" },
    { value: "Acenden Mortgages", label: "Acenden Mortgages" },
    { value: "Advantage Finance Ltd", label: "Advantage Finance Ltd" },
    { value: "Alliance & Leicester", label: "Alliance & Leicester" },
    { value: "Bank of Ireland", label: "Bank of Ireland" },
    { value: "Bank of Scotland", label: "Bank of Scotland" },
    { value: "Barclays Bank", label: "Barclays Bank" },
    { value: "Birmingham Midshires", label: "Birmingham Midshires" },
    { value: "Black Horse", label: "Black Horse" },
    // Add more lenders as needed
  ];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm({ ...form, [name]: value });
  };

  const handleClick = () => {
    const title = localStorage.getItem("selectedClaimTitle");

    if (title === "Mortgage Miscalculation") {
      router.push("/mortgage");
    } else {
      router.push("/default-claim-page"); // fallback or default page
    }
  };

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      {/* Left side image */}
      <div className="md:w-1/2 w-full">
        {/* <img
          src="/claim-image.jpg" // Save and use the uploaded image as public asset or import from URL
          alt="House Image"
          className="w-full h-full object-cover"
        /> */}
      </div>

      {/* Right side form */}
      <div className="md:w-1/2 w-full bg-white p-8 flex flex-col justify-center shadow-md">
        <h2 className="text-orange-600 text-xl font-bold mb-2">
          <u>FREE</u> CLAIM CHECKER
        </h2>
        <p className="mb-4 text-gray-700">
          Complete our secure, <strong>60 seconds</strong> form to see if you’re owed
          £1,000’s in compensation.
        </p>

        <div className="bg-white p-6 rounded-lg border border-gray-300">
          {/* Title selection */}
          <div className="mb-4 flex flex-wrap gap-4">
            {["Mr", "Mrs", "Ms", "Miss", "Other"].map((option) => (
              <label key={option} className="flex items-center gap-1">
                <input
                  type="radio"
                  name="title"
                  value={option}
                  checked={form.title === option}
                  onChange={handleChange}
                />
                {option}
              </label>
            ))}
          </div>

          {/* Name Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input
              type="text"
              name="firstName"
              placeholder="First Name"
              value={form.firstName}
              onChange={handleChange}
              className="p-2 border rounded w-full bg-gray-100 hover:bg-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            <input
              type="text"
              name="lastName"
              placeholder="Last Name"
              value={form.lastName}
              onChange={handleChange}
              className="p-2 border rounded w-full bg-gray-100 hover:bg-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Contact Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input
              type="email"
              name="email"
              placeholder="Email Address"
              value={form.email}
              onChange={handleChange}
              className="p-2 border rounded w-full bg-gray-100 hover:bg-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
            <input
              type="text"
              name="phone"
              placeholder="Phone Number"
              value={form.phone}
              onChange={handleChange}
              className="p-2 border rounded w-full  bg-gray-100 hover:bg-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Referral code */}
          <div className="mb-4">
            <input
              type="text"
              name="referralCode"
              placeholder="Referral Code if Any"
              value={form.referralCode}
              onChange={handleChange}
              className="p-2 border rounded w-full bg-gray-100 hover:bg-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          {/* Dropdown */}
          <div className="mb-4 relative">
            <label className="block font-semibold mb-1">Who was your loan with?</label>

            <select
              name="lender"
              value={form.lender}
              onChange={handleChange}
              className="p-2 border rounded w-full bg-gray-100 hover:bg-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              {lenderOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {form.lender === "Other" && (
              <input
                type="text"
                name="specific_lender"
                placeholder="Enter other lender name"
                value={form.specific_lender}
                onChange={handleChange}
                className="mt-2 p-2 border rounded w-full bg-gray-100 hover:bg-white focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            )}

          </div>



          {/* Submit button */}
          <button
            onClick={handleClick} // or "/mortgage" if that's a typo
            className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded text-lg"
          >
        
            <strong> Start Free Claim Check </strong>&gt;&gt;
          </button>
        </div>
      </div>
    </div>
  );
}
// Note: Ensure to replace "/claim-image.jpg" with the actual path of your image in the public folder or use an appropriate URL.