FROM python:3.13.2-slim-bookworm

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONBUFFERED=1
ENV HNSWLIB_NO_NATIVE=1
ENV TOKENIZERS_PARALLELISM=false

# # Install core dependencies, since we use postgres 
RUN apt-get update \
    && apt-get install -y libpq-dev build-essential \
    && apt-get install -y ca-certificates libssl-dev openssl \
    && apt-get install -y curl \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .

RUN pip install -r requirements.txt --no-cache-dir

COPY . .

EXPOSE 8000

CMD ["python",  "main.py"]