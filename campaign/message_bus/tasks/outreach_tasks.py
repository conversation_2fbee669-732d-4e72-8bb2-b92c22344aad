import requests
import json
import re
from bs4 import BeautifulSoup
from loguru import logger
from sqlalchemy import update, delete, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
from sqlalchemy.ext.asyncio import  AsyncSession
from datetime import datetime, timedelta, time, date

from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from database.database import SessionFactory, get_session
from database.models import SentEmail, ReceivedEmail, Company, Draft, Representative, SenderEmail, Campaign, LinkedInConnection, SentMessage
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPC<PERSON>lient
from shared_state import app_state
from core.config import settings

#region EMAIL OUTREACH
async def get_sender(db: AsyncSession):
    try:
        query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc(), SenderEmail.updated_at.asc())
        results = await db.execute(query)
        senders = results.scalars().all()
        if len(senders) == 0:
            return None
        else:
            sender = senders[0]
            return {
                "sender_address": sender.sender_address,
                "unipile_account_id": sender.unipile_account_id
            }
        # return {
        #     "sender_address": "<EMAIL>",
        #     "unipile_account_id": "DVuozmRlTw-my9ix8ZTJmg"
        # }
    except Exception as e:
        raise e

async def create_drafts(message):
    """
    Creates email draft sequences for outreach campaigns.
    
    Processes a message from the queue, generates email drafts using AI service,
    and stores them in the database. Also handles credit usage tracking.
    
    Args:
        message: RabbitMQ message containing campaign and company details
    """

    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]         # Company to generate drafts for
        prompt_name = message_body["prompt_name"] if "prompt_name" in message_body else None         # Optional prompt template name
        campaign_id = message_body["campaign_id"]
        auto_outreach = message_body["auto_outreach"] if "auto_outreach" in message_body else False         # Controls automatic sending
        user_id = message_body["user_id"] if "user_id" in message_body else None
        logger.info(f"[CONSUMER] Processing create_drafts task")       
        async for db in get_session():
            # Fetch company and campaign data
            query = select(Company).where(Company.company_id==company_id)
            results = await db.execute(query)
            company = results.scalars().first()
            query = select(Campaign).where(Campaign.campaign_id==campaign_id)
            results = await db.execute(query)
            campaign = results.scalars().first()

            # Verify user has sufficient credits
            if company.credit_spent == False:
                headers = {
                    "accept": "application/json",
                    "request-user-id": str(user_id)
                }
                response = requests.get(
                    f"{settings.USER_SERVICE_URL}/api/users/credits",
                    headers=headers
                )
                if response.status_code not in [200, 201]:
                    raise Exception(f"Failed to retrieve user credits: {response.text}")
                else:
                    user_credit = response.json()
                    if user_credit["current_credits"] < 1:
                        raise Exception("User does not have enough credits")

            # Get campaign details and sender name
            campaign_info = campaign.campaign_info
            campaign_contact = campaign.campaign_contact
            user_nick_name = "Robert Mandev"
            if campaign_contact.get("user_nick_name") is not None:
                user_nick_name = campaign_contact["user_nick_name"]

            # Get representative and company data
            query = (
                select(
                    Representative.rep_name, 
                    Representative.rep_email, 
                    # Representative.personality_analysis, 
                    # Representative.email_personalization, 
                    Representative.communication_advice,
                    Representative.company_id,
                    Representative.rep_linkedin_address,
                    # Representative.analysis_status,
                    Company.outreach_progress,
                    Company.industry,
                    Company.company_name,
                    Company.auto_outreach
                )
                .join(Company, Representative.company_id==Company.company_id)
                .filter(Representative.company_id==company_id)
            )

            result = await db.execute(query)
            records = result.fetchall()
            for row in records:
                # Only process if analysis is complete or not found
                if row.company_id is not None:
                    # Prepare parameters for AI draft generation
                    params = {
                        "from_name": user_nick_name,
                        "to_name": row.rep_name,
                        "core_service": campaign_info["core_service"],
                        "key_benefits": campaign_info["key_benefits"],
                        "problem_solved": campaign_info["problem_solved"],
                        "industry": row.industry,
                        "company_name": row.company_name,
                        "prompt_name": prompt_name,
                        "target_audience": campaign_info["target_audience"],
                        "unique_selling_proposition": campaign_info["unique_selling_proposition"]
                    }

                    # Call AI service to generate drafts
                    aiengine_url = f"{settings.AI_SERVICE_URL}/api/nlp/generate_drafts/test"
                    headers = {
                        "accept": "application/json",
                        "Content-Type": "application/json"
                    }
                    response = requests.post(aiengine_url, headers=headers, json=params)
                    if response.status_code not in [200, 201]:
                        raise Exception(f"Error from aiengine service: Failed to generate drafts for company_id: {row.company_id}")
                    
                    result = response.json()
                    drafts_list = result["drafts_list"]
                    drafts_to_create = []
                    
                    # Process each draft from AI response
                    for draft in drafts_list:
                        # Extract plain text from HTML body
                        draft_body = draft["body"]                            
                        soup = BeautifulSoup(draft_body, "html.parser")
                        draft_body_plain = soup.text

                        # Check if draft already exists
                        query = select(Draft).filter(Draft.company_id==row.company_id, Draft.draft_type==draft["draft_type"])
                        result = await db.execute(query)
                        existing_draft = result.scalars().first()
                        if existing_draft is not None:
                            # Update existing draft if not sent yet
                            if existing_draft.email_sent == False:
                                stmt = (
                                    update(Draft)
                                    .filter(Draft.company_id==row.company_id, Draft.draft_type==draft["draft_type"])
                                    .values(
                                        subject = draft["subject"],
                                        body = draft_body,
                                        body_plain = draft_body_plain,
                                        day_to_send = draft["day_to_send"]
                                    )
                                )
                                await db.execute(stmt)
                            else:
                                continue
                        else:                    
                            # Create new draft
                            new_draft = Draft(
                                from_name = user_nick_name,
                                draft_type = draft["draft_type"],
                                company_id = row.company_id,
                                to_address = row.rep_email,
                                to_name = row.rep_name,
                                to_linkedin_address = row.rep_linkedin_address,
                                subject = draft["subject"],
                                body = draft_body,
                                body_plain = draft_body_plain,
                                day_to_send = draft["day_to_send"]
                            )
                            drafts_to_create.append(new_draft)
                    
                    # Add all new drafts to database
                    db.add_all(drafts_to_create)

                    # Set auto_outreach flag based on input and existing value
                    if row.auto_outreach != True:
                        is_auto_outreach = True if auto_outreach == True else False
                    else:
                        is_auto_outreach = False

                    # Update company with first draft content and status
                    stmt = update(Company).filter(Company.company_id==row.company_id).values(
                        content_subject = drafts_list[0]["subject"],
                        content = drafts_list[0]["body"],
                        email_confirmation_status_id = 2,
                        auto_outreach = is_auto_outreach,
                    )
                    await db.execute(stmt)

                    # Deduct user credit if not already spent
                    if company.credit_spent == False:
                        headers = {
                            "accept": "application/json",
                            "request-user-id": str(user_id)
                        }
                        response = requests.put(
                            f"{settings.USER_SERVICE_URL}/api/users/credits/update-usage",
                            headers=headers,
                            json={
                                "campaign_id": str(campaign_id),
                                "company_id": str(company.company_id),
                                "campaign_name": campaign.campaign_name,
                                "company_name": company.company_name
                            }
                        )
                        if response.status_code in [200, 201]:
                            stmt = update(Company).filter(Company.company_id==row.company_id).values(credit_spent=True)
                            await db.execute(stmt)

                    # Commit all database changes
                    await db.commit()
                    logger.info(f"[CONSUMER] Successfully created drafts for company_id: {row.company_id}")

    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process create_drafts task\n {str(e)}") 


async def update_db_emails(draft_email: Draft, from_address: str, tracking_id: str, to_info: list, unipile_account_id: str, db: AsyncSession):
    """
    Create a SentEmail record and update related database records after sending an email.
    
    Args:
        draft_email: Draft object containing email content and metadata
        from_address: Email address of the sender
        tracking_id: Unique ID for tracking email opens/clicks
        to_info: List containing recipient information
        unipile_account_id: ID of the Unipile account used to send the email
        db: Database session for executing queries
    """
    try:
        # Create new SentEmail record with email details
        sent_email = SentEmail(
            subject=draft_email.subject,
            body=draft_email.body,
            body_plain=draft_email.body_plain,
            from_address=from_address,
            to_info=to_info,
            to_addresses=[draft_email.to_address],
            company_id=draft_email.company_id,
            tracking_id=tracking_id,
            draft_type=draft_email.draft_type,
            account_id=unipile_account_id
        )        
        db.add(sent_email)
        
        # Mark the draft as sent in the database
        stmt = update(Draft).where(Draft.draft_id==draft_email.draft_id).values(email_sent=True)
        await db.execute(stmt)

        # Update company record with latest email details and status
        stmt = update(Company).filter(Company.company_id==draft_email.company_id).values(
            outreach_progress=draft_email.draft_type,
            content_subject=draft_email.subject,
            content=draft_email.body,
            email_confirmation_status_id=4,  # Status 4 indicates email was sent
            last_date_sent_email=date.today(),
            last_time_sent_email=datetime.now()
        )
        await db.execute(stmt)

        # Decrement the remaining email count for the sender
        stmt = update(SenderEmail).filter(SenderEmail.sender_address==from_address).values(remaining_emails=SenderEmail.remaining_emails-1)
        await db.execute(stmt)

    except Exception as e:
        # Re-raise any exceptions to be handled by the caller
        raise e


async def send_email_outreach(message):
    """
    Send an email to the recipient.
    
    Processes a message from the queue, finds the next unsent draft email for the specified company,
    and sends it using the email service. Updates database records after successful sending.
    
    Args:
        message: RabbitMQ message containing company ID to send email for
    """

    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]
        logger.info(f"[CONSUMER] Processing send_email_outreach task")
        async for db in get_session():
            # Get company information
            query = select(Company).filter(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()            
            
            # Only proceed if company hasn't already replied (status 6 or 7)
            if company.email_confirmation_status_id not in [6,7]:
                # Find the next unsent draft email
                query = select(Draft).filter(Draft.company_id==company_id, Draft.email_sent==False).order_by(Draft.draft_type.asc())
                result = await db.execute(query)
                drafts = result.scalars().all()
                if len(drafts) > 0:
                    draft = drafts[0]
                    draft_body = draft.body
                    
                    # Get campaign contact info for calendly link
                    query = select(Campaign.campaign_contact).filter(Campaign.campaign_id==company.campaign_id)
                    results = await db.execute(query)
                    campaign_contact = results.scalars().first()

                    # Add calendly booking link to email if available
                    if campaign_contact.get("calendly_link") is not None:
                        calendly_link = campaign_contact.get("calendly_link")
                        calendly_booking_line = f"""<p>\nBook a meeting on Calendly: <a href={calendly_link} target="_blank">Click here</a></p>"""
                        draft_body = draft_body.replace("</body>", f"{calendly_booking_line}\n\n</body>")

                    # Get available email sender
                    sender = await get_sender(db=db)     
                    
                    if sender is None:
                        raise Exception("[CONSUMER] No more email senders available")
                    
                    # Initialize email client and send the email
                    email_client = EmailClient()
                    email_client.set_unipile_account_id(sender["unipile_account_id"])
                    response = email_client.send_email(
                        subject=draft.subject,
                        content=draft_body,
                        name_from=draft.from_name.title(),
                        name_to=draft.to_name,
                        email_to=draft.to_address
                    )
                    if response.status_code not in [200,201]:
                        raise Exception(f"[CONSUMER] Failed to send email to {draft.to_address}\n {response.text}")
                    
                    # Prepare recipient info for database
                    to_info = [
                        {
                            "display_name": draft.to_name.title(),
                            "identifier": draft.to_address,
                            "identifier_type": "EMAIL_ADDRESS"
                        }
                    ]
                    
                    # Update database records after successful send
                    await update_db_emails(draft_email=draft, from_address=sender["sender_address"], tracking_id=response.json()["tracking_id"], to_info=to_info, unipile_account_id=sender["unipile_account_id"], db=db)
                    await db.commit()                

                    logger.info(f"[CONSUMER] Successfully sent email draft {draft.draft_type} to {draft.to_address}")

            else:
                logger.info(f"[CONSUMER] no email to send to {company_id}")
    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process send_email_outreach task\n {str(e)}")

async def send_response(message):
    """
    Sends a response email to a recipient who has replied to a previous outreach email.
    
    Processes a message from the queue containing response details, sends the email
    using an available sender, and updates the company status in the database.
    
    Args:
        message: RabbitMQ message containing response email details
    """
    json_string = message.body.decode()
    message_body = json.loads(json_string)    

    try:
        async for db in get_session():
            # Get available email sender
            sender = await get_sender(db=db)
            if sender is None:
                    raise Exception("No more email senders available")
                    
            # Initialize email client with sender account
            email_client = EmailClient()
            email_client.set_unipile_account_id(sender["unipile_account_id"])    
            email_to = message_body["to_address"]
            company_id = message_body["company_id"]
            
            # Send the response email
            response = email_client.send_email(
                subject=message_body["subject"],
                content=message_body["body"],
                name_from=message_body["from_name"],
                name_to=message_body["to_name"],
                email_to=email_to
            )
            
            # Handle response and update database
            if response.status_code not in [200,201]:
                raise Exception(response.text)
            else:
                # Update company status to indicate response was sent (status 7)
                stmt = update(Company).filter(Company.company_id==company_id).values(email_confirmation_status_id=7)
                await db.execute(stmt)
                await db.commit()
                logger.info(f"successfully responded the reply from {email_to}")
    except Exception as e:
        logger.error(f"failed to send response to reply email: {str(e)}")

#endregion