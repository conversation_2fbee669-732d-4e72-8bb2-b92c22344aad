import asyncio
import logging
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import Cron<PERSON>rigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from task_scheduler.tasks import (reset_remaining_emails, 
                                #   auto_send_email_outreach, 
                                #   auto_send_linkedin_outreach, 
                                  continue_auto_email_outreach, 
                                  continue_auto_linkedin_outreach,
                                  start_auto_outreach)

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

# A dictionary mapping task names to their corresponding function and cron schedule.
BACKGROUND_TASKS = {
    "reset_remaining_emails": (reset_remaining_emails, "0 0 * * *"),  # Midnight every day
    "start_auto_outreach": (start_auto_outreach, "0 1 * * *"), # 1 AM every day,
    "continue_auto_email_outreach": (continue_auto_email_outreach, "0 8 * * *"), # 8 AM every day
    "continue_auto_linkedin_outreach": (continue_auto_linkedin_outreach, "0 9 * * *") #9 AM every day
}

#For testing local machine
# BACKGROUND_TASKS = {
#     # "reset_remaining_emails": (reset_remaining_emails, "0 0 * * *"),  # Midnight every day
#     "start_auto_outreach": (start_auto_outreach, "1,6,11,16,21,26,31,36,41,46,51,56 * * * *"), # 1 AM every day,
#     "continue_auto_email_outreach": (continue_auto_email_outreach, "3,8,13,18,23,28,33,38,43,48,53,58 * * * *"), # 8 AM every day
#     "continue_auto_linkedin_outreach": (continue_auto_linkedin_outreach, "3,8,13,18,23,28,33,38,43,48,53,58 * * * *") #8 AM every day
# }

# BACKGROUND_TASKS = {
#     # "reset_remaining_emails": (reset_remaining_emails, "0 0 * * *"),  # Midnight every day
#     "start_auto_outreach": (start_auto_outreach, "1,6,11,16,21,26,31,36,41,46,51,56 * * * *"), # 1 AM every day,
#     "continue_auto_email_outreach": (continue_auto_email_outreach, "* * * * *"), # 8 AM every day
#     "continue_auto_linkedin_outreach": (continue_auto_linkedin_outreach, "* * * * *") #8 AM every day
# }

class TaskScheduler:
    """
    A wrapper class for the APScheduler to manage and run background tasks.
    """
    def __init__(self):
        """
        Initializes the TaskScheduler with an AsyncIOScheduler instance.
        """
        self.scheduler = AsyncIOScheduler()

    async def start(self):
        """Start the scheduler."""
        logger.info("Starting scheduler...")
        self.scheduler.start()

        # Add event listeners for job execution and errors
        self.scheduler.add_listener(self.log_job_executed, EVENT_JOB_EXECUTED)
        self.scheduler.add_listener(self.log_job_error, EVENT_JOB_ERROR)

        # Add all background tasks
        for task_name, (func, cron_expression) in BACKGROUND_TASKS.items():
            self.add_cron_job(func, cron_expression)

        logger.info("Scheduler started.")

    async def shutdown(self):
        """Gracefully shut down the scheduler."""
        logger.info("Shutting down scheduler...")
        self.scheduler.shutdown()
        logger.info("Scheduler shut down.")

    def add_cron_job(self, func, cron_expression: str):
        """
        Add a cron job to the scheduler.
        
        :param func: The function to execute.
        :param cron_expression: A cron expression (e.g., "0 0 * * *" for midnight).
        """
        # Create a CronTrigger from the cron expression string.
        trigger = CronTrigger.from_crontab(cron_expression)
        self.scheduler.add_job(func, trigger)
        logger.info(f"Added cron job: {func.__name__} with cron expression: {cron_expression}")

    def log_job_executed(self, event):
        """
        Log when a scheduled job is successfully executed.

        Args:
            event: The event object containing details about the executed job.
        """
        job_id = event.job_id
        job = self.scheduler.get_job(job_id)  # Retrieve the job object using its ID
        if job:
            job_name = job.func_ref.split(":")[0] if hasattr(job, "func_ref") else job.name
            logger.info(f"Scheduled job executed: Job ID={job_id}, Job Name={job_name}")
        else:
            logger.warning(f"Could not find job with ID={job_id}")
            
    def log_job_error(self, event):
        """
        Log when a scheduled job encounters an error.

        Args:
            event: The event object containing details about the failed job.
        """
        job_id = event.job_id
        job = self.scheduler.get_job(job_id)  # Retrieve the job object using its ID
        exception = event.exception
        traceback = event.traceback

        if job:
            job_name = job.func_ref.split(":")[0] if hasattr(job, "func_ref") else job.name
            logger.error(f"Scheduled job failed: Job ID={job_id}, Job Name={job_name}")
        else:
            logger.error(f"Scheduled job failed: Job ID={job_id}, Job not found")

        logger.error(f"Exception: {exception}")
        logger.error(f"Traceback: {traceback}")


async def start_scheduler():
    """
    Start the scheduler and handle its lifecycle.
    This function can be used in the FastAPI lifespan context.
    """
    scheduler = TaskScheduler()

    try:
        # Start the scheduler
        await scheduler.start()

        # Keep the scheduler running until explicitly canceled
        while True:
            await asyncio.sleep(3600)  # Sleep for an hour (or any long duration)
    except asyncio.CancelledError:
        # Gracefully shut down the scheduler on cancellation
        await scheduler.shutdown()