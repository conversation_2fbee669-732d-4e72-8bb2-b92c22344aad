.venv
__pycache__
*.egg-info
# setup.py

# Ignore dependencies
node_modules

# Ignore build outputs
.next
dist
out

# Ignore system files
.DS_Store
Thumbs.db

# Ignore version control
.git
.gitignore

# Ignore editor/IDE files
.vscode
.idea

# Docker-related files (optional, depends on structure)
Dockerfile
docker-compose.yml

# Logs and temporary files
npm-debug.log
yarn-debug.log
pnpm-debug.log
*.log
.env.local
.env.development.local
.env.test.local
.env.production.local