import asyncio
import aio_pika
import json
from loguru import logger
from aio_pika.abc import AbstractIncomingMessage


from core.config import settings
from message_bus.procedure.user import get_user_info

async def test_function(test_number: int):
    """A simple test function for RPC calls."""
    return {"result": f"This is a test function, test {test_number}"}

# A mapping of function names to their callable implementations for the RPC server.
RPC_FUNCTIONS = {
    "test_function": test_function,
    "get_user_info": get_user_info
}

async def process_request(message: aio_pika.IncomingMessage):
    """
    Processes an incoming RPC request message.

    It decodes the message body, identifies the requested function,
    executes it with the provided parameters, and prepares the response.

    Args:
        message (aio_pika.IncomingMessage): The incoming message from the queue.

    Returns:
        bytes: The encoded JSON response to be sent back to the client.
    """
    try:
        # Decode the incoming JSON request
        request_data = json.loads(message.body.decode())
        function_name = request_data.get("function")
        params = request_data.get("params", {})
        logger.info(f"Received RPC request: {function_name} | Reply to: {message.reply_to} | Correlation ID: {message.correlation_id}")

        # Fetch and execute the corresponding function from the RPC_FUNCTIONS map.
        rpc_func = RPC_FUNCTIONS.get(function_name)
        if rpc_func:
            result = await rpc_func(**params)
        else:
            result = {"error": f"Unknown function: {function_name}"}
            logger.error(result)

        # Ensure a reply-to queue is specified in the message properties.
        if not message.reply_to:
            logger.error("No reply_to queue specified, unable to send response")
            return

        # Prepare the response message body.
        response_body = json.dumps({"result": result}).encode()
        logger.info(f"Processed RPC: {function_name}")
        return response_body
    except Exception as e:
        logger.error(f"Error processing RPC request: {e}")
        # Return an error message if processing fails.
        return json.dumps({"result": "error: "+str(e)}).encode()

async def rpc_server(worker_id: int):
    """
    Sets up and runs a single RPC server worker.

    This worker connects to RabbitMQ, declares an exchange and a queue,
    binds the queue, and then listens for incoming RPC requests to process.

    Args:
        worker_id (int): A unique identifier for the worker instance.
    """
    logger.info(f"[RPC SERVER] Starting RPC server")
    try:
        # Establish a robust connection to RabbitMQ.
        connection = await aio_pika.connect_robust(
            f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"
        )
    except Exception as e:
        logger.error(f"[RPC SERVER] Worker {worker_id} Failed to connect to RabbitMQ: {e}")
        return
    
    async with connection:
        # Create a channel and declare the necessary exchange and queue.
        channel = await connection.channel()
        rpc_exchange = await channel.declare_exchange("rpc_exchange", type="direct", durable=True)
        queue = await channel.declare_queue("user_rpc_queue", durable=True)
        await queue.bind(rpc_exchange, routing_key="user.*")
 
        try:
            logger.info(f"[RPC SERVER] RPC worker {worker_id} is waiting for request")
            # Iterate over incoming messages from the queue.
            async with queue.iterator() as queue_iter:
                message: AbstractIncomingMessage
                async for message in queue_iter:
                    async with message.process(requeue=False):
                        # Process the request and get the response body.
                        response_body = await process_request(message)
                        # Publish the response back to the reply_to queue.
                        await rpc_exchange.publish(
                            aio_pika.Message(
                                body=response_body,
                                correlation_id=message.correlation_id,
                            ),
                            routing_key=message.reply_to,
                        )
                        logger.info(f"[RPC SERVER] RPC response returned successfully.")
        except asyncio.CancelledError:
            logger.info(f" [RPC SERVER] RPC worker {worker_id} of service has been shutdown.")
        finally:
            # Ensure the channel is closed gracefully.
            await channel.close()
        
async def start_rpc_server(worker_count: int = 5):
    """
    Starts and manages multiple RPC server workers.

    Args:
        worker_count (int): The number of concurrent RPC workers to start.

    Returns:
        A future that resolves when all workers have completed.
    """
    # Create a list of worker tasks.
    server = [rpc_server(i+1) for i in range(worker_count)]
    # Run all worker tasks concurrently.
    return await asyncio.gather(*server) 