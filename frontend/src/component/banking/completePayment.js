import React, { useEffect, useState } from 'react';
import { OnboardingHeaderPart } from '../../header/OnboardingHeader';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { FaCheckCircle } from 'react-icons/fa';
import Swal from 'sweetalert2';

const CompletePayment = () => {
    const [searchParams] = useSearchParams();
    const navigate = useNavigate();
    const roboman_api = process.env.REACT_APP_ROBOMAN_API;
    const [paymentData, setPaymentData] = useState(null);

    const fetchCheckoutInfo = async (sessionId) => {
        try {
            const response = await fetch(`${roboman_api}/stripe/checkout/session/${sessionId}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Authorization': localStorage.getItem('access_token'), // Replace with actual token
                },
            });

            const data = await response.json();

            if (!response.ok) {
                console.error('Failed to fetch session');
            } else {
                console.log('Session details');
                setPaymentData(data);
            }
        } catch (error) {
            console.error('Network or parsing error:', error);
        }
    };

    useEffect(() => {
        // Extract session_id from the query string
        const sessionId = searchParams.get('session_id');
        if (sessionId) {
            console.log('Session ID:', sessionId); // Log the session_id for debugging
            localStorage.setItem('session_id', sessionId); // Save session_id to localStorage
            fetchCheckoutInfo(sessionId) // Fetch payment data
        } else {
            console.error('No session_id found in the URL');
        }
    }, [searchParams]);

    const PaymentInfo = ({ paymentData }) => {
        const navigate = useNavigate();

        const handleBackToDashboard = () => {
            navigate('/dashboard');
        };

        // Trigger SweetAlert spinner when paymentData is null
        useEffect(() => {
            if (!paymentData) {
                Swal.fire({
                    title: 'Please wait...',
                    text: 'Loading payment information...',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                });
            } else {
                Swal.close(); // Close the spinner when paymentData is available
            }
        }, [paymentData]);

        return (
            <div className="flex flex-col items-center justify-center w-full max-w-lg bg-white p-8 rounded-lg shadow-lg">
                {/* Container 1: Big Green Tick and Success Message */}
                <div className="flex flex-col items-center mb-6">
                    <FaCheckCircle className="text-green-500 text-7xl mb-4" />
                    <h1 className="text-3xl md:text-4xl font-bold text-gray-800 text-center">
                        Payment Successful!
                    </h1>
                </div>

                <hr className="w-full border-gray-300 mb-6" />

                {/* Container 2: Payment Information */}
                <div className="flex flex-col items-center w-full mb-6">
                    <h2 className="text-xl font-semibold text-gray-700 mb-6 text-center">
                        Your Payment Information
                    </h2>
                    {paymentData ? (
                        <div className="grid grid-cols-2 gap-4 w-full max-w-md">
                            {/* Row 1: Account */}
                            <div className="text-left text-gray-600 font-semibold ml-5">Account:</div>
                            <div className="text-left text-gray-800">{paymentData.customer_details.email}</div>
                            {/* Row 2: Name */}
                            <div className="text-left text-gray-600 font-semibold ml-5">Name:</div>
                            <div className="text-left text-gray-800">{paymentData.customer_details.name}</div>
                            {/* Row 3: Payment */}
                            <div className="text-left text-gray-600 font-semibold ml-5">Payment:</div>
                            <div className="text-left text-gray-800">
                                £{(paymentData.amount_total / 100).toFixed(2)}
                            </div>
                        </div>
                    ) : (
                        <div className="text-gray-600 text-center">Loading payment information...</div>
                    )}
                </div>

                <hr className="w-full border-gray-300 mb-6" />

                {/* Container 3: Back Button */}
                <button
                    onClick={handleBackToDashboard}
                    className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-3 px-8 rounded-full transition duration-300 shadow-md"
                >
                    Back to Dashboard
                </button>
            </div>
        );
    };

    return (
        <div>
            {/* Render user data or other components here */}
            <div className="flex flex-col w-full bg-gray-100 min-h-screen items-center justify-center">
                {/* This part is for the header section */}
                <OnboardingHeaderPart isLogout={false} />
                {/* This part is for the main/body section */}
                <div className="flex-grow">
                    <PaymentInfo paymentData={paymentData}/>
                </div>
                {/* Footer is placed here to ensure it stays at the bottom */}
            </div>
        </div>
    );
}

export default CompletePayment;