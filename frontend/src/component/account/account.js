import React, { useState } from 'react';
import BillingHistorySection from './BillingHistorySection';
import HeaderMainPage from '../../header/HeaderMainPage';
import UserProfileSection from './UserProfileSection';
import { FaAngleDown, FaAngleUp } from 'react-icons/fa';

const Account = ({ user, onProfileSave }) => {
  const [currentView, setCurrentView] = useState('profile');
  const [hoveredMenu, setHoveredMenu] = useState(null);

  const navItems = [
    { label: 'Billing & Usage', view: 'billing', icon: '💳' },
    {
      label: 'Account & Settings',
      view: 'account-settings',
      icon: '⚙️',
      subItems: [
        { label: 'Profile', view: 'profile' },
        { label: 'Workspace & Members', view: 'workspace-members' },
        { label: 'Workspace Group', view: 'workspace-group' },
        { label: 'Lead Labels', view: 'lead-labels' },
        { label: 'Custom Tags', view: 'custom-tags' },
        { label: 'Website Visitors', view: 'website-visitors' },
        { label: 'Agency', view: 'agency' },
        { label: 'Audit Logs', view: 'audit-logs' },
      ],
    },
    { label: 'Preferences', view: 'preferences', icon: '🌙' },
    { label: 'Integrations', view: 'integrations', icon: '🔗' },
    { label: 'Blocklist', view: 'blocklist', icon: '⛔' },
    { label: 'Advanced Deliverability', view: 'advanced-deliverability', new: true, icon: '🚀' },
  ];

  const handleNavigate = (view) => setCurrentView(view);
  if (!user) return null;

  return (
    <div className="bg-gradient-to-br from-indigo-50 via-white to-blue-100 min-h-screen flex flex-col font-sans">
      <HeaderMainPage />
      <div className="flex flex-1 max-w-7xl mx-auto w-full pt-12 gap-10 px-8">
        {/* Sidebar Navigation */}
        <nav className="w-64 bg-white shadow-xl rounded-3xl border border-blue-100 py-8 px-5 flex flex-col gap-2 sticky top-10 h-fit">
          {navItems.map((item) => (
            <div
              key={item.view}
              onMouseEnter={() => item.subItems && setHoveredMenu(item.view)}
              onMouseLeave={() => item.subItems && setHoveredMenu(null)}
              className="relative"
            >
              {item.subItems ? (
                <>
                  <button
                    className={`flex items-center justify-between w-full px-3 py-2 font-semibold rounded-xl transition-all ${
                      currentView === item.view ||
                      item.subItems.some((sub) => sub.view === currentView)
                        ? 'bg-blue-50 text-blue-800 shadow'
                        : 'text-gray-600 hover:bg-blue-100'
                    }`}
                    onClick={() => handleNavigate(item.subItems?.view)}
                  >
                    <span className="flex items-center gap-2">{item.icon} {item.label}</span>
                    {hoveredMenu === item.view ? <FaAngleUp /> : <FaAngleDown />}
                  </button>
                  {/* Dropdown */}
                  <ul
                    className={`absolute left-full top-0 ml-2 bg-white border border-blue-100 shadow-xl rounded-xl min-w-max overflow-hidden transition-all duration-200 z-20 ${
                      hoveredMenu === item.view
                        ? 'opacity-100 translate-x-0 pointer-events-auto'
                        : 'opacity-0 -translate-x-3 pointer-events-none'
                    }`}
                  >
                    {item.subItems.map((subItem) => (
                      <li key={subItem.view}>
                        <button
                          className={`w-full text-left px-6 py-2 text-base transition ${
                            currentView === subItem.view
                              ? 'bg-blue-50 text-blue-700 font-semibold'
                              : 'text-gray-700 hover:bg-blue-50'
                          }`}
                          onClick={() => handleNavigate(subItem.view)}
                        >
                          {subItem.label}
                        </button>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <button
                  onClick={() => handleNavigate(item.view)}
                  className={`flex items-center w-full px-3 py-2 font-semibold rounded-xl transition-all ${
                    currentView === item.view
                      ? 'bg-blue-50 text-blue-800 shadow'
                      : 'text-gray-600 hover:bg-blue-100'
                  }`}
                >
                  {item.icon} {item.label}
                  {item.new && (
                    <span className="ml-2 bg-yellow-100 text-yellow-700 text-xs px-2 py-0.5 rounded-full font-medium">
                      New
                    </span>
                  )}
                </button>
              )}
            </div>
          ))}
        </nav>

        {/* Main Content */}
        <main className="flex-1 bg-white rounded-3xl shadow-xl border border-blue-100 p-10 min-h-[70vh]">
          {currentView === 'profile' && <UserProfileSection user={user} onSave={onProfileSave} />}
          {currentView === 'billing' && <BillingHistorySection billingHistory={user.billingHistory} />}
          {currentView !== 'profile' && currentView !== 'billing' && (
            <div className="flex flex-col items-center justify-center h-full text-gray-400 text-lg py-32">
              <span className="text-5xl mb-4">🚧</span>
              <p>
                Content for "
                {
                  navItems.find(
                    (item) =>
                      item.view === currentView ||
                      (item.subItems && item.subItems.some((sub) => sub.view === currentView))
                  )?.label || "this"
                }
                " section coming soon!
              </p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default Account;
