<h1 style="font-size: 36px; font-family: <PERSON><PERSON>, sans-serif; text-align: center;">
  ROBOMAN BACKEND
</h1>

Welcome to the documentation for the **ROBOMAN Backend** project. This document provides an overview of the project, setup instructions, usage guidelines, and other relevant information.

# Table of Contents

1. [Introduction](#introduction)
    
    - 1.1. [Project Overview](#project-overview)

    - 1.2. [Goals](#goals)

2. [Features](#features)

    - 2.1. [Key Features](#key-features)

    - 2.2. [Architecture](#architecture)

3. [Installation](#installation)

    - 3.1. [Prerequisites](#prerequisites)

    - 3.2. [Installation Steps](#installation-steps)

4. [API Reference](#api-reference)

5. [Contribution](#contribution)
    
    - 5.1. [How to contribute](#how-to-contribute)

    - 5.2. [Code of Conduct](#code-of-conduct)

# Introduction

## Project Overview

The **ROBOMAN Backend** is significanly designed to meet the growing demands of the ROBOMAN application. This version leverages a microservices architecture, which allows for better scalability, easier debugging, and simplified maintenance. Additionally, it is designed to be more robust and flexible, making it easier to add new features in the future.

## Goals

The primary goals of the ROBOMAN Backend project are:

- **Scalability**: To ensure the backend can handle a growing number of users and data by leveraging microservices architecture.
- **Performance**: To provide fast and efficient processing of requests.
- **Reliability**: To maintain high availability and fault tolerance.
- **Security**: To protect user data and ensure secure communication.
- **Maintainability**: To create a codebase that is easy to maintain and extend.

# Features

## Key Features

The ROBOMAN Backend includes the following key features:

- **User Authentication and Authorization**: Secure user login and role-based access control.

- **Data Management**: Efficient handling of user data, preferences, and settings.

- **API Services**: RESTful APIs for interacting with the frontend and other services.

- **Real-time Communication**: Support for real-time updates and notifications.

- **Logging and Monitoring**: Comprehensive logging and monitoring for debugging and performance analysis.

- **Microservices Architecture**: Independent services that can be deployed and scaled separately.

- **Fault Tolerance**: Improved fault isolation to ensure system reliability.

- **Extensibility**: Modular design that makes it easy to add new features and services.

The backend system is composed of the following four main services:

1. **AI Engine**: 
    - Handles all AI-related features, including large language models (LLMs) and embeddings.
    - Centralizes heavy AI processing tasks to optimize performance and resource usage.

2. **Campaign Service**: 
    - Manages chat sessions, including creating, deleting campaigns.
    - Handles usage management based on time, ensuring efficient campaign control.

3. **User Service**: 
    - Manages user-related operations such as creating users, deleting users, and changing user roles.
    - Ensures secure and efficient user management.

4. **Gateway Service**: 
    - Acts as an entry point to the microservices system, routing requests to the appropriate services (AI Engine, Campaign, User).
    - Handles authentication and ensures secure communication between services.

## Architecture

<p align="center">
  <img src="./assets/roboman.png" alt="Architecture Diagram">
</p>

# Installation

## Prerequisites

Before installing the Neurond Assistant Backend v2, ensure you have the following prerequisites:

- **Docker** (for containerization)
- **Docker Compose** (for managing multi-container Docker applications)
- **Git** (for version control)

## Installation Steps

Follow these steps to install the Neurond Assistant Backend v2:

1. **Clone the Repository**:
    ```sh
    git clone <repository URL>

    cd ROBOMAN
    ```

2. **Build and Start the Application**:
    The easiest way to build and start the application is by using Docker Compose. Run the following command:
    ```sh
    docker-compose up --build
    ```
    Run this if you want to remove all containers and volume 
    ```sh
    docker-compose down -v
    ```

3. **Verify Installation**:
    Open your browser and navigate to the following URLs to verify that the application is running:
    - `http://localhost:8001/docs`: for Gateway
    - `http://localhost:8002/docs`: for User
    - `http://localhost:8004/docs`: for Campaign
    - `http://localhost:8005/docs`: for AI Engine

# API Reference

Refer to the following documentation for detailed usage and API reference for different services:

1. **AI Engine Service Documentation**: [AI Engine Service](./aiengine/README.md) (to be updated)
2. **User Service API Documentation**: [User Service API](link-to-data-management-service-api-doc) (to be updated)
3. **Chat Service API Documentation**: [Campaign Service API](link-to-notification-service-api-doc) (to be updated)
4. **Gateway Service API Documentation**: [Gateway Service API](link-to-authentication-service-api-doc) (to be updated)

Each documentation provides comprehensive details on the available endpoints, request and response formats, authentication mechanisms, and usage examples for the respective services.

# Contribution

## How to Contribute

We welcome contributions to the ROBOMAN project. To ensure a smooth and efficient development process, please follow these guidelines:

1. **Create Your Own Branch**:
    - You MUST create your own branch for development from the branch `uat`.
    - Use the following commands to create your branch:
      ```sh
      git checkout uat
      git checkout -b your-branch-name
      ```

2. **Do Not Push Directly to Other Branches**:
    - You MUST not push your code directly into other branches besides your own branch, especially `uat`.

3. **Update Your Branch**:
    - Before pushing your code for a pull request, make sure your code is up-to-date with the `uat` branch. Don't worry if you don't know how; you only need to remember these magic commands:
        ```sh
        git log --oneline # This special command is your best friend
        git fetch
        git pull origin uat
        git log --oneline # I meant it
        git status
        ```
    - One final thing, Git convention is REALLY important. Please check the [Git convention documentation](https://www.conventionalcommits.org/en/v1.0.0/) before making any commit. Adhering to these conventions ensures a clean and manageable codebase.
        
        For example:
        ```sh
        git commit -m "feat: your new feature" # If you add new feature
        git commit -m "fix: your bug" # If you fix a bug
        git commit -m "ref: refactor description" # If you refactor code for better performance
        git commit -m "chore: what ever" # If your action isn't impact anything (or you just want other members ignore this commit)
        git commit -m "doc: document" # If you add or change anything in documentation or requirements
        ```

4. **Submit a Pull Request**:
    - The best practice is to push your code to your own branch and then submit a pull request to `uat`.
    - Ensure that your pull request is reviewed and approved before merging.

5. **Code Review**:
    - Ask for a code review from one of the project maintainers to ensure that your changes meet the project's standards and do not introduce any issues.

## Code of Conduct

To ensure a welcoming and inclusive environment, we expect all contributors to adhere to our Code of Conduct:

- **Be Respectful**: Treat everyone with respect and consideration.
- **Be Collaborative**: Work together to achieve common goals.
- **Be Inclusive**: Welcome and support people of all backgrounds and identities.
- **Be Professional**: Conduct yourself in a professional manner at all times.

By following these guidelines, you help maintain the integrity and quality of the codebase. Thank you for your contributions!