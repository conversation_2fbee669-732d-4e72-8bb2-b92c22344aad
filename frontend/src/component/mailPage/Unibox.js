import React, {useState, useEffect} from 'react';
import { useNavigate } from 'react-router-dom';
import HeaderMainPage from '../../header/HeaderMainPage';
import { FaReply, FaSearch } from "react-icons/fa";
import Swal from "sweetalert2";
import { FaArrowLeft, FaInfoCircle } from 'react-icons/fa';
import ReactQuill from 'react-quill';
// import "react-quill/dist/quill.snow.css";
import 'quill/dist/quill.snow.css';

// Define the modules for ReactQuill editor
const modules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ["bold", "italic", "underline", "strike"],
    [{ list: "ordered" }, { list: "bullet" }],
    [{ script: "sub" }, { script: "super" }],
    [{ indent: "-1" }, { indent: "+1" }],
    [{ direction: "rtl" }],
    [{ color: [] }, { background: [] }],
    ["blockquote", "code-block"],
    ["link", "image"],
    ["clean"],
  ],
};

// Define column widths
const columnWidths = {
    star: '50px',
    email: '150px',
    date: '100px',
};

const ReplyBox = ({ replyHistory, handleCloseRepSuggest, activeTab, AutoReply }) => {
    // Extract the last reply from the history, showing analysis on the last reply
    // to help user understand the email, including th

    const lastReply = replyHistory.at(-1); // Get last item
    const subject = lastReply?.suggested_response?.subject || "";
    const body = lastReply?.suggested_response?.body || "";
    const explanation = lastReply?.explanation || "No Explanation";

    // State for showing explanation box
    const [showExplanation, setShowExplanation] = useState(false);

    const AutoPasteReply = () => {
        AutoReply(activeTab, subject, body)
    }

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50 bg-gray-500 bg-opacity-50 animate-fadeIn">
            <div className="relative flex">
                {/* Main Reply Box */}
                <div className="bg-white p-4 rounded-md shadow-lg border w-[600px] animate-fadeInUp relative">
                    {/* Subject Input */}
                    {activeTab === "mail" && (
                        <div className="mb-2">
                            <label className="block text-md font-medium text-gray-700 mb-1">Subject:</label>
                            <input
                                type="text"
                                value={subject}
                                readOnly
                                className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-600 bg-gray-100"
                            />
                        </div>
                    )}

                    {/* Message Input */}
                    <div className="mb-2">
                        <label className="block text-md font-medium text-gray-700 mb-1">
                            {activeTab === "mail" ? "Content" : activeTab === "linkedin" ? "Message" : "Content"}
                        </label>
                        <ReactQuill
                            theme="snow"
                            value={body}
                            modules={{ toolbar: false }}
                            readOnly={true}
                            className="h-[400px] rounded-md mb-[10px] bg-gray-100"
                        />
                    </div>

                    {/* Buttons: Close & Star */}
                    <div className="flex justify-end gap-2 mt-2">
                        <button
                            className="bg-gray-300 w-30 text-black px-4 py-2 rounded-full hover:bg-gray-400"
                            onClick={handleCloseRepSuggest}
                        >
                            Close
                        </button>

                        <button
                            className="px-4 py-2 rounded-full border border-gray-500 text-gray-700 shadow-md bg-white hover:bg-gray-700 hover:text-white transition-all"
                            onClick={AutoPasteReply}
                        >
                            Reply ✨
                        </button>

                        {/* Star/Explain Button */}
                        <button
                            className="px-4 py-2 rounded-full border border-gray-500 text-gray-700 shadow-md bg-white hover:bg-gray-700 hover:text-white transition-all"
                            // onClick={() => setShowExplanation(!showExplanation)}
                            onMouseEnter={() => setShowExplanation(true)}
                            onMouseLeave={() => setShowExplanation(false)}
                        >
                            Explain ✨
                        </button>
                    </div>
                </div>

                {/* Explanation Box - Positioned to the right */}
                {showExplanation && (
                    <div className="absolute top-0 right-[-420px] w-[400px] p-3 border border-gray-400 rounded-lg shadow-md bg-white animate-fadeIn transition-opacity">
                        <label className="block text-md font-medium text-gray-700 mb-1">Explanation ✨</label>
                        <textarea
                            value={explanation}
                            readOnly
                            className="w-full h-[500px] border border-gray-300 rounded-md p-2 bg-gray-100 resize-none"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

const MailboxContent = ({ activeTab }) => {

    const roboman_api = process.env.REACT_APP_ROBOMAN_API;
    const campaignId = localStorage.getItem("campaign_id");
    const [selectedCompany, setSelectedCompany] = useState(null);
    const [replyBox, setReplyBox] = useState(false);
    const [replyMailSubject, setReplyMailSubject] = useState("");
    const [replyMailContent, setReplyMailContent] = useState("");
    const [sentMails, setSentMails] = useState([]);
    const [replyBoxSuggest, setReplyBoxSuggest] = useState(false);

    const [replyLinkedInContent, setReplyLinkedInContent] = useState("");

    const [selected_internal_id, setSelectedInternalID] = useState(null);
    const [replyHistory, setReplyHistory] = useState([]);
    const [selectedMail, setSelectedMail] = useState([]);
    const [linkedInMessages, setLinkedInMessage] = useState([]);
    const [linkedinMessHistory, setLinkedInMessHistory] = useState([]);
    const [selectedLinkedin, setSelectedLinkedin] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const authToken = localStorage.getItem("access_token");

    const [showMailPanel, setMailDataPanel] = useState(false);
    const [selectMailName, setSelectMailName] = useState(null);
    const [mailData, setMailData] = useState(null);

    const [showLinkedinPanel, setLinkedinDataPanel] = useState(false);
    const [selectLinkedInName, setSelectLinkedinName] = useState(null);
    const [LinkedinData, setLinkedinData] = useState(null);
    


    const fetchSentMails = async () => {
        /* 
        * Fetch sent emails from the API 
        */
        try {
            const response = await fetch(`${roboman_api}/campaign/${campaignId}/emails/sent-mails`, {
                method: "GET",
                headers: {
                    "accept": "application/json",
                    "Authorization": authToken,
                },
            });

            if (!response.ok) {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Failed to fetch sent emails.",
                    confirmButtonText: "OK"
                });
                return; // Exit the function early
            }

            const data = await response.json();
            console.log("Fetched sent mails:");
            setSentMails(data);
        } catch (error) {
            Swal.fire({
                title: "Error!",
                text: error.message,
                icon: "error",
                confirmButtonText: "Okay"
            });
        }
    };

    const fetchSentLinkedin = async () => {
        /*
        * Fetch sent LinkedIn messages from the API
        */
        try {
            const response = await fetch(`${roboman_api}/campaign/${campaignId}/linkedin/message/sent-messages`, {
                method: "GET",
                headers: {
                    "accept": "application/json",
                    "Authorization": authToken,
                },
            });

            if (!response.ok) {
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: "Failed to fetch sent LinkedIn messages.",
                    confirmButtonText: "OK"
                });
                return;
            }

            const data = await response.json();
            console.log("Fetched sent LinkedIn messages:");
            setLinkedInMessage(data);
        } catch (error) {
            Swal.fire({
                title: "Error!",
                text: error.message,
                icon: "error",
                confirmButtonText: "Okay"
            });
        }
    };

    useEffect(() => {
        // Fetch sent emails and LinkedIn messages when the component mounts
        console.log("Switch to tab:", activeTab)
        if (activeTab === "mail") {
            fetchSentMails();
            setReplyBox(false)
            setReplyLinkedInContent("")
            setReplyMailSubject("")
            setReplyMailContent("")

        } else if (activeTab === "linkedin") {
            fetchSentLinkedin();
            setReplyBox(false)
            setReplyLinkedInContent("")
            setReplyMailSubject("")
            setReplyMailContent("")
        }
    }, [activeTab])

    const handleShowMailData = (mail_data, mail_name) => {
        // Show the mail data panel
        if (showMailPanel===false) setMailDataPanel(true);
        setMailData(mail_data)
        setSelectMailName(mail_name)
    }

    const handleMailReplyHistory = async (company_id, internal_id) => {
        /*
        * Fetch reply history for a specific email
        */

        try {
            Swal.fire({
                title: 'Fetching Email Reply...',
                text: 'Please wait while the Email history is being loaded.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading(); // Show loading spinner
                },
            });

            const response = await fetch(`${roboman_api}/campaign/companies/${company_id}/emails/conversation`,
                {
                    method: "GET",
                    headers: {
                        "Accept": "application/json",
                        "Authorization": authToken, // Replace with your actual token
                    },
                }
            );
    
            if (!response.ok) {
                console.error(`Error: ${response.status}`);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: `Failed to load email reply history. Please try again`,
                });
                return;
            }
    
            const data = await response.json();
            const matching_mail_history = data.filter(item => item.sent_email_internal_id === internal_id || item.internal_id === internal_id);
            console.log("email history fetched")
            if (matching_mail_history) {
                setReplyHistory(matching_mail_history); // Set the matching item to state
            } else {
                setReplyHistory([]); // Set to null if no match is found
            }

            Swal.close();

        } catch (error) {
            console.error("Failed to fetch mail reply history:", error);
             // Show error alert
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Failed to load email reply history. Please try again later.",
            });
        }
    };

    const handleMailReplyHis = async (company_id, mail_rep_name, mail_rep_info) => {
        /*
        * Fetch reply history for a specific email
        */
        setReplyMailSubject("");
        setReplyMailContent("");
        setReplyBox(false);
        
        try {
            Swal.fire({
                title: 'Fetching Email Reply...',
                text: 'Please wait while the Email history is being loaded.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading(); // Show loading spinner
                },
            });

            const response = await fetch(`${roboman_api}/campaign/companies/${company_id}/emails/conversation`,
                {
                    method: "GET",
                    headers: {
                        "Accept": "application/json",
                        "Authorization": authToken, // Replace with your actual token
                    },
                }
            );
    
            if (!response.ok) {
                console.error(`Error: ${response.status}`);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: `Failed to load email reply history. Please try again`,
                });
                return;
            }
    
            const data = await response.json();
            console.log("email history fetched")
            if (data) {
                setReplyHistory(data); // Set the matching item to state
            } else {
                setReplyHistory([]); // Set to null if no match is found
            }
            Swal.close();

        } catch (error) {
            console.error("Failed to fetch mail reply history:", error);
             // Show error alert
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Failed to load email reply history. Please try again later.",
            });
        }

        setSelectedMail([mail_rep_name, mail_rep_info, company_id]);
    };

    const handleShowingEmailReply = (company_id, internal_id, mail_subject, mail_to_info, fisrt_mail_body) => {
        // Show the mail data panel
        setSelectedInternalID(internal_id);
        setReplyBox(false); // Hide reply box when switching emails
        handleMailReplyHistory(company_id, internal_id); // Fetch reply history for the selected email
        setSelectedMail([mail_subject, mail_to_info, fisrt_mail_body, company_id]);
    };

    const handleLinkedInReplyHistory = async (company_id) => {
        /*
        * Fetch reply history for a specific LinkedIn message
        */
        try {
            Swal.fire({
                title: 'Fetching LinkedIn Messages...',
                text: 'Please wait while the LinkedIn message history is being loaded.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading(); // Show loading spinner
                },
            });
            
            const response = await fetch(`${roboman_api}/campaign/companies/${company_id}/linkedin/messages/chat`,
                {
                    method: "GET",
                    headers: {
                        "Accept": "application/json",
                        "Authorization": authToken,
                    },
                }
            );
    
            if (!response.ok) {
                console.error(`Error: ${response.status}`);
                Swal.fire({
                    icon: "error",
                    title: "Error",
                    text: `Failed to load linkedin message history. Please try again`,
                });
                return;
            }
    
            const data = await response.json();

            console.log("linkedin message history fetched")
            if (data) {
                setLinkedInMessHistory(data); // Set the matching item to state
            } else {
                setLinkedInMessHistory([]); // Set to null if no match is found
            }
            Swal.close();
        } catch (error) {
            console.error("Failed to fetch mail reply history:", error);
             // Show error alert
            Swal.fire({
                icon: "error",
                title: "Oops...",
                text: "Failed to load linkedin message history. Please try again later.",
            });
        }
    };

    const handleShowingLinkedinReply = (company_id, rep_name) => {
        // Show the mail data panel
        setReplyBox(false); // Hide reply box when switching emails
        handleLinkedInReplyHistory(company_id); // Fetch reply history for the selected email
        setSelectedLinkedin([rep_name, company_id]);
    };

    const handleMailReply = async () => {
        /*
        * Send a reply to the selected email
        */
        if (!replyMailSubject.trim() || !replyMailContent.trim()) {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Information',
                text: 'Please enter both subject and content before replying.',
            });
            return;
        }

        try {
            Swal.fire({
                title: 'Sending Reply...',
                text: 'Please wait while your reply is being sent.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            const response = await fetch(`${roboman_api}/campaign/emails/reply-mails/respond`, {
                method: 'POST',
                headers: {
                    'accept': 'application/json',
                    'Authorization': authToken,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: selectedMail[2],
                    subject: replyMailSubject,
                    body: replyMailContent,
                }),
            });
    
            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Reply Sent',
                    text: 'Your reply has been successfully sent.',
                });
    
                // Clear form
                setReplyMailSubject("");
                setReplyMailContent("");
                setReplyBox(false);
            } else {
                const err = await response.json();
                Swal.fire({
                    icon: 'error',
                    title: 'Failed to Send',
                    text: err?.message || 'Something went wrong while sending the reply.',
                });
            }
    
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error.message || 'Something went wrong.',
            });
        }
    };

    const handleLinkedReply = async () => {
        /*
        * Send a reply to the selected LinkedIn message
        */
        if (!replyLinkedInContent.trim()) {
            Swal.fire({
                icon: 'warning',
                title: 'Missing Information',
                text: 'Please enter your message before replying.',
            });
            return;
        }
    
        try {
            Swal.fire({
                title: 'Sending Reply...',
                text: 'Please wait while your reply is being sent.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            const response = await fetch(`${roboman_api}/campaign/linkedin/message/respond`, {
                method: 'POST',
                headers: {
                    'accept': 'application/json',
                    'Authorization': authToken,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: selectedLinkedin[1],
                    message_content: replyLinkedInContent,
                }),
            });

            // Clear form
            setReplyLinkedInContent("");
            setReplyBox(false);
    
            if (response.ok) {
                Swal.fire({
                    icon: 'success',
                    title: 'Reply Sent',
                    text: 'Your reply message has been successfully sent.',
                });
            } else {
                const err = await response.json();
                Swal.fire({
                    icon: 'error',
                    title: 'Failed to Send',
                    text: err?.message || 'Something went wrong while sending the reply.',
                });
            }
    
        } catch (error) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: error.message || 'Something went wrong.',
            });
        }
    };

    const handleDiscard = () => {
        // Function to discard reply
        setReplyBox(false);
        setReplyMailSubject("");
        setReplyMailContent("");
        setReplyLinkedInContent("");
    };

    const handleCloseRepSuggest = () => {
        setReplyBoxSuggest(false);
    };

    const parseHTML = (htmlString) => {
        // Safely parse HTML string and extract body content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlString, "text/html");
        return doc.body.innerHTML; // Extracts only the body content safely
    };

    const filteredSentMails = sentMails.filter(email => {
        if (activeTab !== 'mail') return true; // Only filter when "Mail" tab is active
        
        const searchLower = searchTerm.toLowerCase();
    
        // Check if the search term matches the `rep_name`
        const nameMatch = email.rep_name?.toLowerCase().includes(searchLower);

        // Check if the search term matches the `rep_email`
        const emailMatch = email.rep_email?.toLowerCase().includes(searchLower);
    
        return nameMatch || emailMatch;
    });

    const filteredSentLinkedinMesssage = linkedInMessages.filter(linkedinmess => {
        if (activeTab !== 'linkedin') return true; // Only filter when "Mail" tab is active
        
        const searchLower = searchTerm.toLowerCase();
    
        // Check if the search term matches the `rep_name`
        const nameMatch = linkedinmess.rep_name?.toLowerCase().includes(searchLower);
    
        return nameMatch;
    });

    const ToggleButton = ({ label, content, type }) => {
        // State to manage the open/close state of the button
        const [isOpen, setIsOpen] = useState(false);
        
        let buttonStyles = "bg-white border-2 text-[#223F9E] px-4 py-2 min-h-[40px] rounded-full shadow-md transition-all text-left border-2 border-[#223F9E] hover:bg-gray-200";
        let contentBoxStyles = "mt-2 p-3 rounded shadow-lg z-30 text-left border-2 border-[#223F9E] bg-white";
        let contentStyles = "text-sm text-left";
    
        if (type === "sentiment") {
            if (isOpen) {
                if (content === "positive") {
                    buttonStyles = "bg-green-500 text-white border-2 px-4 py-2 min-h-[40px] rounded-full shadow-md transition-all text-left";
                } else if (content === "negative") {
                    buttonStyles = "bg-red-500 text-white border-2 px-4 py-2 min-h-[40px] rounded-full shadow-md transition-all text-left";
                } else {
                    buttonStyles = "bg-gray-300 text-black border-2 px-4 py-2 min-h-[40px] rounded-full shadow-md transition-all text-left";
                }
            }
        } else if (type === "emotion") {
            contentBoxStyles += " border border-blue-500 text-black w-full";
            contentStyles += " whitespace-pre-wrap";
        } else if (type === "key_takeaway") {
            contentBoxStyles += " text-black border border-gray-400 w-full";
        }
    
        return (
            <div className="relative inline-block w-auto">
                <button
                    onClick={() => setIsOpen(!isOpen)}
                    className={buttonStyles}
                >
                    {isOpen && type === "sentiment" ? (content === "positive" ? "Positive" : "Negative") : label}
                </button>
                
                {isOpen && type !== "sentiment" && (
                    <div className={contentBoxStyles}>
                        <p className={contentStyles}>{content}</p>
                    </div>
                )}
            </div>
        );
    };

    const AutoReply = (activeTab, subject, content) => {
        if (activeTab === "mail") {
            setReplyMailSubject(subject)
            setReplyMailContent(content)
        } else {
            setReplyLinkedInContent(content)
        }

        setReplyBoxSuggest(false);
        setReplyBox(true);
    }

    return (
        <div className="flex flex-row gap-4 flex-1 overflow-hidden">
            {/* Left Panel: Email List */}
            <div className="w-1/3 bg-white p-2 space-x-1 rounded-md shadow-sm h-full">
                {/* Top Box with Search and Dropdown */}
                <div className="bg-white mb-2 rounded-md shadow-sm">
                    <div className="flex items-center space-x-1">
                        {/* Search Bar */}
                        <input
                            type="text"
                            placeholder="Search Email or Name..."
                            className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />

                        {/* Dropdown for Sorting Options */}
                        <select className="px-4 py-2 text-sm border border-gray-300 rounded-md">
                            <option value="date">Date</option>
                            <option value="name">Name</option>
                        </select>
                    </div>
                </div>

                {/* Email & LinkedIn List */}
                <div className="bg-gray-100 p-2 rounded-md h-[calc(100vh-230px)] overflow-y-auto">
                    {/* Email Panel */}
                    {activeTab === "mail" ? (
                    <div className="flex flex-col">
                        <div>
                            {/* Title */}
                            <h2 className="text-lg font-semibold text-gray-900 mt-2 mb-2 px-2">Inbox Email</h2>

                            {/* Scrollable Email List */}
                            <div className="overflow-y-auto h-full rounded-md p-2">
                            {!filteredSentMails || filteredSentMails.length === 0 ? (
                                <p className="text-center text-gray-500">No Email Sent</p>
                            ) : (
                                <div className="grid grid-cols-1 gap-4">
                                    {filteredSentMails.map((mail, index) =>
                                        mail.rep_name || mail.rep_info ? (
                                        <div
                                            key={index}
                                            className={`p-4 bg-white rounded-md shadow-sm border cursor-pointer hover:bg-gray-200`}
                                            onClick={() => handleMailReplyHis(mail.company_id, mail.rep_name, mail.rep_email)}
                                            // onClick={() => handleShowMailData(mail.sent_emails, mail.rep_name)}
                                        >
                                            <div className="flex justify-between items-center">
                                                <div className="text-lg font-medium text-gray-900"> {mail.rep_name} </div>
                                                <div className="text-sm text-gray-500"> {mail.rep_email} </div>
                                            </div>
                                        </div>
                                        ) : (
                                        <div key={index} className="p-4 bg-white rounded-md shadow-sm border cursor-pointer">
                                            <p className="text-center text-gray-500">Invalid Email Data</p>
                                        </div>
                                        )
                                    )}
                                </div>
                            )}
                            </div>
                        </div>

                        {/* {showMailPanel === false ? (
                        <div>
                            <h2 className="text-lg font-semibold text-gray-900 mt-2 mb-2 px-2">Inbox Email</h2>
                            <div className="overflow-y-auto h-full rounded-md p-2">
                            {!filteredSentMails || filteredSentMails.length === 0 ? (
                                <p className="text-center text-gray-500">No Email Sent</p>
                            ) : (
                                <div className="grid grid-cols-1 gap-4">
                                    {filteredSentMails.map((mail, index) =>
                                        mail.rep_name || mail.rep_info ? (
                                        <div
                                            key={index}
                                            className={`p-4 bg-white rounded-md shadow-sm border cursor-pointer hover:bg-gray-200`}
                                            // onClick={() => handleEmailClick(mail.internal_id, mail.subject, mail.to_info)}
                                            onClick={() => handleShowMailData(mail.sent_emails, mail.rep_name)}
                                        >
                                            <div className="flex justify-between items-center">
                                                <div className="text-lg font-medium text-gray-900"> {mail.rep_name} </div>
                                                <div className="text-sm text-gray-500"> {mail.rep_email} </div>
                                            </div>
                                        </div>
                                        ) : (
                                        <div key={index} className="p-4 bg-white rounded-md shadow-sm border cursor-pointer">
                                            <p className="text-center text-gray-500">Invalid Email Data</p>
                                        </div>
                                        )
                                    )}
                                </div>
                            )}
                            </div>
                        </div>
                        ) : (
                        <div className="bg-white p-4 rounded-md shadow-md w-full z-50">
                            <div className="flex items-center space-x-4 mb-5">
                                <button  className="text-gray-600 hover:text-gray-900 rounded-full bg-gray-200 p-2"
                                        onClick={() => {
                                            setMailDataPanel(false);
                                            setSelectedMail([]);
                                            setReplyHistory([]);
                                        }} 
                                >
                                    <FaArrowLeft className="h-4 w-4" />
                                </button>
                                <div>
                                    <h2 className="text-xl font-bold text-gray-900"> Email to {selectMailName || "Unknown"} </h2>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 gap-4">
                                {mailData && mailData.length > 0 && mailData.map((email, index) => (
                                    <div
                                        key={index}
                                        className="p-4 bg-gray-100 rounded-md shadow-sm border cursor-pointer hover:bg-gray-200"
                                        onClick={() => handleShowingEmailReply(email.company_id, email.internal_id, email.subject, email.to_info, email.body)}
                                    >
                                        <div className="text-lg font-medium text-gray-900">
                                        {email.to_info[0]?.display_name || "Unknown"}
                                        </div>

                                        <div className="text-sm text-gray-500">
                                        {new Date(email.created_at).toLocaleString()}
                                        </div>

                                        <div className="text-sm text-gray-700 mt-2">
                                        {email.body_plain.length > 100
                                            ? `${email.body_plain.substring(0, 100)}...`
                                            : email.body_plain}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                        )} */}
                    </div>
                    ) : (
                    // LinkedIn Tab - Under Development
                    <div className="flex flex-col h-full">
                        
                        <div>
                            {/* Title */}
                            <h2 className="text-lg font-semibold text-gray-900 mt-2 mb-2 px-2">Inbox LinkedIn</h2>

                            {/* Scrollable Linkedin List */}
                            <div className="overflow-y-auto h-full rounded-md p-2">
                            {!filteredSentLinkedinMesssage || filteredSentLinkedinMesssage.length === 0 ? (
                                <p className="text-center text-gray-500">No LinkedIn Message</p>
                            ) : (
                                <div className="grid grid-cols-1 gap-4">
                                    {filteredSentLinkedinMesssage.map((lnk_mess, index) =>
                                        lnk_mess.rep_name || lnk_mess.rep_linkedin_address ? (
                                        <div
                                            key={index}
                                            className={`p-4 bg-white rounded-md shadow-sm border cursor-pointer hover:bg-gray-200`}
                                            onClick={() => handleShowingLinkedinReply(lnk_mess.company_id, lnk_mess.rep_name, lnk_mess.sent_messages)}
                                        >
                                            <div className="flex justify-between items-center">
                                                <div className="text-lg font-medium text-gray-900"> {lnk_mess.rep_name} </div>
                                            </div>
                                        </div>
                                        ) : (
                                        <div key={index} className="p-4 bg-white rounded-md shadow-sm border cursor-pointer">
                                            <p className="text-center text-gray-500">Invalid Linkedin Data</p>
                                        </div>
                                        )
                                    )}
                                </div>
                            )}
                            </div>
                        </div>
                    
                    </div>
                    )}
                </div>
            </div>

            {/* Right Panel: Mail & LinkedIn History */}
            <div className="w-2/3 flex flex-col max-h-[800px]">
                {activeTab === "mail" ? (
                    <>
                        {/* Information Box */}
                        <div className="bg-white px-4 py-1 rounded-md shadow-sm flex flex-col">
                            {selectedMail.length > 0 && (
                                <div className="">
                                    <div className="flex items-center justify-between p-2 rounded-full shadow-sm mb-2">
                                        {/* Subject and Receiver Info*/}
                                        <div className="flex-1">
                                            <h3 className="text-lg font-semibold">{selectedMail[0] || "Unknown Subject"}</h3>
                                            <p className="text-sm text-gray-600">
                                                {/* To: {selectedMail[1] && Array.isArray(selectedMail[1])
                                                    ? selectedMail[1].map((info) => info.identifier).join(", ")
                                                    : "Unknown Receiver"} */}
                                                    {selectedMail[1] ? selectedMail[1] : "Unknown Receiver"}
                                            </p>
                                        </div>
                                        
                                        {/* Refresh Button */}
                                        <button
                                            // onClick={() => handleMailReplyHistory(selectedMail[3], selected_internal_id)} // Call the refresh function
                                            onClick={() => handleMailReplyHis(selectedMail[2], selectedMail[0], selectedMail[1])} // Call the refresh function
                                            className="ml-4 bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-full shadow-sm w-[100px]"
                                        >
                                            Refresh
                                        </button>
                                    </div>

                                    {/* Display selectedMail[3] in a styled box with HTML treatment */}
                                    {/* <div className="bg-gray-100 p-4 rounded-md shadow-sm mt-4">
                                        <div className="text-gray-700 space-y-2">
                                            {selectedMail[2] &&
                                                selectedMail[2]
                                                    .split("\n\n")
                                                    .map((paragraph, index) => (
                                                        <div key={index} dangerouslySetInnerHTML={{ __html: parseHTML(paragraph) }} />
                                                    ))}
                                        </div>
                                    </div> */}
                                </div>
                            )}
                        </div>

                        {/* Conversation History */}
                        <div className="flex-1 bg-white p-4 rounded-md shadow-sm overflow-y-auto flex flex-col">
                            {replyHistory && replyHistory.length > 0 ? (
                                <div className="flex-1 space-y-4">
                                    {replyHistory
                                        .slice()
                                        .sort((a, b) => new Date(a.receiver_time) - new Date(b.receiver_time))
                                        .map((mail, idx) => (
                                            <div key={idx} className="bg-gray-100 rounded-md shadow-md overflow-visible relative">
                                                {/* Upper Box - Sender Info */}
                                                <div className="bg-white p-4 shadow border-b">
                                                    <div className="flex justify-between items-center">
                                                        {mail.role === "prospect" ? (
                                                        <h2 className="text-lg font-bold">
                                                            {mail.from_info?.display_name || mail.from_info?.identifier || "Unknown Sender"}
                                                        </h2> 
                                                        ):(
                                                            <h2 className="text-lg font-bold"> You </h2>
                                                        )}
                                                        <span className="text-xs text-gray-500">
                                                            {mail.timestamp ? new Date(mail.timestamp).toLocaleString() : "N/A"}
                                                        </span>
                                                    </div>
                                                </div>

                                                {/* Separator Line */}
                                                <div className="h-[1px] bg-gray-300 w-full"></div>

                                                {/* Lower Box - Email Body */}
                                                <div className="bg-white p-4 shadow-sm">
                                                    <div className="text-gray-700 mt-2 space-y-2">
                                                        {mail.body
                                                            .split("\n\n")
                                                            .map((paragraph, index) => (
                                                                <div key={index} dangerouslySetInnerHTML={{ __html: parseHTML(paragraph) }} />
                                                            ))}
                                                    </div>
                                                </div>

                                                {/* Separator Line Before Information Button */}
                                                <div className="h-[1px] bg-gray-300 w-full"></div>
                                                
                                                {mail.role === "prospect" && (
                                                    <>
                                                        {/* Information Buttons */}
                                                        <div className="bg-white p-4 flex gap-4 items-start text-left">
                                                            <ToggleButton 
                                                                label={<span>Sentiment <span className="text-lg">✨</span></span>} 
                                                                content={mail.sentiment || "N/A"} 
                                                                type="sentiment" 
                                                            />
                                                            
                                                            <ToggleButton 
                                                                label={<span>Emotion <span className="text-lg">✨</span></span>} 
                                                                content={mail.emotions?.join("\n") || "N/A"} 
                                                                type="emotion" 
                                                            />
                                                            
                                                            <ToggleButton 
                                                                label={<span>Key Takeaway <span className="text-lg">✨</span></span>} 
                                                                content={mail.key_takeaways || ""} 
                                                                type="key_takeaway" 
                                                            />
                                                        </div>
                                                    </>
                                                )}
                                            </div>
                                        ))}

                                    {/* Reply Button Below Last Mail */}
                                    {!replyBox && (
                                        <div className="w-full flex justify-start space-x-2">
                                            <button
                                                className="bg-[#223F9E] text-white px-4 py-2 rounded-full flex items-center gap-2"
                                                onClick={() => setReplyBox(true)}
                                            >
                                                Reply <FaReply />
                                            </button>
                                            <button
                                                className="bg-white border-2 border-[#223F9E] text-[#223F9E] px-4 py-2 rounded-full flex items-center gap-2 shadow-md hover:bg-[#223F9E] hover:text-white transition-all"
                                                onClick={() => setReplyBoxSuggest(true)}
                                            >
                                                Reply Suggestion <span className="text-lg">✨</span>
                                            </button>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex h-full items-center justify-center">
                                    {/* <h2 className="text-xl font-semibold text-gray-500">Select an email to view the conversation.</h2> */}
                                </div>
                            )}
                        </div>

                        {/* Reply Box at the Bottom */}
                        {replyBox && (
                            <div className="bg-white p-4 rounded-md shadow border mt-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
                                    <input
                                        type="text"
                                        value={replyMailSubject}
                                        onChange={(e) => setReplyMailSubject(e.target.value)}
                                        className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-600 mb-2"
                                    />
                                </div>
                                <ReactQuill
                                    theme="snow"
                                    value={replyMailContent}
                                    onChange={(content) => setReplyMailContent(content)}
                                    modules={modules}
                                    className="h-[100px] rounded-full mb-[50px]"
                                    placeholder="Write your reply here..."
                                />
                                <div className="flex justify-start gap-2 mt-2">
                                    <button
                                        className="bg-[#223F9E] w-30 text-white px-4 py-2 rounded-full hover:bg-blue-700"
                                        onClick={handleMailReply}
                                    >
                                        Reply
                                    </button>
                                    <button
                                        className="bg-gray-300 w-30 text-black px-4 py-2 rounded-full hover:bg-gray-400"
                                        onClick={handleDiscard}
                                    >
                                        Discard
                                    </button>
                                </div>
                            </div>
                        )}

                        {replyBoxSuggest && (
                            <ReplyBox 
                                replyHistory={replyHistory} 
                                handleCloseRepSuggest={() => setReplyBoxSuggest(false)}
                                activeTab={activeTab}
                                AutoReply={AutoReply} 
                            />
                        )}
                    </>
                ) : (
                    <>
                        {/* Information Box */}
                        <div className="bg-white px-4 py-1 rounded-md shadow-sm flex flex-col mb-2">
                            {selectedLinkedin.length > 0 && (
                                <div className="flex items-center justify-between w-full">
                                    <div className="bg-blue-100 px-4 py-2 rounded-full shadow-sm flex-grow">
                                        <h2 className="text-lg font-semibold text-blue-900">{selectedLinkedin[0] || "Unknown Subject"}</h2>
                                    </div>
                                    <button
                                        onClick={() => handleLinkedInReplyHistory(selectedLinkedin[1])} // Call the refresh function
                                        className="ml-4 bg-gray-200 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-full shadow-sm w-[100px] flex items-center justify-center"
                                    >
                                        Refresh
                                    </button>
                                </div>
                            )}
                        </div>

                        {/* Conversation History */}
                        <div className="flex-1 bg-white p-4 rounded-md shadow-sm overflow-y-auto flex flex-col h-full">
                            {linkedinMessHistory && linkedinMessHistory.length > 0 ? (
                                <div className="flex-1 space-y-4">
                                    {linkedinMessHistory
                                        .slice()
                                        .sort((a, b) => new Date(a.receiver_time) - new Date(b.receiver_time))
                                        .map((lnk_mess, idx) => (
                                            <div key={idx} className="bg-gray-100 rounded-md shadow-md overflow-visible relative">
                                                {/* Upper Box - Sender Info */}
                                                <div className="bg-white p-4 shadow border-b">
                                                    <div className="flex justify-between items-center">
                                                        <h2 className="text-lg font-bold">
                                                            {lnk_mess.sender?.attendee_name || "Unknown Sender"}
                                                        </h2>
                                                        <span className="text-xs text-gray-500">
                                                            {lnk_mess.timestamp ? new Date(lnk_mess.timestamp).toLocaleString() : "N/A"}
                                                        </span>
                                                    </div>
                                                </div>

                                                {/* Separator Line */}
                                                <div className="h-[1px] bg-gray-300 w-full"></div>

                                                {/* Lower Box - Email Body */}
                                                <div className="bg-white p-4 shadow-sm">
                                                    <div className="text-gray-700 mt-2 space-y-2">
                                                        {lnk_mess.message_content
                                                            .split("\n\n")}
                                                    </div>
                                                </div>

                                                {/* Separator Line Before Information Button */}
                                                <div className="h-[1px] bg-gray-300 w-full"></div>

                                                {lnk_mess.role === "prospect" && (
                                                    <>
                                                        {/* Information Buttons */}
                                                        <div className="bg-white p-4 flex gap-4 items-start text-left">
                                                            <ToggleButton 
                                                                label={<span>Sentiment <span className="text-lg">✨</span></span>} 
                                                                content={lnk_mess.sentiment || "N/A"} 
                                                                type="sentiment" 
                                                            />
                                                            
                                                            <ToggleButton 
                                                                label={<span>Emotion <span className="text-lg">✨</span></span>} 
                                                                content={lnk_mess.emotions?.join("\n") || "N/A"} 
                                                                type="emotion" 
                                                            />
                                                            
                                                            <ToggleButton 
                                                                label={<span>Key Takeaway <span className="text-lg">✨</span></span>} 
                                                                content={lnk_mess.key_takeaways || ""} 
                                                                type="key_takeaway" 
                                                            />
                                                        </div>
                                                    </>
                                                )}    
                                            </div>
                                        ))}

                                    {/* Reply Button Below Last Mail */}
                                    {!replyBox && (
                                        <div className="w-full flex justify-start space-x-2">
                                            <button
                                                className="bg-[#223F9E] text-white px-4 py-2 rounded-full flex items-center gap-2"
                                                onClick={() => setReplyBox(true)}
                                            >
                                                Reply <FaReply />
                                            </button>
                                            <button
                                                className="bg-white border-2 border-[#223F9E] text-[#223F9E] px-4 py-2 rounded-full flex items-center gap-2 shadow-md hover:bg-[#223F9E] hover:text-white transition-all"
                                                onClick={() => setReplyBoxSuggest(true)}
                                            >
                                                Reply Suggestion <span className="text-lg">✨</span>
                                            </button>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex h-full items-center justify-center">
                                    {/* <h2 className="text-xl font-semibold text-gray-500">Select an email to view the conversation.</h2> */}
                                </div>
                            )}
                        </div>

                        {/* Reply Box at the Bottom */}
                        {replyBox && (
                            <div className="bg-white p-4 rounded-md shadow border mt-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Message:</label>
                                    <textarea
                                        value={replyLinkedInContent}
                                        onChange={(e) => setReplyLinkedInContent(e.target.value)}
                                        className="w-full border border-gray-300 rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-600 mb-2 h-[100px] bg-white text-black overflow-y-auto"
                                        style={{ whiteSpace: "pre-wrap" }}
                                    />
                                </div>
                                <div className="flex justify-start gap-2 mt-2">
                                    <button
                                        className="bg-[#223F9E] w-30 text-white px-4 py-2 rounded-full hover:bg-blue-700"
                                        onClick={handleLinkedReply}
                                    >
                                        Reply
                                    </button>
                                    <button
                                        className="bg-gray-300 w-30 text-black px-4 py-2 rounded-full hover:bg-gray-400"
                                        onClick={handleDiscard}
                                    >
                                        Discard
                                    </button>
                                </div>
                            </div>
                        )}

                        {replyBoxSuggest && (
                            <ReplyBox 
                                replyHistory={linkedinMessHistory} 
                                handleCloseRepSuggest={() => setReplyBoxSuggest(false)}
                                activeTab={activeTab}
                                AutoReply={AutoReply} 
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
};

const Unibox = () => {

    const [activeTab, setActiveTab] = useState('mail');
    const navigate = useNavigate();
    
    const handleBack = () => {
        navigate('/email-status');
    };

    return (
        <div className="h-screen flex flex-col bg-white overflow-hidden">
            <HeaderMainPage />
            {/* Page Content Fullscreen */}
            <div className="flex justify-center items-center flex-grow h-screen">
                {/* Main Box Fullscreen */}
                <div className="bg-white w-full h-full shadow-md rounded-md flex flex-col p-2">
                    {/* Object 1 */}
                    <div className="rounded p-2 flex justify-between items-center">
                        {/* Left Section with Back Button */}
                        <div className="flex items-center space-x-4">
                            <button onClick={handleBack} className="text-gray-600 hover:text-gray-900 rounded-full bg-gray-200 p-2">
                                <FaArrowLeft className="h-6 w-6" />
                            </button>
                            <div>
                                <h1 className="text-lg font-bold">{localStorage.getItem("campaign_name")}</h1>
                                <h2 className="text-md text-gray-600">{localStorage.getItem("core_service")}</h2>
                            </div>
                        </div>

                        {/* Tag Navigation with Equal Space and Bold Text */}
                        <div className="bg-gray-200 rounded-full flex w-[300px] justify-between">
                            <button onClick={() => setActiveTab('mail')} 
                                className={`rounded-full flex-1 px-2 py-2 font-medium text-lg ${activeTab === 'mail' ? 'bg-[#223F9E] text-white' : 'bg-gray-200 text-black'}`}>
                                Mail
                            </button>
                            <button onClick={() => setActiveTab('linkedin')} 
                                className={`rounded-full flex-1 px-2 py-2 font-medium text-lg ${activeTab === 'linkedin' ? 'bg-[#223F9E] text-white' : 'bg-gray-200 text-black'}`}>
                                LinkedIn
                            </button>
                        </div>
                    </div>

                    {/* Object 2 - Using MailboxContent with state */}
                    <MailboxContent activeTab={activeTab} />
                </div>
            </div>
        </div>
    );
}

export default Unibox;