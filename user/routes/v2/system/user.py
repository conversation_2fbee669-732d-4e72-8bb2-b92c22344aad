from database.database import get_db
from routes.v2.system_error import BAD_REQUEST, USER_NOT_FOUND
from database.models import User
from schema.user_schema import UserResponse, UserUpdate, LinkedInConnect, UnipileCallback, UnipileStatusUpdate
from utils.unipile_utils.unipile_client import LinkedInClient

from sqlalchemy.future import select
from sqlalchemy import update
from sqlalchemy.orm import Session
from fastapi import Header, Depends, status, APIRouter, HTTPException
from loguru import logger



# Create an API router
router = APIRouter(
    prefix="/api",
    tags=['User Management'],
    responses={404: {'description': 'Not found'}},
)


# Get Personal Information
@router.get('/users/me', response_model=UserResponse, status_code=status.HTTP_200_OK)
async def get_personal_information(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        user = db.query(User).filter(User.user_id == request_user_id).first()

        user_response = UserResponse(
            email = user.email,
            user_info = user.user_info,
            user_linkedin_info=user.user_linkedin_info,
            unipile_linkedin_id=user.unipile_linkedin_id,
            linkedin_connection_status=user.linkedin_connection_status
        )
        return user_response
    except:
        raise BAD_REQUEST
    

# Update Persional Infomation
@router.put('/users/me/update-info', status_code=status.HTTP_200_OK)
async def update_personal_information(
    update_info: UserUpdate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        user_info = {
            "user_type": update_info.user_type,
            "nick_name": update_info.nick_name,
            "company_type": update_info.company_type,
            "billing_plan": update_info.billing_plan,
            "linkedin_address": update_info.linkedin_address
        }        
        user = db.query(User).filter(User.user_id == request_user_id).first()
        
        if not user:
            raise USER_NOT_FOUND
        
        stmt = (
            update(User)
            .where(User.user_id==request_user_id)
            .values(user_info=user_info)
        )
        db.execute(stmt)
        db.commit()
        return {"status": "success"}
    
    except Exception as e:
        db.rollback()
        logger.info(e)
        raise BAD_REQUEST
    
@router.post("/users/unipile/linkedin/connect", status_code=status.HTTP_201_CREATED)
async def create_linkedin_connection(
    unipile_callback: UnipileCallback,
    db: Session = Depends(get_db)
):
    try:
        this_user = db.query(User).where(User.user_id==unipile_callback.name).first()
        connection_status = this_user.linkedin_connection_status
        if connection_status == None:
            connection_status = "NOT_CONNECTED"

        if unipile_callback.status == "CREATION_SUCCESS":
            connection_status = "CONNECTED"

        if this_user:
            client = LinkedInClient()
            client.set_unipile_account_id(unipile_callback.account_id)

            response = client.retrieve_own_profile()
            if response.status_code in [200,201]:
                profile = response.json()
                linkedin_info = {
                    'public_id': profile['public_identifier'],
                    'urn_id': profile["provider_id"],
                }
                stmt = (
                    update(User)
                    .where(User.user_id==unipile_callback.name)
                    .values(
                        user_linkedin_info=linkedin_info, 
                        unipile_linkedin_id=unipile_callback.account_id, 
                        linkedin_connection_status=connection_status
                    )
                )

                db.execute(stmt)
                db.commit()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=response.text
                )
        else:
            raise USER_NOT_FOUND
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

    
@router.put("/users/unipile/linkedin/disconnect", status_code=status.HTTP_200_OK)
async def remove_linkedin_connection(
    request_user_id : str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        this_user = db.query(User).where(User.user_id==request_user_id).first()
        stmt = (
            update(User)
            .where(User.user_id==request_user_id)
            .values(linkedin_connection_status="DISCONNECTED")
        )

        # client = LinkedInClient()
        # response = client.delete_account(this_user.unipile_linkedin_id)
        db.execute(stmt)
        db.commit()        
        return {
            "status": "success"
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/users/unipile/linkedin/reconnect")
async def reconnect_linkedin_connection(
    unipile_callback: UnipileCallback,
    db: Session = Depends(get_db)
):
    try:
        logger.info(unipile_callback.dict())
        this_user = db.query(User).where(User.user_id==unipile_callback.name).first()
        connection_status = this_user.linkedin_connection_status
        if unipile_callback.status == "RECONNECTED":
            connection_status = "CONNECTED"
        if this_user:
            client = LinkedInClient()
            client.set_unipile_account_id(unipile_callback.account_id)

            response = client.retrieve_own_profile()
            if response.status_code in [200,201]:
                profile = response.json()
                linkedin_info = {
                    'public_id': profile['public_identifier'],
                    'urn_id': profile["provider_id"],
                }
                stmt = (
                    update(User)
                    .where(User.user_id==unipile_callback.name)
                    .values(
                        user_linkedin_info=linkedin_info, 
                        unipile_linkedin_id=unipile_callback.account_id, 
                        linkedin_connection_status=connection_status
                    )
                )

                db.execute(stmt)
                db.commit()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=response.text
                )
        else:
            raise USER_NOT_FOUND
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/users/unipile/linkedin/status-update")
async def update_linkedin_connection_status(
    unipile_callback: UnipileStatusUpdate,
    db: Session = Depends(get_db)
):
    try:
        logger.info(unipile_callback.AccountStatus)
        account_id = unipile_callback.AccountStatus["account_id"]
        account_type = unipile_callback.AccountStatus["account_type"]
        message = unipile_callback.AccountStatus["message"]
        
        this_user = db.query(User).where(User.unipile_linkedin_id==account_id).first()
        if this_user:
            connection_status = this_user.linkedin_connection_status

            if message == "CREDENTIALS":
                connection_status = "DISCONNECTED"

            stmt = (
                update(User)
                .where(User.unipile_linkedin_id==account_id)
                .values(linkedin_connection_status=connection_status)
            )
            db.execute(stmt)
            db.commit()
        else:
            raise USER_NOT_FOUND
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

# Endpoint for get user infomation
@router.get('/users', status_code=status.HTTP_200_OK)
async def get_total_users(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    query = select(User)
    results = db.execute(query)
    user_list = results.scalars().all()
    return {"user_list": user_list}


# # Endpoint for admin get user infomation
# @router.get('/users/{user_id}', response_model=UserResponse, status_code=status.HTTP_200_OK)
# async def get_user(
#     user_id: uuid.UUID,
#     db: Session = Depends(get_db)
# ):
#     try:
#         db_user = db.query(
#             User.user_id,
#             User.email,
#             User.first_name,
#             User.last_name,
#             User.is_active,
#             User.avatar
#         ).where(
#             User.user_id == user_id,
#         ).first()

#         db_user_response = UserResponse(
#             user_id = db_user.user_id,
#             email = db_user.email,
#             first_name = db_user.first_name,
#             last_name = db_user.last_name,
#             is_active = db_user.is_active,
#             avatar = db_user.avatar,
#         )
#         return db_user_response
#     except Exception as e:
#         logger.error(e)
#         raise BAD_REQUEST


# # Endpoint for admin update user infomation
# @router.put('/users/{user_id}', response_model=AccountResponse, status_code=status.HTTP_200_OK)
# async def update_user(
#     user_id: uuid.UUID,
#     update_info: AdminAccountUpdate,
#     db: Session = Depends(get_db)
# ):
#     try:
#         db_user = crud_user.get_by_user_id(
#             db,
#             user_id = user_id
#         )
        
#         crud_user.update(
#             db = db,
#             db_obj = db_user, 
#             obj_in = update_info
#         )
        
#         return AccountResponse(
#             email = db.query(
#                 User
#             ).filter(
#                 User.user_id == db_user.user_id
#             ).first().email,
#             first_name = db_user.first_name,
#             last_name = db_user.last_name,
#             is_active = db_user.is_active,
#             avatar = db_user.avatar,
#         )
#     except:
#         raise BAD_REQUEST


# Endpoint for delete user infomation
# @router.delete('/users/{user_id}', response_model=Message, status_code=status.HTTP_200_OK)
# async def delete_user(
#     user_id: uuid.UUID,
#     request_user_id: str = Header(None),
#     db: Session = Depends(get_db)
# ):
#     try:
#         crud_user.delete_user(db, user_id = user_id)
#         return Message(
#             msg = "Deleted"
#         )
#     except:
#         raise BAD_REQUEST

# Endpoint for update user role
# @router.put('/users/role/{user_id}', response_model=Message, status_code=status.HTTP_200_OK)
# async def update_user_role(
#     user_id: uuid.UUID,
#     role_update: RoleUpdate,
#     db: Session = Depends(get_db)
# ):
#     user_to_update = db.query(
#         User
#     ).filter(
#         User.user_id == user_id
#     ).first()

#     if not user_to_update:
#         return Message(msg=f"status: error - User not found")

#     role_to_update = db.query(
#         SystemRole
#     ).filter(
#         SystemRole.role_id == role_update.role_id
#     ).first()

#     if not role_to_update:
#         return Message(msg=f"status: error - Role not found")
    
#     user_to_update.system_role.clear()
#     user_to_update.system_role.append(role_to_update)

#     db.commit()

#     return Message(msg=f"User role updated")


# @router.get('/users/email/{query}', status_code=status.HTTP_200_OK)
# async def get_users_by_email(
#     query: str,
#     request_user_id: str = Header(None),
#     db: Session = Depends(get_db)
# ):
#     raw_sql = text(f'''
#         SELECT 
#             usr_account.usr_id,
#             usr_account.first_name,
#             usr_account.last_name,
#             usr_login_data.email
#         FROM usr_account
#         JOIN usr_login_data
#         ON usr_account.usr_id = usr_login_data.usr_id
#         WHERE email LIKE '%{query}%';
#     ''')

#     users_info = db.execute(raw_sql)
#     users_info = users_info.mappings().all()

#     return users_info
    

# Endpoint create user with role
# @router.post('/users/role/create', response_model=LoginDataResponse, status_code=status.HTTP_200_OK)
# async def create_user_role(
#     user: RoleCreate,
#     db: Session = Depends(get_db)
# ):
#     db_item = db.query(
#         UserLoginData
#     ).filter(
#         UserLoginData.email == user.email
#     ).first()

#     if db_item is not None:
#         raise USER_EXIST
    
#     if len(user.password) < 8:
#         raise INVALID_PASSWORD

#     else:
#     # Hash the user's password
#         hashed_password = hash_password(user.password)
#         new_user = UserAccount()

#         user_role_id = db.query(
#             SystemRole
#         ).filter(
#             SystemRole.role_id == user.role_id
#         ).first()

#         new_user.system_role.append(user_role_id)
#         db.add(new_user)
#         db.commit()
        
#         # Create new login data object
#         login_data = UserLoginData(
#             email = user.email,
#             usr_id = new_user.usr_id,
#             password = hashed_password.decode("utf-8"),
#         )
        
#         # Add the new user to the database
#         db.add(login_data)
#         db.commit()

#     return {'status': 'success', 'message': 'Account created.'}
    

# # Endpoint create new role
# @router.post('/admin/role/create', response_model=LoginDataResponse, status_code=status.HTTP_200_OK)
# async def create_new_role(
#     new_role_in: NewRoleIn,
# ):
#     existed_role_db = db.query(
#             SystemRole
#         ).filter(
#             SystemRole.role_name == new_role_in.role_name
#         ).first()
    

#     if existed_role_db is not None:
#         return HTTPException(
#             status_code=409,
#             detail='The role already exists'
#         )
#     else:
#         max_role_id = db.query(func.max(SystemRole.role_id)).scalar()
    
#         new_role_db = SystemRole(
#             role_name = new_role_in.role_name,
#             role_description = new_role_in.role_description
#         )
#         db.query(
#                 SystemRole
#             ).filter(
#                 SystemRole.role_id.is_(None)
#             ).update({SystemRole.role_id: max_role_id + 1})
        
#         db.add(new_role_db)
#         db.commit()
        
#         return f'New role has been created successfully!'

# Export the router
user_routes = router