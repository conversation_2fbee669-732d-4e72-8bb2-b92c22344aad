import React, { useState, useEffect } from 'react';
import {OnboardingHeaderPart, LargeImagePlaceholder} from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import video3 from '../../assets/page3_female_openai.mp4'
import video3_data from '../../assets/page3_female_openai_data.mp4'
import Swal from 'sweetalert2';

// This is old version of the file. moving on OnboardingSelectDataFill2.js

const CampaignInitialize = () => {
  const [isPageReady, setIsPageReady] = useState(false);  
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const Button = ({ className, onClick, text }) => (
    <button className={className} onClick={onClick}>
      {text}
    </button>
  );

  useEffect(() => {
    const fetchUserDetails = async () => {
      const headers = {
        'accept': 'application/json',
        'Authorization': localStorage.getItem('access_token')
      };

      try {
        const response = await fetch(`${roboman_api}/users/me`, {
          method: 'GET',
          headers: headers,
        });

        if (response.ok) {
          const data = await response.json();
          if (data.unipile_account_id === null) {
            localStorage.setItem('lnk_act', false);
            await Swal.fire({
              icon: 'error',
              title: 'LinkedIn Login Failed',
              text: 'The LinkedIn login is unsuccessful. You cannot use to LinkedIn feature.',
              confirmButtonText: 'Okay',
            });
          } else {
            localStorage.setItem('lnk_act', true);
            await Swal.fire({
              icon: 'success',
              title: 'LinkedIn Login Successful',
              text: 'The LinkedIn login was successful. You can use to LinkedIn feature.',
              confirmButtonText: 'Okay',
            });
          }
        } else {
          localStorage.setItem('lnk_act', false);
          await Swal.fire({
            icon: 'error',
            title: 'Error',
            text: `Failed to fetch user details. You cannot use to LinkedIn feature.`,
            confirmButtonText: 'Okay',
          });
        }
      } catch (error) {
        localStorage.setItem('lnk_act', false);
        await Swal.fire({
          icon: 'error',
          title: 'Error',
          text: `An error occurred: ${error.message}`,
          confirmButtonText: 'Okay',
        });
      } finally {
        setIsPageReady(true); // Allow page rendering after SweetAlert interaction
      }
    };

    fetchUserDetails();
  }, []);

  const HandleUsingCampaignAssistance = () => {
    console.log("Using Campaign Assistance");
    navigate('/useraichat')
    // Add your logic here
  };

  const HandleUsingManualFill = () => {
    console.log("Using Manual Fill");
    navigate('/campfill')
    // Add your logic here
  };

  const HandleSkip  = () => {
    console.log("Skipped");
    navigate('/onboarding-plan')
    // Add your logic here
  };

  const PlanOption = ({ title, features, onChoose }) => (
    <div className="bg-white shadow-md rounded-lg p-4 flex flex-col items-start w-116 h-30 justify-between relative">
      {/* Center-Aligned Title */}
      <div className="flex justify-center w-full mb-4">
        <h3 className="text-lg font-semibold text-center">{title}</h3>
      </div>
      {/* Features List */}
      <div className="flex justify-center items-center h-[150px]">
        <ul className="list-disc list-inside mb-2 flex-grow text-left w-full ml-2">
          {features.map((feature, index) => (
            <li key={index} className="text-black-700">{feature}</li>
          ))}
        </ul>
      </div>
      {/* Center-Aligned Choose Button */}
      <div className="flex justify-center w-full mt-4">
        <Button
          onClick={onChoose}
          className="text-white bg-[#223F9E] min-w-[100px] py-2 px-4 font-semibold rounded-full"
          text="Choose"
        />
      </div>
    </div>
  );

  const SelectionBox = () => {
    
    const plans = [
      { 
        title: "Campaign Assistant", 
        features: ["Our AI Campagin Assistant will help you to create new campaign by complete each field we need to collect."],
        onChoose: HandleUsingCampaignAssistance
      },
      { 
        title: "Manual Fill", 
        features: ["Providing information about your campaign and our AI will help you to complete the total campaign"],
        onChoose: HandleUsingManualFill
      },
    ];

    return (
      <main className="max-h-[600px] flex-grow flex flex-col justify-center rounded-lg items-start p-6 bg-gray-100 shadow-md">
        <h2 className="text-xl font-bold mb-6">Tell us about your situation</h2>
        <div className="min-h-[350px] text-sm flex space-x-4 mb-8 ">
          {plans.map((plan, index) => (
            <PlanOption key={index} title={plan.title} features={plan.features} onChoose={plan.onChoose} />
          ))}
        </div>
        {/* Right-Aligned Skip Button */}
        <div className="flex justify-end w-full">
          <Button
            onClick={HandleSkip} // Note: Replace with actual navigation logic
            className="h-[40px] min-w-[150px] text-blue-700 bg-white py-1 px-7 rounded-lg border border-blue-700"
            text="Skip"
          />
        </div>
      </main>
    );
  };

  // If the page isn't ready yet, show a loading indicator or nothing
  if (!isPageReady) {
    return <div className="flex items-center justify-center min-h-screen bg-gray-100">Loading...</div>;
  }

  const videoToUse = JSON.parse(localStorage.getItem("havedata")) === true ? video3_data : video3;
  
  return (
    <div className="flex flex-col min-h-screen bg-white relative">
      {/* Header section */}
      <OnboardingHeaderPart />
      {!isPageReady ? (
        // Loading State UI
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <p>Loading...</p>
        </div>
      ) : (
        // Main Page Content
        <div className="flex justify-center items-start flex-grow pt-10 overflow-auto bg-gray-100">
          <div className="flex flex-col lg:flex-row w-full max-w-[1200px] min-h-[600px] md:justify-center md:items-center">
            {/* The below <div> is the left part */}
            {/* <div className="w-[600px] flex flex-col items-start justify-start ml-16 mr-10">
              <div className="flex-grow flex items-center justify-center bg-gray-200 w-full rounded-xl h-[calc(100%-1rem)]">
                <span className="text-gray-500">Large Image Placeholder</span>
              </div>
            </div> */}
            {/* Left Section */}
            <LargeImagePlaceholder videomain={videoToUse} />
            {/* Right Section */}
            <div className="md:w-[600px] md:h-[600px] bg-gray-100 flex flex-col justify-center items-center">
              <SelectionBox />
            </div>
          </div>
        </div>
      )}
    </div>
  );

};

export default CampaignInitialize;
