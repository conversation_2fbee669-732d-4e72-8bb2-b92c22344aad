from pydantic import BaseModel, Field
from pydantic.networks import EmailStr
from typing import Optional, List
from uuid import UUID

class RepUpdate(BaseModel):
    rep_id: str
    company_id: str
    rep_email: Optional[EmailStr] = None
    rep_linkedin_address: Optional[str] = None
    rep_linkedin_urn: Optional[str] = None
    ocean_label: Optional[List[str]] = None
    disc_label: Optional[List[str]] = None
    primary_contact: Optional[bool] = None

    class Config:
        orm_mode = True

