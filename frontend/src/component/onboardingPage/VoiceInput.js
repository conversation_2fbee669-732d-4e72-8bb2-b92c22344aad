import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

export const useVoiceInput = () => {
  /**
   * useVoiceInput Hook
   * This hook provides functionality for voice input using the Web Speech API.
   * It allows starting and stopping voice recognition, and retrieving the recognized text.
   */
  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  } = useSpeechRecognition({
    interimResults: true
  });

  const startListening = (isContinuous = true) => {
    /**
     * Start listening for voice input.
     * @param {boolean} isContinuous - If true, the recognition will continue until stopped manually.
     */
    if (browserSupportsSpeechRecognition) {
      SpeechRecognition.startListening({ continuous: isContinuous });
    } else {
      console.error("Browser doesn't support speech recognition.");
    }
  };

  const stopListening = () => {
    /**
     * Stop listening for voice input.
     * This will stop the recognition process.
     */
    SpeechRecognition.stopListening();
  };

  const getTranscript = () => {
    /**
     * Get the current transcript of recognized speech.
     * @returns {string} - The recognized text.
     */
    return transcript;
  };

  return {
    startListening,
    stopListening,
    getTranscript,
    isListening: listening,
    resetTranscript,
    browserSupportsSpeechRecognition
  };
};

export default useVoiceInput;
