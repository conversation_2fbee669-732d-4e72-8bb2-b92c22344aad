import React, { useEffect, useState, useRef } from 'react';
import { OnboardingHeaderPart } from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { useVoiceInput } from '../onboardingPage/VoiceInput';
import { fetchAndReturnPCM16 } from '../onboardingPage/AvatarUtils';
import { SimliClient } from 'simli-client';
import { FaMicrophone, FaMicrophoneSlash, FaSyncAlt } from 'react-icons/fa';
import video3_v2 from '../../assets/page3_female_openai_v2.mp4';

const videoToUse = video3_v2;

const simliClient = new SimliClient();

const CampaignInitialize2 = () => {
  const [isPageReady, setIsPageReady] = useState(false);
  const [isLinkedInLoggedIn, setIsLinkedInLoggedIn] = useState(() => {
    return localStorage.getItem('lnk_act') === 'true';
  });
  const [showPreRecorded, setShowPreRecorded] = useState(true);
  const [enableReload, setEnableReload] = useState(false);
  const [inputMessage, setInputMessage] = useState('');
  const [isAvatarPlaying, setAvatarPlaying] = useState(false);
  const [messageSent, setMessageSent] = useState(false);

  const navigate = useNavigate();

  const preRecordedRef = useRef(null);
  const videoRef = useRef(null);
  const audioRef = useRef(null);
  const transcriptTimeoutRef = useRef(null);

  const {
    startListening,
    stopListening,
    getTranscript,
    isListening,
    resetTranscript,
  } = useVoiceInput();

  // Define PlanOption component internally
  const PlanOption = ({ title, features, onChoose }) => (
    <div className="flex flex-col justify-between bg-white rounded-xl shadow-md border border-gray-200 p-6 hover:shadow-lg transition cursor-pointer">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <p className="text-gray-600 flex-grow">{features}</p>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onChoose();
        }}
        className="mt-6 w-full py-2 px-4 rounded-full bg-blue-600 hover:bg-blue-700 text-white font-medium transition"
      >
        Choose
      </button>
    </div>
  );

  // Define Button component internally
  const Button = ({ className, onClick, text }) => (
    <button className={className} onClick={onClick}>
      {text}
    </button>
  );

  // Simli Client Initialization and events
  useEffect(() => {
    const fetchUserDetails = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_ROBOMAN_API}/users/me`, {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            Authorization: localStorage.getItem('access_token'),
          },
        });
        if (response.ok) {
          const data = await response.json();
          setIsLinkedInLoggedIn(data.unipile_linkedin_id !== null);
        } else {
          setIsLinkedInLoggedIn(false);
        }
      } catch {
        setIsLinkedInLoggedIn(false);
      } finally {
        setIsPageReady(true);
      }
    };

    if (!isPageReady) fetchUserDetails();
  }, [isPageReady]);

  useEffect(() => {
    if (isPageReady) {
      handleStartSimli();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPageReady]);

  // Start Simli Client
  const handleStartSimli = async () => {
    try {
      simliClient.Initialize({
        apiKey: process.env.REACT_APP_SIMLI_API_KEY,
        faceID: process.env.REACT_APP_SIMLI_FACEID_FEMALE,
        handleSilence: true,
        maxSessionLength: 1800,
        maxIdleTime: 200,
        videoRef,
        audioRef,
        SimliURL: 's://api.simli.ai',
      });

      await simliClient.start();

      setTimeout(() => {
        const audioData = new Uint8Array(1000).fill(0);
        simliClient.sendAudioData(audioData);
      }, 4000);
    } catch (error) {
      console.error('Error in Simli start:', error);
    }
  };

  // Close Simli Client
  const handleCloseSimli = () => {
    simliClient.close();
  };

  // Use Effect to handle Simli Client events
  useEffect(() => {
    simliClient.on('connected', () => {
      console.log('SimliClient connected');
      stopListening();
    });

    simliClient.on('disconnected', () => {
      console.log('SimliClient disconnected');
      stopListening();
      setEnableReload(true);
    });

    simliClient.on('failed', () => {
      console.log('SimliClient connection failed');
      stopListening();
      setEnableReload(true);
    });

    simliClient.on('speaking', () => {
      setAvatarPlaying(true);
      stopListening();
    });

    simliClient.on('silent', () => {
      setAvatarPlaying(false);
      if (!showPreRecorded) {
        setTimeout(() => {
          startListening();
        }, 500);
      }
    });

    return () => {
      simliClient.off('connected');
      simliClient.off('disconnected');
      simliClient.off('failed');
      simliClient.off('speaking');
      simliClient.off('silent');
    };
  }, [showPreRecorded]);

  // Handle Send Message
  const handleSendMessage = async () => {
    if (isListening) stopListening();
    resetTranscript();

    try {
      const response = await fetch(`${process.env.REACT_APP_ROBOMAN_API}/campaign/initiation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
          Authorization: localStorage.getItem('access_token'),
        },
        body: JSON.stringify({ input_str: inputMessage }),
      });

      const fullResponse = await response.text();
      setInputMessage('');
      setMessageSent(true);

      if (fullResponse.includes('Thank you for choosing Campaign Assistant')) {
        handleCloseSimli();
        navigate('/useraichat');
      } else if (fullResponse.includes('Thank you for choosing Manual Fill')) {
        handleCloseSimli();
        navigate('/campfill');
      } else {
        await sendAudiotoSimli(fullResponse, false);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      Swal.fire({
        icon: 'error',
        title: 'Oops...',
        text: 'There was an error processing your request. Please try again.',
      });
    }
  };

  // Send audio to Simli Client
  const sendAudiotoSimli = async (assistant, is_summary = true) => {
    try {
      const response = await fetch(`${process.env.REACT_APP_ROBOMAN_API}/openai/text-to-speech`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: localStorage.getItem('access_token'),
        },
        body: JSON.stringify({ text_input: assistant, is_summary }),
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        return;
      }

      const result = await response.json();
      const audio8uintdata = await fetchAndReturnPCM16(result.image_url);

      const chunkSize = 6000;
      for (let i = 0; i < audio8uintdata.length; i += chunkSize) {
        const chunk = audio8uintdata.slice(i, i + chunkSize);
        simliClient.sendAudioData(chunk);
      }

      setAvatarPlaying(true);
    } catch (error) {
      console.error('Error sending audio to Simli:', error);
    }
  };

  // Handle Pre-recorded video end
  const handlePreRecordedEnd = () => {
    setShowPreRecorded(false);
    startListening();
  };

  // Voice input listener and auto-send on transcript changes
  useEffect(() => {
    const transcript = getTranscript();

    if (isListening) {
      if (transcript !== inputMessage) {
        setInputMessage(transcript);
        setMessageSent(false);
      } else if (!messageSent) {
        if (transcriptTimeoutRef.current) clearTimeout(transcriptTimeoutRef.current);

        transcriptTimeoutRef.current = setTimeout(async () => {
          if (inputMessage.trim() !== '' && !messageSent) {
            await handleSendMessage();
            setMessageSent(true);
          }
        }, 2500);
      }
    } else if (transcript) {
      setInputMessage(transcript);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getTranscript, isListening, inputMessage, messageSent]);

  if (!isPageReady) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        Loading...
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <OnboardingHeaderPart />

      {/* Main Content */}
      <main className="flex flex-col lg:flex-row flex-grow max-w-[1400px] mx-auto p-6 gap-8">
        {/* Left: Avatar Video Section */}
        <section
          aria-label="Avatar Video Section"
          className="flex-1 flex flex-col items-center justify-center relative bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl shadow-md p-6"
        >
          <div className="relative w-[350px] h-[350px] sm:w-[450px] sm:h-[450px] rounded-full overflow-hidden shadow-lg">
            {/* Pre-recorded Video */}
            <video
              ref={preRecordedRef}
              className={`absolute top-0 left-0 w-full h-full object-cover transition-opacity duration-500 ${
                showPreRecorded ? 'opacity-100 z-10' : 'opacity-0 pointer-events-none'
              }`}
              onEnded={handlePreRecordedEnd}
              autoPlay
              playsInline
              muted
            >
              <source src={videoToUse} type="video/ogg" />
            </video>

            {/* Live Video */}
            <video
              ref={videoRef}
              id="simli_video"
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
              aria-label="Live Avatar Video"
            />
            <audio ref={audioRef} id="simli_audio" autoPlay muted aria-hidden="true" />
          </div>

          {/* Controls */}
          <div className="flex mt-6 gap-4">
            {/* Reload Button */}
            <button
              type="button"
              onClick={() => window.location.reload()}
              disabled={!enableReload}
              aria-disabled={!enableReload}
              className={`flex items-center gap-2 px-4 py-2 rounded-full text-white font-medium shadow-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                enableReload ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'
              }`}
              aria-label="Reload video"
            >
              <FaSyncAlt />
              Reload
            </button>

            {/* Microphone Button */}
            <button
              type="button"
              className={`p-3 rounded-full text-white shadow-md transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-red-500 ${
                isListening ? 'bg-red-500' : 'bg-gray-500'
              }`}
              disabled={isListening || isAvatarPlaying}
              aria-pressed={isListening}
              aria-label={isListening ? 'Mute microphone' : 'Unmute microphone'}
            >
              {isListening ? <FaMicrophone size={20} /> : <FaMicrophoneSlash size={20} />}
            </button>
          </div>
        </section>

        {/* Right: Situation Selection Section */}
        <section
          aria-label="User Situation Selection"
          className="flex-1 flex flex-col justify-center"
        >
          <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center lg:text-left">
            Tell us about your situation
          </h1>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <PlanOption
              title="Campaign Assistant"
              features="Chat with Anna and she will create your campaign for you"
              onChoose={() => {
                handleCloseSimli();
                navigate('/useraichat');
              }}
            />
            <PlanOption
              title="Manual Fill"
              features="Enter campaign details manually so we can build your campaign"
              onChoose={() => {
                handleCloseSimli();
                navigate('/campfill');
              }}
            />
          </div>

          {/* Skip Button */}
          <div className="mt-8 flex justify-center lg:justify-start">
            <Button
              onClick={() => {
                handleCloseSimli();
                navigate('/dashboard');
              }}
              className="px-6 py-2 rounded-full bg-red-600 hover:bg-red-700 text-white font-semibold shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              text="Skip"
            />
          </div>
        </section>
      </main>

      {/* LinkedIn Status */}
      <footer className="w-full flex justify-center py-4 bg-white border-t shadow-inner">
        <span
          className={`flex items-center gap-2 px-6 py-2 text-white font-medium rounded-full shadow-md select-none ${
            isLinkedInLoggedIn ? 'bg-green-600' : 'bg-red-600'
          }`}
          aria-live="polite"
        >
          {isLinkedInLoggedIn ? '✅ LinkedIn connected' : '❌ LinkedIn not connected'}
        </span>
      </footer>
    </div>
  );
};

export default CampaignInitialize2;
