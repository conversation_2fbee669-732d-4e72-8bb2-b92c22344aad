from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

# class FeesAndCharges(BaseModel):
#     """Details regarding various fees and charges associated with the mortgage."""
#     product_fee: Optional[float] = Field(None, description="The fee charged for the specific mortgage product. May be added to the loan or paid upfront.")
#     valuation_fee: Optional[float] = Field(None, description="The cost incurred for assessing the property's value.")
#     mortgage_account_fee: Optional[float] = Field(None, description="A fee for setting up and maintaining the mortgage account.")
#     arrangement_fee: Optional[float] = Field(None, description="Fee paid to an intermediary or broker for arranging the mortgage.")
#     legal_fees: Optional[float] = Field(None, description="Estimated costs for conveyancing and other legal services.")
#     risk_charge_fee: Optional[float] = Field(None, description="A fee charged if the lender is lending more than their normal limit on the property's security.")
#     cashback_amount: Optional[float] = Field(None, description="A payment from the lender to the borrower on completion of the mortgage.")
#     annual_administration_fee_non_direct_debit: Optional[float] = Field(None, description="An annual fee applied if monthly repayments are not made via direct debit.")

# class HouseholdInsurance(BaseModel):
#     """Details of the required household insurance policy."""
#     insurer: Optional[str] = Field(None, description="The provider of the household insurance.")
#     type_of_cover: Optional[str] = Field(None, description="Specifies the type of coverage, e.g., 'Supercover Special'.")
#     sum_insured: Optional[float] = Field(None, description="The maximum amount the property is insured for.")
#     annual_premium: Optional[float] = Field(None, description="The yearly cost of the household insurance.")
#     premium_tax: Optional[float] = Field(None, description="The tax component included in the insurance premium.")

# class LifeAssurancePolicy(BaseModel):
#     """Details of any required life assurance policy."""
#     company: Optional[str] = Field(None, description="The company providing the life assurance policy.")
#     amount: Optional[float] = Field(None, description="The sum assured by the life assurance policy.")
#     maturity_date: Optional[str] = Field(None, description="The date the policy is expected to pay out (e.g., 'YYYY-MM-DD' or 'YYYY').")
#     assignment_required: Optional[bool] = Field(None, description="Indicates if the life assurance policy must be assigned as additional security.")
#     separate_monthly_payments_required: Optional[bool] = Field(None, description="True if the homeowner is responsible for making separate monthly payments directly to the policy provider.")

# class EarlyRepaymentCharge(BaseModel):
#     """Details of early repayment charges that may apply."""
#     charge_rate_percentage: Optional[float] = Field(None, description="The percentage of the repaid amount charged as an early repayment fee.")
#     start_date: Optional[str] = Field(None, description="The start date from which the early repayment charge applies. Format as YYYY-MM-DD.")
#     end_date: Optional[str] = Field(None, description="The end date until which the early repayment charge applies. Format as YYYY-MM-DD.")

# class RepaymentPeriod(BaseModel):
#     """
#     Represents a specific period within the mortgage repayment schedule,
#     with its own interest rate, payment amount, and duration.
#     """
#     period_type: Optional[str] = Field(None, description="Type of repayment period, e.g., 'Fixed Rate', 'Variable Rate', 'Initial Payment'.")
#     period_interest_rate: float = Field(..., description="The interest rate applicable for this repayment period. Express as a decimal (e.g., 6.99% as 0.0699).")
#     period_monthly_payment: float = Field(..., description="The expected monthly payment amount for this period.")
#     number_of_payments_in_period: Optional[int] = Field(None, description="The number of monthly payments to be made within this specific period.")
#     period_start_date: Optional[str] = Field(None, description="The start date of this repayment period. Format as YYYY-MM-DD.")
#     period_end_date: Optional[str] = Field(None, description="The end date of this repayment period. Format as YYYY-MM-DD.")
#     description: Optional[str] = Field(None, description="A brief description or note for this repayment period, e.g., 'First payment amount', 'Follow-on rate'.")

# class MortgageCategorization(BaseModel):
#     """Categorization of the mortgage product based on its features."""
#     repayment_method: Optional[str] = Field(None, description="The method by which the loan is repaid, e.g., 'Repayment mortgage' (capital and interest) or 'Interest-Only mortgage'.")
#     interest_rate_structure_type: Optional[str] = Field(None, description="The primary type of interest rate structure for the overall mortgage, e.g., 'Fixed Rate', 'Tracker', 'Standard Variable Rate (SVR)', 'Discount', 'Capped', 'Flexible', 'Offset'.")
#     borrower_property_scheme_type: Optional[str] = Field(None, description="The type of mortgage based on borrower, property use, or specific scheme, e.g., 'Buy-to-Let', 'First-Time Buyer', 'Guarantor', 'Joint', 'Shared Ownership', 'Green Mortgage', '95% LTV'.")

# class MortgageOffer(BaseModel):
#     """
#     Pydantic schema representing the key entities to be extracted from UK mortgage offer documents.
#     This structure facilitates the calculation of expected payments and detection of overcharges.
#     """
#     lender_name: Optional[str] = Field(None, description="The name of the financial institution providing the mortgage.")
#     offer_date: Optional[str] = Field(None, description="The date the mortgage offer was issued. Format as YYYY-MM-DD.")
#     actual_loan_start_date: Optional[str] = Field(None, description="The actual date when the mortgage loan commenced or completed, and interest began accruing. Format as YYYY-MM-DD.")
#     application_number: Optional[str] = Field(None, description="A unique identifier for the mortgage application.")
#     mortgage_account_number: Optional[str] = Field(None, description="The specific identifier for the mortgage account.")
#     principal_sum: float = Field(..., description="The initial amount of money borrowed for the mortgage.")
#     term_years: int = Field(..., description="The total duration of the loan in years.")
#     term_months: int = Field(..., description="The total duration of the loan in months.")

#     mortgage_categorization: Optional[MortgageCategorization] = Field(None, description="Categorization of the mortgage product based on its features and type.")

#     applicant_names: List[str] = Field(..., description="A list of full names of the mortgage applicants.")
#     # correspondence_address: Optional[str] = Field(None, description="The primary correspondence address for the applicant(s).")
#     # security_address: Optional[str] = Field(None, description="The full address of the property being mortgaged.")
#     # original_purchase_price: Optional[float] = Field(None, description="The purchase price of the property at the time of original acquisition.")

#     fees_and_charges: Optional[FeesAndCharges] = Field(None, description="A nested object containing details of various fees and charges.")
#     # household_insurance: Optional[HouseholdInsurance] = Field(None, description="A nested object containing details of the required household insurance policy.")
#     # life_assurance_policy: Optional[LifeAssurancePolicy] = Field(None, description="A nested object containing details of any required life assurance policy.")

#     repayment_schedule: List[RepaymentPeriod] = Field(default_factory=list, description="A list of objects detailing each distinct repayment period with its specific interest rate, payment amount, and duration.")

#     direct_debit_required: Optional[bool] = Field(None, description="Indicates if monthly repayments must be made by direct debit.")
#     overpayment_provision_allowed: Optional[bool] = Field(None, description="True if the borrower is allowed to make additional payments to reduce the principal.")
#     permitted_underpayment_provision_allowed: Optional[bool] = Field(None, description="True if the borrower can temporarily reduce or skip payments based on previous overpayments.")
#     payment_holiday_provision_allowed: Optional[bool] = Field(None, description="True if the borrower can temporarily suspend payments under certain conditions.")
#     interest_accrual_during_payment_holiday: Optional[bool] = Field(None, description="True if interest continues to accrue on the total debt during a payment holiday.")
#     offer_withdrawal_period_weeks: Optional[int] = Field(None, description="The number of weeks within which the mortgage must complete before the offer can be withdrawn.")
#     early_repayment_charges: List[EarlyRepaymentCharge] = Field(default_factory=list, description="A list of objects detailing early repayment charges that may apply.")
#     overpayment_concession_erc_waiver_percentage: Optional[float] = Field(None, description="The percentage of the outstanding balance that can be overpaid annually without incurring early repayment charges.")
#     portability_allowed: Optional[bool] = Field(None, description="True if the mortgage terms can be transferred to a new property if the homeowner moves.")
#     existing_charge_redemption_required: Optional[bool] = Field(None, description="True if any existing charges on the property must be redeemed or postponed.")
#     mortgage_redemption_required: Optional[bool] = Field(None, description="True if an existing mortgage on the property must be fully redeemed.")


#################################################################
class FeesAndCharges(BaseModel):
    """Details regarding various fees and charges associated with the mortgage."""
    product_fee: Optional[float] = Field(None, description="The fee charged for the specific mortgage product. May be added to the loan or paid upfront.")
    valuation_fee: Optional[float] = Field(None, description="The cost incurred for assessing the property's value.")
    mortgage_account_fee: Optional[float] = Field(None, description="A fee for setting up and maintaining the mortgage account.")
    arrangement_fee: Optional[float] = Field(None, description="Fee paid to an intermediary or broker for arranging the mortgage.")
    legal_fees: Optional[float] = Field(None, description="Estimated costs for conveyancing and other legal services.")
    risk_charge_fee: Optional[float] = Field(None, description="A fee charged if the lender is lending more than their normal limit on the property's security.")
    cashback_amount: Optional[float] = Field(None, description="A payment from the lender to the borrower on completion of the mortgage.")
    annual_administration_fee_non_direct_debit: Optional[float] = Field(None, description="An annual fee applied if monthly repayments are not made via direct debit.")

class EarlyRepaymentCharge(BaseModel):
    """Details of early repayment charges that may apply."""
    charge_rate_percentage: Optional[float] = Field(None, description="The percentage of the repaid amount charged as an early repayment fee.")
    start_date: Optional[str] = Field(None, description="The start date from which the early repayment charge applies. Format as YYYY-MM-DD.")
    end_date: Optional[str] = Field(None, description="The end date until which the early repayment charge applies. Format as YYYY-MM-DD.")

class RepaymentPeriod(BaseModel):
    """
    Represents a specific period within the mortgage repayment schedule,
    with its own interest rate, payment amount, and duration.
    """
    period_type: Optional[str] = Field(None, description="Type of repayment period, e.g., 'Fixed Rate', 'Variable Rate', 'Initial Payment'.")
    period_interest_rate: float = Field(..., description="The interest rate applicable for this repayment period. Express as a decimal (e.g., 6.99% as 0.0699).")
    period_monthly_payment: float = Field(..., description="The expected monthly payment amount for this period.")
    number_of_payments_in_period: Optional[int] = Field(None, description="The number of monthly payments to be made within this specific period.")
    period_start_date: Optional[str] = Field(None, description="The start date of this repayment period. Format as YYYY-MM-DD.")
    period_end_date: Optional[str] = Field(None, description="The end date of this repayment period. Format as YYYY-MM-DD.")
    description: Optional[str] = Field(None, description="A brief description or note for this repayment period, e.g., 'First payment amount', 'Follow-on rate'.")

class MortgageCategorization(BaseModel):
    """Categorization of the mortgage product based on its features."""
    repayment_method: Optional[str] = Field(None, description="The method by which the loan is repaid, e.g., 'Repayment mortgage' (capital and interest) or 'Interest-Only mortgage'.")
    interest_rate_structure_type: Optional[str] = Field(None, description="The primary type of interest rate structure for the overall mortgage, e.g., 'Fixed Rate', 'Tracker', 'Standard Variable Rate (SVR)', 'Discount', 'Capped', 'Flexible', 'Offset'.")
    borrower_property_scheme_type: Optional[str] = Field(None, description="The type of mortgage based on borrower, property use, or specific scheme, e.g., 'Buy-to-Let', 'First-Time Buyer', 'Guarantor', 'Joint', 'Shared Ownership', 'Green Mortgage', '95% LTV'.")

class MortgageOffer(BaseModel):
    """
    Pydantic schema representing the key entities to be extracted from UK mortgage offer documents.
    This structure facilitates the calculation of expected payments and detection of overcharges.
    """
    lender_name: Optional[str] = Field(None, description="The name of the financial institution providing the mortgage.")
    offer_date: Optional[str] = Field(None, description="The date the mortgage offer was issued. Format as YYYY-MM-DD.")
    actual_loan_start_date: Optional[str] = Field(None, description="The actual date when the mortgage loan commenced or completed, and interest began accruing. Format as YYYY-MM-DD.")
    application_number: Optional[str] = Field(None, description="A unique identifier for the mortgage application.")
    mortgage_account_number: Optional[str] = Field(None, description="The specific identifier for the mortgage account.")
    principal_sum: float = Field(..., description="The initial amount of money borrowed for the mortgage.")
    term_years: int = Field(..., description="The total duration of the loan in years.")
    term_months: int = Field(..., description="The total duration of the loan in months.")
    mortgage_categorization: Optional[MortgageCategorization] = Field(None, description="Categorization of the mortgage product based on its features and type.")
    applicant_names: List[str] = Field(..., description="A list of full names of the mortgage applicants.")
    fees_and_charges: Optional[FeesAndCharges] = Field(None, description="A nested object containing details of various fees and charges.")
    repayment_schedule: List[RepaymentPeriod] = Field(default_factory=list, description="A list of objects detailing each distinct repayment period with its specific interest rate, payment amount, and duration.")
    direct_debit_required: Optional[bool] = Field(None, description="Indicates if monthly repayments must be made by direct debit.")
    overpayment_provision_allowed: Optional[bool] = Field(None, description="True if the borrower is allowed to make additional payments to reduce the principal.")
    permitted_underpayment_provision_allowed: Optional[bool] = Field(None, description="True if the borrower can temporarily reduce or skip payments based on previous overpayments.")
    payment_holiday_provision_allowed: Optional[bool] = Field(None, description="True if the borrower can temporarily suspend payments under certain conditions.")
    interest_accrual_during_payment_holiday: Optional[bool] = Field(None, description="True if interest continues to accrue on the total debt during a payment holiday.")
    offer_withdrawal_period_weeks: Optional[int] = Field(None, description="The number of weeks within which the mortgage must complete before the offer can be withdrawn.")
    early_repayment_charges: List[EarlyRepaymentCharge] = Field(default_factory=list, description="A list of objects detailing early repayment charges that may apply.")
    overpayment_concession_erc_waiver_percentage: Optional[float] = Field(None, description="The percentage of the outstanding balance that can be overpaid annually without incurring early repayment charges.")
    portability_allowed: Optional[bool] = Field(None, description="True if the mortgage terms can be transferred to a new property if the homeowner moves.")
    existing_charge_redemption_required: Optional[bool] = Field(None, description="True if any existing charges on the property must be redeemed or postponed.")
    mortgage_redemption_required: Optional[bool] = Field(None, description="True if an existing mortgage on the property must be fully redeemed.")