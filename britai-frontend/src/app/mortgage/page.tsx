/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';
import React, { useRef, useState } from 'react';
import Swal from 'sweetalert2';

// interface MortgageAccountInfo {
//   number_of_payments_in_fixed_rate_period_for_initial: number;
//   number_of_payments_in_fixed_rate_period: number;
//   number_of_payments_in_variable_rate_period: number;
//   monthly_payment_fixed_rate_for_initial: number;
//   monthly_payment_fixed_rate: number;
//   monthly_payment_variable_rate: number;
// }

// interface MortgageAccount {
//   loan_account_name: string;
//   loan_account_info: MortgageAccountInfo[];
// }

// interface MortgageInfo {
//   loan_amount: number;
//   final_loan_amount: number;
//   loan_term: number;
//   loan_start_time: string;
//   annual_rate: number;
//   loan_account?: MortgageAccount[];
// }


export default function MortgageInfoPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [mortgageInfo, setMortgageInfo] = useState<any>(null);
  const [showGuidance, setShowGuidance] = useState(false);

  const handleUploadClick = () => {
    setShowGuidance(true);
  };

  const handleGuidanceUpload = () => {
    setShowGuidance(false);
    setTimeout(() => {
      fileInputRef.current?.click();
    }, 100); // slight delay to allow modal to close
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      Swal.fire({
        icon: 'error',
        title: 'Invalid File',
        text: 'Please upload a valid PDF file.',
      });
      return;
    }

    const formData = new FormData();
    formData.append('uploaded_files', file);
    console.log('Uploading file:', file.name);
    console.log("");

    try {
      Swal.fire({
        title: 'Uploading...',
        text: 'Please wait while your PDF is being processed.',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      const response = await fetch('http://localhost:8881/api/document/mortgage-acceptance/extract', {
        method: 'POST',
        headers: {
          'Accept': 'application/json'
        },
        body: formData,
      });

      if (!response.ok) {
        Swal.close();
        throw new Error(`Upload failed: ${response.status}`);
      }

      const result = await response.json();
      setMortgageInfo(result);
      console.log('API Result:', result);

      Swal.fire({
        icon: 'success',
        title: 'Success',
        text: 'PDF processed successfully! Check the information below.',
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      Swal.fire({
        icon: 'error',
        title: 'Upload Failed',
        text: 'Failed to upload and process the file.',
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-8">
      <div className="max-w-3xl bg-white shadow-md rounded-lg p-6">
        <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center">
          Mortgage Miscalculation
        </h1>
        <p className="text-gray-600 text-lg leading-relaxed mb-6">
          Please input your mortgage document in PDF format. Our AI will analyze the document to identify any potential miscalculations or discrepancies in your mortgage terms.
          <br />
        </p>
        <div className="text-center">
          <button
            onClick={handleUploadClick}
            className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600"
          >
            Upload
          </button>
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
        </div>
        {showGuidance && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center"
            id="guidance_popup"
            style={{ backgroundColor: 'rgba(243,244,246,0.4)' }}
          >
            <div className="bg-white rounded-lg shadow-lg p-8 max-w-lg w-full relative">
              <h1 className="text-2xl font-bold mb-4">Document Upload Guidance</h1>
              <p className="mb-4 text-gray-700">
                Please follow the guidance to improve the performance of the system
              </p>
              <ol className="list-decimal list-inside mb-4 text-gray-700 space-y-2">
                <li>Upload pages showing payment breakdowns, interest charged and rate changes</li>
                <li>Skip cover pages or general terms unless they mention your loan number or interest rate</li>
              </ol>
              <div className="mb-4">
                <div className="font-semibold mb-2">You need to upload:</div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-green-600 text-xl">✔️</span>
                    <span>Sample images of a good page</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-red-600 text-xl">❌</span>
                    <span>Sample images of a bad or irrelevant page</span>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-2 mt-6">
                <button
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
                  onClick={() => setShowGuidance(false)}
                >
                  Cancel
                </button>
                <button
                  className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600"
                  onClick={handleGuidanceUpload}
                >
                  Upload
                </button>
              </div>
            </div>
          </div>
        )}
        <div className="mt-6 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center"> <strong>Mortgage Information</strong> </h1>
          <div className="w-full min-h-[300px] bg-gray-200 rounded-lg flex flex-col items-start justify-start p-6 text-left overflow-x-auto">
            {mortgageInfo ? (
              <div>
                <div><strong>Loan Amount:</strong> {mortgageInfo.loan_amount}</div>
                <div><strong>Final Loan Amount:</strong> {mortgageInfo.final_loan_amount}</div>
                <div><strong>Loan Term:</strong> {mortgageInfo.loan_term}</div>
                <div><strong>Loan Start Time:</strong> {mortgageInfo.loan_start_time}</div>
                <div><strong>Annual Rate:</strong> {mortgageInfo.annual_rate}%</div>
                {mortgageInfo.loan_account && mortgageInfo.loan_account.length > 0 && (
                  <div className="mt-4">
                    <strong>Loan Accounts:</strong>
                    {mortgageInfo.loan_account.map((acc: any, idx: number) => (
                      <div key={idx} className="ml-4 mt-2">
                        <div><strong>Name:</strong> {acc.loan_account_name}</div>
                        {acc.loan_account_info && acc.loan_account_info.length > 0 && (
                          <div className="ml-4">
                            {acc.loan_account_info.map((info: any, j: number) => (
                              <div key={j} className="mb-2">
                                <div>Number of Payments (Initial Fixed): {info.number_of_payments_in_fixed_rate_period_for_initial}</div>
                                <div>Number of Payments (Fixed): {info.number_of_payments_in_fixed_rate_period}</div>
                                <div>Number of Payments (Variable): {info.number_of_payments_in_variable_rate_period}</div>
                                <div>Monthly Payment (Initial Fixed): {info.monthly_payment_fixed_rate_for_initial}</div>
                                <div>Monthly Payment (Fixed): {info.monthly_payment_fixed_rate}</div>
                                <div>Monthly Payment (Variable): {info.monthly_payment_variable_rate}</div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-gray-500 flex items-center h-full">No mortgage information yet.</div>
            )}
          </div>
        </div>
        <div className="mt-6 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center"> <strong>Mortgage Payment Estimation</strong> </h1>
          <div className="w-full h-[300px] bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    </div>
  );
}
