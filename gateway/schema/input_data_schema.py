from pydantic import BaseModel, Field
from pydantic.networks import EmailStr
from typing import List, Optional, Literal
import uuid


# System prompt
class InputDataCreate(BaseModel):
    request_message: str
    model_id: int = 1


class InputRAGCreate(BaseModel):
    request_message: str
    model_id: int = 1
    history: List[dict] = None


class ModelCreate(BaseModel):
    model_name: str
    display_name: str 
    model_type: str = 'text completion'
    provider: str = 'OpenAI'
    reference: str = None
    max_token: int = None
    price_in: float = None
    price_out: float = None


class ModelUpdate(ModelCreate):
    model_id: int = 1


class TextCampaign(BaseModel):
    input_str: str

class GenerateEmail(BaseModel):
    input_str: str



class CampaignCreate(BaseModel):
    campaign_name: str
    core_service: str
    unique_selling_proposition: str
    target_audience: str
    problem_solved: str
    key_benefits: List[str]
    primary_goal_of_outreach_campaign:str
    ideal_client: List[str]
    success_measurement: str
    must_have_info: str
    email_format : str = "Write an email of 300 words"
    linkedin_msg_format : str = "Write a message of 100 words" 

class CampaignDetails(BaseModel):
    campaign_name: str
    core_service: str
    unique_selling_proposition: str
    target_audience: str
    problem_solved: str
    key_benefits: List[str]
    primary_goal_of_outreach_campaign:str
    ideal_client: List[str]
    success_measurement: str
    industry: List[str]
    location: str

# class CompanyMatching(BaseModel):
#     industry: List[str]
#     location: str
#     industry_similarity_threshold: Optional[float] = Field(default=0.4, ge=0.0, le=1.0)
#     location_similarity_threshold: Optional[float] = Field(default=0.4, ge=0.0, le=1.0)
#     search_size: Optional[int] = Field(default=100, ge=1, le=300)
#     industry_weight: Optional[float] = Field(default=0.5, ge=0.0, le=1.0)
#     location_weight: Optional[float] = Field(default=0.5, ge=0.0, le=1.0)

class CompanyMatching(BaseModel):
    industries: List[str]
    countries: Optional[List[str]]
    cities_or_states: Optional[List[str]]
    continents: Optional[List[str]]
    search_size: Optional[int] = Field(default=100, ge=1, le=300)

class CampaignDetailsUpdate(BaseModel):
    campaign_name: Optional[str] = None
    core_service: Optional[str] = None
    unique_selling_proposition: Optional[str] = None
    target_audience: Optional[str] = None
    problem_solved: Optional[str] = None
    key_benefits: Optional[List[str]] = None
    primary_goal_of_outreach_campaign: Optional[str] = None
    ideal_client: Optional[List[str]] = None
    success_measurement: Optional[str] = None
    must_have_info: Optional[str] = None 
    email_format: Optional[str]
    linkedin_msg_format: Optional[str]    
    class Config:
        orm_mode = True

class CampaignContentUpdate(BaseModel):
    email_format: Optional[str]
    linkedin_msg_format: Optional[str]
    class Config:
        orm_mode = True

class CompanyEmailUpdate(BaseModel):
    company_id: str
    content_subject : Optional[str] = None
    content : Optional[str] = None 
    email_confirmation_status_id : Optional[int] = Field(default=2, ge=1, le=5) 

    class Config:
        orm_mode = True

class SendEmailRequest(BaseModel):
    company_id_list: List[str]

class SendLinkedInRequest(BaseModel):
    company_id_list: List[str]  
    
class CampaignSendLinkedInMessage(BaseModel):
    recipient_address: str
    subject: Optional[str]
    content: str 

class CampaignSendLinkedInInvitation(BaseModel):
    recipient_address: str
    subject: Optional[str]
    content: str 

class AudioCreate(BaseModel):
    text_input: str
    is_summary: bool


class CompanyLinkedInMsgUpdate(BaseModel):
    company_id : str
    linkedin_message : Optional[str] = None 
    
    class Config:
        orm_mode = True
class CompanyAddManual(BaseModel):
    company_name: str 
    company_email: Optional[EmailStr] = None
    company_linkedin: Optional[str] = None
    industry: str    
    rep_name: str
    rep_email: Optional[EmailStr] = None
    rep_linkedin_address: Optional[str] = None
class CompanyAddManual(BaseModel):
    company_name: str 
    company_email: Optional[EmailStr] = None
    company_linkedin: Optional[str] = None
    industry: str    
    rep_name: str
    rep_email: Optional[EmailStr] = None
    rep_linkedin_address: Optional[str] = None

class CampaignContact(BaseModel):
    calendly_link: Optional[str] = None
    class Config:
        orm_mode = True

class RepUpdate(BaseModel):
    rep_id: str
    company_id: str
    rep_email: Optional[EmailStr] = None
    rep_linkedin_address: Optional[str] = None
    rep_linkedin_urn: Optional[str] = None
    ocean_label: Optional[List[str]] = None
    disc_label: Optional[List[str]] = None
    primary_contact: Optional[bool] = None

    class Config:
        orm_mode = True    

class WebhooksNewEmail(BaseModel):
    email_id: str = None
    account_id: str = None
    event: str = None
    webhook_name: str = None
    date: str = None
    from_attendee: dict = None
    to_attendees: List[dict] = None
    bcc_attendees: List = None
    cc_attendees: List = None
    reply_to_attendees: List = None
    provider_id: str = None
    message_id: str = None
    has_attachments: bool = False
    subject: str = None
    body: str = None
    body_plain: str = None
    attachments: List = None
    folders: List = None
    role: str = None
    read_date: str = None
    is_complete: bool = False
    in_reply_to: Optional[dict] = None
    tracking_id: str = None
    origin: str = None          

    class Config:
        orm_mode = True        

class WebhooksEmailTracking(BaseModel):
    event: Optional[str]
    event_id: Optional[str]
    tracking_id: Optional[str]
    date: Optional[str]
    email_id: Optional[str]
    account_id: Optional[str]
    ip: Optional[str]
    user_agent: Optional[str]
    url: Optional[str]
    label: Optional[str]
    custom_domain: Optional[str]    

    class Config:
        orm_mode = True    

class WebhooksNewMessage(BaseModel):
    account_id: str
    account_type: Literal["LINKEDIN"]
    account_info: dict
    event: Literal[
        "message_reaction",
        "message_read",
        "message_received",
        "message_edited",
        "message_deleted"
    ]
    chat_id: str
    timestamp: str
    webhook_name: str
    message_id: str
    message: Optional[str] = None
    sender: dict
    attendees: List[dict]
    attachments: Optional[dict] = None
    reaction: Optional[str] = None
    reaction_sender: Optional[dict] = None    

    class Config:
        orm_mode = True

class EmailGeneration(BaseModel):
    company_id_list: List[str]
    campaign_id: str
    prompt_name: Optional[str] = None    

class EmailSend(BaseModel):
    company_id_list: List[str]   

class CreateDraftEmail(BaseModel):
    company_id: str
    draft_type: int = 0
    from_address: EmailStr = "<EMAIL>"
    from_name: str
    to_address: EmailStr
    to_name: str
    subject: str
    body: str
    body_plain: str

    class Config:
        orm_mode = True

class UpdateDraftEmail(BaseModel):
    company_id: str
    draft_id: str
    subject: Optional[str] = None
    body: Optional[str] = None

class EmailOutreach(BaseModel):
    campaign_id : str
    company_id_list: List[str]
    prompt_name: Optional[str] = None

class TestScheduleJob(BaseModel):
    test_number: int
    delay_seconds: int    

class GenerateResponse(BaseModel):
    input_str: str

class EnablePrompt(BaseModel):
    prompt_id: str    

class RespondEmail(BaseModel):
    company_id: str
    subject: str
    body: str    

class RespondMessage(BaseModel):
    company_id: str
    message_content: str

class HtmlToText(BaseModel):
    html_content: str    

class SimilarityCheck(BaseModel):
    text1: str
    text2: str
    embedding_dim: int    