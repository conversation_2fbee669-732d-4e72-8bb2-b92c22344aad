import asyncio
import aio_pika
import json
from loguru import logger
from datetime import datetime

from core.config import settings
from message_bus.tasks.humantic_ai_task import update_humantic_profile
from message_bus.tasks.unipile_tasks import receive_reply_webhooks, receive_sent_webhooks, receive_tracking_webhooks
from message_bus.tasks.outreach_tasks import send_response, send_email_outreach, create_drafts
from message_bus.tasks.linkedin_outreach_tasks import send_linkedin_outreach
from message_bus.tasks.elasticsearch_tasks import ingest_data
from message_bus.rabbitmq_connection import RabbitMQConnectionPool

# List of task types (queue names)
TASK_TYPES = ["update_humantic_profile", 
            #   "process_scheduled_job", 
              "receive_reply_webhooks", 
              "send_email_outreach", 
              "send_response", 
            #   "receive_sent_webhooks", 
            #   "receive_tracking_webhooks", 
              "ingest_data", 
              "create_drafts", 
              "send_linkedin_outreach"]

PROCESS_TASK_FUNCTIONS = {
    "update_humantic_profile": update_humantic_profile,
    # "process_scheduled_job": process_scheduled_job,
    "receive_reply_webhooks": receive_reply_webhooks,
    "send_email_outreach": send_email_outreach,
    "send_response": send_response,
    # "receive_sent_webhooks": receive_sent_webhooks,
    # "receive_tracking_webhooks": receive_tracking_webhooks,
    "ingest_data": ingest_data,
    "create_drafts": create_drafts,
    "send_linkedin_outreach": send_linkedin_outreach,
}


RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

async def consume_task(task_type, connection_pool: RabbitMQConnectionPool):
    logger.info(f"[CONSUMER] Starting consumer for {task_type}...")
    try:
        # Acquire a channel from the pool using async with
        async with connection_pool.channel_pool.acquire() as channel:
            try:
                # Delete existing queue if it exists
                if task_type not in ["update_humantic_profile"]:
                    try:
                        await channel.queue_delete(task_type)
                        logger.info(f"[CONSUMER] Deleted existing queue: {task_type}")
                    except Exception as e:
                        logger.warning(f"[CONSUMER] Queue {task_type} might not exist yet. Proceeding...")

                # Delete existing exchanges if they exist
                # try:
                #     await channel.exchange_delete("task_exchange")
                #     logger.info(f"[CONSUMER] Deleted existing exchange: task_exchange")
                # except Exception as e:
                #     logger.warning(f"[CONSUMER] Exchange task_exchange might not exist yet. Proceeding...")

                # Declare the exchange and queue
                exchange = await channel.declare_exchange(
                    name="task_exchange",
                    type="direct",
                    durable=True
                )

                queue = await channel.declare_queue(task_type, durable=True)
                await queue.bind(exchange, routing_key=task_type)
                logger.info(f"[CONSUMER] Waiting for {task_type} tasks.")

                # Start consuming messages
                async with queue.iterator() as queue_iter:
                    async for message in queue_iter:
                        try:
                            async with message.process():
                                process_task_function = PROCESS_TASK_FUNCTIONS.get(task_type)
                                if process_task_function:
                                    await asyncio.shield(process_task_function(message))
                        except Exception as e:
                            logger.error(f"[CONSUMER] Error processing message in {task_type}: {e}")

            except Exception as e:
                logger.error(f"[CONSUMER] Unexpected error while consuming {task_type}: {e}")
                raise

    except asyncio.CancelledError:
        logger.info(f"[CONSUMER] Consumer for {task_type} was cancelled.")
    finally:
        # Close the connection pool
        # await connection_pool.close()
        logger.info(f"[CONSUMER] consumer for {task_type} is being shutdown")


async def start_task_consumers(connection_pool: RabbitMQConnectionPool):
    consumers = [consume_task(task_type=task_type, connection_pool=connection_pool) for task_type in TASK_TYPES]
    return await asyncio.gather(*consumers)