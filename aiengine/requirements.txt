# Web server and API framework
uvicorn[standard]
fastapi==0.111.0

# Data processing and analysis
numpy==1.26.4
pandas==2.2.1

# Data validation and serialization
pydantic>=2.0, <3
python-multipart==0.0.9

# Command line interface
click==8.1.7

# Database ORM and migrations
SQLAlchemy==2.0.27
alembic==1.13.1
asyncpg==0.29.0
psycopg2==2.9.7
sqlalchemy_utils==0.41.1
pgvector==0.2.5  # Vector extension for PostgreSQL

# Networking and async
aiohttp==3.9.1
httpx==0.27.2
requests==2.32.3

# Message queue
pika==1.3.2
aio-pika==9.5.4

# NLP and ML libraries
nltk==3.9.1
scikit-learn==1.5.0
scipy==1.13.1
sentencepiece==0.1.99
tqdm==4.66.1
transformers==4.34.0

# Logging
loguru==0.6.0

# Llama-Index components
llama-index-core==0.11.6
llama-index-llms-openai==0.2.2
llama-index-llms-gemini==0.3.4
llama-index-llms-anthropic==0.3.0
llama-index-embeddings-openai==0.2.4
llama-index-agent-openai==0.3.0
llama-index-multi-modal-llms-openai==0.2.0
llama-index-program-openai==0.2.0
llama-index-question-gen-openai==0.2.0
llama-index-readers-file==0.2.1
llama-index-vector-stores-chroma==0.2.0
llama-index-vector-stores-postgres==0.2.0
llama-index-tools-duckduckgo==0.2.1
llama-index-agent-llm-compiler==0.2.0
llama-index-multi-modal-llms-gemini==0.3.0

# LLM API clients
openai==1.43.1
anthropic==0.28.1

# Document processing
pyvi==0.1.1
PyMuPDF==1.23.21
docx2txt==0.8
chromadb==0.5.4

# AWS integration
boto3==1.34.109

# Configuration management
pydantic-settings==2.3.4

# AI frameworks
dspy-ai==2.4.10

# Web scraping and parsing
bs4==0.0.2
dnspython==2.4.2