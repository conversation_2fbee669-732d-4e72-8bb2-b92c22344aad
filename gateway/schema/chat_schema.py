from typing import Optional, List
import uuid
from datetime import datetime
from pydantic import BaseModel
from enum import Enum


class MessageRole(str, Enum):
    INPUT = 1
    OUTPUT = 2

# Chat
class ConversationBase(BaseModel):
    conversation_id: uuid.UUID
    conversation_name: str

class ConversationResponse(ConversationBase):
    created_at: datetime
    updated_at: datetime

class ConversationCreate(BaseModel):
    conversation_name: str
    voice_path: Optional[str] = None

class ConversationDocumentUpdate(BaseModel):
    conversation_id: str
    chosen_documents: Optional[List[str]] = None

class NewConversation(BaseModel):
    system_prompt_id: str
    chosen_documents: Optional[List[str]] = None
    chosen_csvs: Optional[List[str]] = None

class ConversationUpdate(BaseModel):
    conversation_name: str

class ConversationDelete(BaseModel):
    conversation_id: uuid.UUID

# Chat Message: Do not create chat message delete API
class ChatMessageBase(BaseModel):
    chat_message_id: uuid.UUID
    message_data: dict

class ChatMessageResponse(ChatMessageBase):
    created_at: datetime
    parent_message_id: uuid.UUID = None

class ChatMessageCreate(BaseModel):
    conversation_id: Optional[str] = None
    message_content: str
    model_id: int = 1

class ChatDocCreate(ChatMessageCreate):
    chosen_documents: Optional[List[str]] = None
    chosen_csvs: Optional[List[str]] = None

class ChatMessageUpdate(BaseModel):
    message_content: str

class SystemPromptCreate(BaseModel):
    chosen_position: str
    prompt_content: str
    temperature: float = 0.0
    presence_penalty: float = 0.0
    frequency_penalty: float = 0.0