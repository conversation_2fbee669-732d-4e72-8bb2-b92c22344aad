import asyncio
from loguru import logger
import json

from sqlalchemy import update, delete
from sqlalchemy.future import select

from database.database import SessionFactory, get_session
from database.models import Representative, HumanticProfile, Company, Campaign
from utils.humantic_ai_utils.humantic_request_wrapper import (
    create_humantic_profile,
    fetch_humantic_profile,
)
from message_bus.task_publisher import publish_task
from shared_state import app_state

async def update_humantic_profile(message):
    """
    Update a representative's profile with personality data from Humantic AI.
    
    This function processes a message from the queue, creates a Humantic AI profile
    for a LinkedIn profile, waits for analysis to complete, and then updates the
    database with the personality insights. If auto_outreach is enabled, it will
    trigger draft creation after profile analysis is complete.
    
    Args:
        message: The message object from the queue containing the task data
                 with company_id, rep_linkedin, and auto_outreach flag
    """
    try:
        # await asyncio.sleep(1)
        # json_string = message.body.decode()
        # # Parse the message body from JSON
        # message_body = json.loads(json_string)

        # company_id = message_body.get("company_id")
        # auto_outreach = message_body.get("auto_outreach")
        # rep_linkedin = message_body.get("rep_linkedin")
        # logger.info(f"Processing update_humantic_profile task for {rep_linkedin}")

        # # Step 1: Create a Humantic AI profile for the LinkedIn URL
        # create_response = create_humantic_profile(rep_linkedin)

        # if create_response.status_code != 200:
        #     logger.error(f'Failed to create Humantic AI profile for {rep_linkedin}')
        # else:
        #     # Wait for Humantic AI to process the profile
        #     await asyncio.sleep(60)
        #     attempts = 0
        #     async for db in get_session():
        #         try:
        #             # Update representative status to in-progress
        #             stmt = update(Representative).where(Representative.rep_linkedin_address == rep_linkedin).values(
        #                 analysis_status = "IN_PROGRESS"
        #             )
        #             await db.execute(stmt)        
        #             await db.commit()            
        #             while attempts < 3 :
        #                 attempts += 1
        #                 logger.info(f"Attempt {attempts} fetching humantic profile for {rep_linkedin}")
        #                 # Step 2: Fetch the analyzed profile data
        #                 fetch_response = fetch_humantic_profile(rep_linkedin)
        #                 metadata = fetch_response.json()["metadata"]
        #                 # Check if analysis is complete
        #                 if (metadata["status_code"] in range(1,21) and metadata['status'] == 'FOUND' and metadata['analysis_status']=='COMPLETE'):    
        #                     # Extract all the personality data from the response
        #                     results = fetch_response.json()["results"]
        #                     profile_image = results['user_profile_image']
        #                     user_description = results['user_description']
        #                     work_history = results['work_history']
        #                     location = results['location']
        #                     skills = results['skills']
        #                     followers = results['followers']
        #                     prographics = results['prographics']
        #                     education = results['education']
        #                     cold_calling_advice = results['persona']['sales']['cold_calling_advice']
        #                     communication_advice = results['persona']['sales']['communication_advice']
        #                     email_personalization = results['persona']['sales']['email_personalization']
        #                     hiring_behavioural_factors = results['persona']['hiring']['behavioural_factors']
        #                     personal_analysis = results['personality_analysis']
        #                     sales_profile_url = results['persona']['sales']['profile_url']
        #                     analysis_status = metadata['analysis_status']
        #                     analysis_confidence = metadata['confidence']
                            
        #                     # Step 3: Update the representative record with personality data
        #                     stmt = update(Representative).where(Representative.rep_linkedin_address == rep_linkedin).values(
        #                         profile_image = profile_image,
        #                         user_description = user_description,
        #                         work_history = work_history,
        #                         location = location,
        #                         skills = skills,
        #                         followers = followers,
        #                         prographics = prographics,
        #                         education = education,                       
        #                         personality_analysis = personal_analysis,
        #                         cold_calling_advice = cold_calling_advice,
        #                         communication_advice = communication_advice,
        #                         email_personalization = email_personalization,
        #                         hiring_behavioural_factors = hiring_behavioural_factors,
        #                         sales_profile_url = sales_profile_url,
        #                         analysis_status = analysis_status,
        #                         analysis_confidence = analysis_confidence
        #                     )
        #                     await db.execute(stmt)

        #                     # Step 4: Store the complete profile in the HumanticProfile table
        #                     rep_linkedin_address = rep_linkedin
        #                     rep_linkedin_address = rep_linkedin_address[:-1] if rep_linkedin_address.endswith('/') else rep_linkedin_address        
        #                     linkedin_username = rep_linkedin_address.split('/')[-1]  
                                                    
        #                     # Check if profile already exists in the database
        #                     query = select(HumanticProfile).filter(HumanticProfile.linkedin_username==linkedin_username)
        #                     query_results = await db.execute(query)
        #                     existing_humantic_profile = query_results.scalars().first()
                        
        #                     if existing_humantic_profile is None:
        #                         # Create new profile record if it doesn't exist
        #                         new_humantic_profile = HumanticProfile(
        #                             linkedin_username = linkedin_username,
        #                             linkedin_address = rep_linkedin,
        #                             results = results,
        #                             meta_data = metadata,                                    
        #                         )
        #                         db.add(new_humantic_profile)

        #                     await db.commit()
        #                     logger.info(f"Successfully updated Humantic AI profile for {rep_linkedin}")
        #                     attempts = 0
                            
        #                     # Step 5: If auto_outreach is enabled, trigger draft creation
        #                     if auto_outreach == True:
        #                         # Get the company and campaign details
        #                         query = select(Company).filter(Company.company_id==company_id)
        #                         results = await db.execute(query)
        #                         company = results.scalars().first()

        #                         query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
        #                         results = await db.execute(query)
        #                         campaign = results.scalars().first()

        #                         # Publish task to create drafts using the personality data
        #                         message = {
        #                             "campaign_id": str(campaign.campaign_id),
        #                             "company_id": str(company_id),
        #                             "prompt_name": campaign.prompt_name,
        #                             "auto_outreach": auto_outreach,
        #                             "user_id": str(campaign.user_id)
        #                         }
        #                         await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])
        #                     return

        #                 else:
        #                     # Analysis not complete yet, wait and retry
        #                     logger.warning(f"Analysis is IN_PROGRESS for profile: {rep_linkedin}. Reattempt in 20 seconds")
        #                     await asyncio.sleep(20)
                    
        #             # If we've exhausted all attempts, mark as not found
        #             if attempts != 0:
        #                 stmt = update(Representative).where(Representative.rep_linkedin_address == rep_linkedin).values(
        #                     analysis_status = "NOT_FOUND"
        #                 )
        #                 await db.execute(stmt)
        #                 await db.commit()
        #                 logger.warning(f"Couldn't fetch profile: {rep_linkedin}")
        #         except Exception as e:
        #             await db.rollback()
        #             raise e
        logger.info("This task is no longer used")
    except Exception as e:
        logger.error(f"Failed to process update_humantic_profile task: {e}")
