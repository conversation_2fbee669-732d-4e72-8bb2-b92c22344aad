import React from "react";
import ProductCard from "./ProductCard";
import { SectionWrapper } from "./SectionWrapper";

const products = [
  {
    title: "Mortgage Miscalculation",
    description:
      "If your lender made errors in calculating your mortgage, you may be owed compensation. We help you identify and recover overpayments due to miscalculations.",
    icon: (
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="16" y="32" width="32" height="24" rx="2" fill="#e6efe3"/>
        <polygon points="32,16 12,32 52,32" fill="#d3e2d0"/>
        <rect x="28" y="44" width="8" height="12" rx="1" fill="#bfcdbb"/>
      </svg>
    ),
    href: "/customer_info",
  },
  {
    title: "Plevin PPI",
    description:
      "Were you sold Payment Protection Insurance (PPI) with high undisclosed commissions? You could be eligible for a refund under the Plevin rule. Let us handle your claim.",
    icon: (
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="16" y="12" width="32" height="40" rx="2" fill="#e6efe3"/>
        <rect x="20" y="20" width="24" height="4" rx="1" fill="#f26c2a"/>
        <rect x="20" y="28" width="20" height="4" rx="1" fill="#bfcdbb"/>
        <rect x="20" y="36" width="16" height="4" rx="1" fill="#bfcdbb"/>
      </svg>
    ),
    href: "/customer_info",
  },
  {
    title: "Mis-Sold Vehicle Finance",
    description:
      "If you were misled or not given full information when arranging vehicle finance, you may have a claim. We support you in recovering what you’re owed.",
    icon: (
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="12" y="36" width="40" height="12" rx="4" fill="#e6efe3"/>
        <rect x="20" y="28" width="24" height="12" rx="4" fill="#f26c2a"/>
        <circle cx="20" cy="52" r="4" fill="#bfcdbb"/>
        <circle cx="44" cy="52" r="4" fill="#bfcdbb"/>
      </svg>
    ),
    href: "/customer_info",
  },
];

export function ProductSection() {
  return (
    <SectionWrapper id="products" className="py-16 bg-[#e6efe3]">
      <h2 className="text-3xl md:text-4xl font-bold text-[#3d4b3d] mb-12 text-center">
        Our Supported Claims
      </h2>
      <div className="flex flex-col md:flex-row gap-8 justify-center items-center">
        {products.map((product) => (
          <ProductCard
            key={product.title}
            icon={product.icon}
            title={product.title}
            description={product.description}
            href={product.href}
          />
        ))}
      </div>
    </SectionWrapper>
  );
}
