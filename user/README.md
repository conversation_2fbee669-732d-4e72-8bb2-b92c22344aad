<h1 style="font-size: 36px; font-family: Arial, sans-serif; text-align: center;">
  ROBOMAN - User Service
</h1>

Welcome to the *User Service* documentation of the *ROBOMAN* project. This document provides details on code structures and API references for the User service.

# Table of Contents

1. [Introduction](#introduction)
    - 1.1. [Overview](#overview)
    - 1.2. [Goals](#goals)

2. [Features](#features)

3. [Code Structure](#code-structure)

4. [Technologies](#technologies)

5. [API Reference](#api-reference)

6. [Setup and Installation](#setup-and-installation)

7. [Contribution](#contribution)

# Introduction

## Overview

The User Service is a foundational component of the ROBOMAN platform, responsible for user authentication, authorization, profile management, and credit handling. It integrates with Stripe for payment processing and Unipile for connecting user LinkedIn accounts. The service is built with FastAPI, uses PostgreSQL for data persistence, and communicates with other microservices via RabbitMQ RPC.

## Goals

The primary goals of the User Service are:

- **Centralize User Identity**: Provide a single source of truth for user accounts, authentication, and authorization.
- **Manage User Credits**: Implement a robust system for purchasing, tracking, and spending user credits.
- **Secure and Scalable**: Ensure user data is secure and the service can handle a growing user base.
- **Seamless Integrations**: Integrate smoothly with third-party services like Stripe and Unipile.
- **Inter-Service Communication**: Expose user data to other microservices in a secure and efficient manner through an RPC interface.

# Features

- **User & Authentication Management**:
    - User registration (`/signup`) and login (`/login`).
    - Secure password management with bcrypt hashing.
    - JWT-based authentication.

- **Credit & Payment System**:
    - Integration with Stripe for creating checkout sessions to purchase credits.
    - Webhook handling for successful payments to update user credit balances.
    - Endpoints to view credit balance, purchase history, and usage logs.

- **LinkedIn Integration via Unipile**:
    - Generate authentication URLs for users to connect their LinkedIn accounts.
    - Callback endpoints to handle successful connections and store account details.
    - Manage connection status (connected, disconnected, needs reconnection).

- **Asynchronous RPC**:
    - Implements a RabbitMQ RPC server to respond to requests for user information from other services (e.g., Campaign Service).

- **Database and Migrations**:
    - Uses PostgreSQL with SQLAlchemy ORM for data storage.
    - Manages database schema changes with Alembic migrations.
    - Initial data seeding for roles and admin user.

- **Admin Functionality**:
    - Endpoints for managing Stripe products and prices.
    - Ability for an admin to reset a user's password.

# Code Structure

<pre>
.
│   .env
│   alembic.ini
│   create_db.py
│   Dockerfile
│   main.py
│   README.md
│   requirements.txt
│
├───core
│   │   config.py
│   │   security.py
│
├───crud
│   │   crud_base.py
│   │   crud_user.py
│
├───database
│   │   database.py
│   │   models.py
│
├───message_bus
│   │   rpc_server.py
│   │   user_info_server.py
│   │
│   └───procedure
│           user.py
│
├───migrations
│   │   env.py
│   │   README
│
├───routes
│   │   auth_dependency.py
│   └───v2
│           auth/
│               auth.py
│           system/
│               stripe_payment.py
│               stripe_products.py
│               user.py
│               user_credits.py
│           system_error.py
│
├───schema
│   │   credit_schemas.py
│   │   message_schema.py
│   │   role_schema.py
│   │   stripe_payment_schema.py
│   │   stripe_product_schema.py
│   │   token_schema.py
│   │   user_schema.py
│
└───utils
    │   migrate.py
    │
    └───unipile_utils
            unipile_client.py
            unipile_request_wrapper.py
</pre>

# Technologies

The User Service leverages several key technologies:

- **FastAPI**: Modern, high-performance web framework for building APIs.
- **SQLAlchemy**: SQL toolkit and ORM for database operations.
- **Alembic**: Database migration tool.
- **RabbitMQ (aio-pika)**: Message broker for RPC communication.
- **Stripe API**: For handling credit purchases and payments.
- **Unipile API**: For connecting and managing LinkedIn accounts.
- **Docker**: For containerization.
- **Loguru**: For advanced logging.
- **Pydantic**: For data validation and settings management.
- **bcrypt**: For secure password hashing.

# API Reference

## Authentication Endpoints

- **POST /api/signup**: Create a new user account.
- **POST /api/login**: Authenticate a user and receive a JWT.
- **PUT /api/users/me/password/change**: Allow an authenticated user to change their password.
- **PUT /api/users/password/change/{forgot_token}**: Change password using a verification token.

## User Profile Endpoints

- **GET /api/users/me**: Get the profile information for the current user.
- **PUT /api/users/me/update-info**: Update the profile information for the current user.

## Credit Management Endpoints

- **GET /api/users/credits**: Get the credit balance for the current user.
- **GET /api/users/credits/transactions**: Get the credit purchase history for the current user.
- **GET /api/users/credits/usage**: Get the credit usage history for the current user.
- **PUT /api/users/credits/update-usage**: Log the usage of one credit (typically called by other services).

## Stripe Payment Endpoints

- **POST /api/stripe/checkout/session/create**: Create a Stripe checkout session to buy credits.
- **POST /api/stripe/checkout/session/success**: Webhook endpoint to handle successful payments from Stripe.
- **GET /api/stripe/checkout/session/{checkout_session_id}**: Retrieve details of a checkout session.

## Unipile LinkedIn Integration Endpoints

- **POST /api/users/unipile/linkedin/auth-url**: Generate a Unipile URL for connecting a LinkedIn account.
- **POST /api/users/unipile/linkedin/reconnect-auth-url**: Generate a Unipile URL for reconnecting a LinkedIn account.
- **PUT /api/users/unipile/linkedin/disconnect**: Disconnect the user's LinkedIn account.
- **POST /api/users/unipile/linkedin/connect**: Webhook to handle LinkedIn connection events from Unipile.
- **POST /api/users/unipile/linkedin/reconnect**: Webhook to handle LinkedIn reconnection events from Unipile.
- **POST /api/users/unipile/linkedin/status-update**: Webhook to handle LinkedIn status update events from Unipile.

## Admin Endpoints

- **PUT /api/users/admin/password/change**: Allow an admin to reset a user's password.
- **GET /api/users**: Get a list of all users.
- **POST /api/stripe/products/create**: Create a new Stripe product.
- **PUT /api/stripe/products/{product_id}/update**: Update an existing Stripe product.
- **GET /api/stripe/products**: Get a list of all Stripe products.
- **POST /api/stripe/prices/create**: Create a new price for a Stripe product.
- **PUT /api/stripe/prices/{price_id}/update**: Update an existing Stripe price.
- **GET /api/stripe/prices**: Get a list of all Stripe prices.

# Setup and Installation

## Prerequisites

- Docker and Docker Compose
- Python 3.10+
- PostgreSQL
- RabbitMQ
- API keys for Unipile and Stripe

## Environment Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd ROBOMAN/user
   ```

2. Create a `.env` file with the following variables:
   ```
   # Server
   SERVER_PORT=8000
   PROJECT_NAME="ROBOMAN User Service"

   # Database
   DATABASE_NAME=roboman_db
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=postgres
   DATABASE_HOST=postgres
   DATABASE_PORT=5432

   # RabbitMQ
   RABBITMQ_DEFAULT_USER=guest
   RABBITMQ_DEFAULT_PASS=guest
   RABBITMQ_HOST=rabbitmq
   RABBITMQ_PORT=5672
   EXCHANGE_NAME=roboman_exchange

   # JWT
   JWT_SECRET=your_jwt_secret
   JWT_ALGORITHM=HS256

   # Unipile API
   UNIPILE_SUBDOMAIN=your_unipile_subdomain
   UNIPILE_PORT=443
   UNIPILE_API_KEY=your_unipile_api_key

   # Stripe API
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   ```

3. Build and start the service (typically via the root `docker-compose.yml`):
   ```
   docker-compose up --build
   ```

4. The service will be available at `http://localhost:8000` (or as configured in your `docker-compose.yml`) with API documentation at `/docs`.

# Contribution

Kindly refer to the contribution section in the overview documentation here.