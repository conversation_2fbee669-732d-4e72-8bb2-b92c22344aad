import aio_pika
import json
from loguru import logger
from datetime import datetime, timedelta, timezone


from core.config import settings
from message_bus.rabbitmq_connection import RabbitMQConnectionPool
from shared_state import app_state


RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

async def publish_task(task_type, message_body, connection_pool:RabbitMQConnectionPool, delay_seconds=0):
    """
    Publishes a task to the specified task_type queue.

    Args:
        task_type (str): The type of task (routing key and queue name).
        message_body (str): The message body to send.
    """
    # Establish a connection to RabbitMQ
    try:
        async with connection_pool.channel_pool.acquire() as channel:
            try:
                exchange = await channel.declare_exchange(
                    name="delayed_task_exchange",
                    type="x-delayed-message",
                    durable=True,
                    arguments={"x-delayed-type": "direct"}
                )            

                execution_time = datetime.now(timezone.utc) + timedelta(seconds=delay_seconds)
                message_body["scheduled_time"] = execution_time.isoformat()

                delay_ms = delay_seconds * 1000

                # Create a message with the provided body
                message = aio_pika.Message(
                    body=json.dumps(message_body).encode(),
                    delivery_mode=aio_pika.DeliveryMode.PERSISTENT,
                    headers={"x-delay": delay_ms}  # Set delay in milliseconds
                )

                # Publish the message to the exchange with the appropriate routing key
                await exchange.publish(message, routing_key=task_type)
                
                logger.info(f" [PUBLISHER] Sent {task_type} task scheduled for {execution_time}")
            except Exception as e:
                logger.error(f"Failed to publish task: {e}")
    
    except Exception as e:
        logger.error(f"Failed to acquire channel: {e}")