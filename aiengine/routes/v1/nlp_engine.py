from core.config import settings
from database.database import get_session
from database.models import Model, FAQuestion, SystemPrompt, PromptType
from schema.input_message_schema import( InputDataCreate, TextCampaign, 
                                        AudioCreate, GenerateEmail, 
                                        GenerateResponse, GenerateResponseOutput, 
                                        HtmlToText, SimilarityCheck)
from schema.generation_schema import GenerateDrafts, GenerateDraftsTEST
from schema.ai_config_schema import DEFAULT_SYSTEM_PROMPT, EMAIL_GENERATION_V2, CAMPAIGN_INIT, RESPOND_PROMPT
from schema.ai_config_schema import EMBEDDING
from schema.system_prompt_schema import EnablePrompt
from utils.tools.streaming import astreamer
from utils.tools.stt import text_to_speech
from utils.tools.humantic import create_humantic_profile, fetch_humantic_profile
from utils.tools.summary import llm_summarize
from utils.amazon_storage import S3ClientSingleton
from utils.history import history_formatting
from utils.helpers import get_email_gen_prompt
from llama_config.aiservice import run_agent, embed_model
from agent.tools.email_postprocess import postprocess
from agent.tools.campaign_create import campaign_creator, campaign_creator_tool
from message_bus.rpc_client import RPCClient
from message_bus.procedures.generation_procedures import html_to_text, generate_drafts, generate_drafts_v2, generate_drafts_test

from sqlalchemy.future import select
from sqlalchemy import update, delete, and_
from starlette.background import BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import status, File, UploadFile
from fastapi import APIRouter, Header, Depends, status, HTTPException
from fastapi.responses import StreamingResponse
from loguru import logger
import uuid
import time
import pandas as pd
from io import StringIO
import asyncio

from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.program.openai import OpenAIPydanticProgram
from llama_index.core.tools import FunctionTool, ToolMetadata
from llama_index.core.base.embeddings.base import similarity

router = APIRouter(
    prefix = "/api",
    tags=['NLP Engine'],
    responses={404: {'description': 'Not found'}},
)


# Helper function to retrieve model display name from database
async def get_model_name_by_model_id(
    db: AsyncSession,
    model_id: int,
):
    """Get model display name by its ID from the database."""
    model_name = await db.execute(select(Model.display_name).where(Model.model_id == model_id))
    return model_name.scalar()


## Default Chat
@router.post("/llm/stream/default", status_code=status.HTTP_200_OK)
async def default_stream(
    input_data: InputDataCreate,
    bg_tasks: BackgroundTasks,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """Stream LLM responses for default chat with campaign creation tool."""
    # Get model name from database
    model_name = await get_model_name_by_model_id(db, input_data.model_id)

    # Format conversation history
    history = history_formatting(input_data.history)

    # Initialize agent with campaign creator tool
    agent = run_agent(
        model_name=model_name,
        system_prompt=DEFAULT_SYSTEM_PROMPT,
        tools=[campaign_creator_tool],
        history=history
    )

    # Get streaming response
    resp = agent.stream_chat(input_data.request_message)
    
    # Return streaming response
    logger.info('Generating response.....')
    return StreamingResponse(
        astreamer(
            resp=resp,
            model_name=model_name,
            user_id=request_user_id,
            input_data=input_data,
            bg_tasks=bg_tasks,
        ),
        media_type='text/event-stream',
    )


@router.post("/llm/campaign/parser", status_code=status.HTTP_200_OK)
async def parse_campaign_information(
    request_data: TextCampaign,
    request_user_id: str = Header(None)
):
    """Parse unstructured text to extract campaign information."""
    output = campaign_creator(input=request_data.input_str)
    return output


@router.post("/openai/text-to-speech", status_code=status.HTTP_200_OK)
async def get_audio(
    request_data: AudioCreate,
    request_user_id: str = Header(None)
):
    """Convert text to speech and store in S3."""
    # Generate unique ID for audio file
    object_id = uuid.uuid4()
    
    # Summarize text if requested
    msg_input = request_data.text_input
    if request_data.is_summary:
        msg_input = llm_summarize(request_data.text_input)

    # Generate audio file
    audio = text_to_speech(
        object_id=object_id,
        text_input=msg_input
    )

    if audio:
        # Upload to S3 and generate URL
        s3_singleton = S3ClientSingleton(
            settings.S3_ACCESS_ID,
            settings.S3_SECRET_KEY,
            'ap-south-1'
        )
        s3_singleton.upload_file(
            f"./{object_id}.mp3",
            settings.S3_BUCKET_NAME,
            f"{object_id}.mp3"
        )
        url = s3_singleton.generate_url(
            settings.S3_BUCKET_NAME,
            f"{object_id}.mp3"
        )

        return {'image_url': url}
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail='Cannot convert this to audio'
        )



@router.post("/campaign/initiation", status_code=status.HTTP_200_OK)
async def campaign_initiation(
    input_data: TextCampaign,
    request_user_id: str = Header(None),
):
    """Initialize campaign from text description using GPT-4."""
    model_name = "gpt-4-turbo"

    agent = run_agent(
        model_name=model_name,
        system_prompt=CAMPAIGN_INIT,
        history=None,
        tools=None
    )
    resp = agent.chat(input_data.input_str)
    return resp.response

@router.post("/nlp/faqs/upload", status_code=status.HTTP_201_CREATED)
async def upload_faqs(
    uploaded_files: UploadFile = File(...),
    db: AsyncSession = Depends(get_session)    
):
    """Upload CSV of FAQs, generate embeddings, and store in database."""
    # Validate file format
    if not uploaded_files.filename.endswith('.csv'):
        return {"error": "File must be a CSV"}

    try:
        # Process CSV file
        contents = await uploaded_files.read()
        str_file = contents.decode()
        csv_file = StringIO(str_file)
        df = pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False)

        # Extract questions and answers
        questions_text_list = df[df.columns[0]].to_list()
        answers_text_list = df[df.columns[-1]].to_list()
        
        # Generate embeddings for questions
        questions_embeddings_list = embed_model.get_text_embedding_batch(texts=questions_text_list)

        # Create database records
        questions_list = []
        for i in range(len(questions_embeddings_list)):
            questions_list.append(
                FAQuestion(
                    question_text = questions_text_list[i],
                    question_embedding = questions_embeddings_list[i],
                    answer_text = answers_text_list[i],
                )
            )

        # Save to database
        db.add_all(questions_list)
        await db.commit()
        return {
            "status": "success",
            "detail": f"indexed {len(questions_embeddings_list)} records"
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )        


@router.post("/nlp/response/generate")
async def generate_response(
    input_str: GenerateResponse
):
    """Generate structured response to a message using GPT-4."""
    llm = OpenAI(
        model='gpt-4o-mini',
        temperature=0,
        api_key=settings.OPENAI_API_KEY
    )
    program = OpenAIPydanticProgram.from_defaults(
        output_cls=GenerateResponseOutput,
        verbose=False,
        llm=llm,
        prompt_template_str=RESPOND_PROMPT
    )
    output = program(query=input_str)
    return output    
    

@router.post("/nlp/generate_drafts")
async def generate_email_drafts(
    generate_drafts_payload: GenerateDrafts
):
    """Generate email drafts based on campaign parameters."""
    try:
        params = generate_drafts_payload.dict()
        results = await generate_drafts(**params)
        return results
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/nlp/generate_drafts/test")
async def generate_email_drafts(
    generate_drafts_payload: GenerateDraftsTEST
):
    """Generate email drafts based on campaign parameters."""
    try:
        params = generate_drafts_payload.dict()
        results = await generate_drafts_test(**params)
        return results
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/nlp/generate_drafts/v2")
async def generate_email_drafts_v2(
    generate_drafts_payload: GenerateDrafts
):
    """Generate email drafts using improved v2 algorithm."""
    try:
        params = generate_drafts_payload.dict()
        results = await generate_drafts_v2(**params)
        return results
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.post("/test/rpc")
async def test_rpc():
    """Test RPC connection to campaign service."""
    rpc = await RPCClient().connect()
    response = await rpc.call(service="campaign.*", function_name="test_function", params={"test_number": 1})
    logger.info(response)
    logger.info(response)
    logger.info(response)
    logger.info(response)

    return {
        "status": "success"
    }


@router.get("/nlp/prompts/email-gen")
async def get_email_gen_prompts(
    db: AsyncSession = Depends(get_session)
):
    """Get all email generation prompts from database."""
    query = select(SystemPrompt).where(SystemPrompt.prompt_type==PromptType.EMAIL_GEN)
    result = await db.execute(query)
    prompts = result.scalars().all()
    return prompts


@router.put("/nlp/prompts/email-gen/enable")
async def enable_email_gen_prompt(
    enalbe_prompt: EnablePrompt,
    db: AsyncSession = Depends(get_session)
):
    """Enable specific email prompt and disable all others."""
    query = select(SystemPrompt).where(SystemPrompt.prompt_id==enalbe_prompt.prompt_id)
    results = await db.execute(query)
    prompt = results.scalars().first()
    if prompt:
        # Disable all other prompts
        stmt = update(SystemPrompt).filter(SystemPrompt.prompt_type==PromptType.EMAIL_GEN, SystemPrompt.prompt_id!=enalbe_prompt.prompt_id).values(is_disabled=True)
        await db.execute(stmt)
        # Enable selected prompt
        stmt = update(SystemPrompt).filter(SystemPrompt.prompt_id==enalbe_prompt.prompt_id).values(is_disabled=False)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    else:
        await db.rollback()
        raise HTTPException(
            status_code=404,
            detail="Prompt not found"
        )


@router.post("/nlp/text-embedding")
async def get_text_embedding():
    """Generate embeddings for sample text."""
    embeddings = embed_model._get_text_embeddings(texts=["hello world"])
    return embeddings
    

@router.post("/nlp/html-to-plain-text")
async def html_to_plain_text(
    html_content: HtmlToText
):
    """Convert HTML to plain text."""
    result = await html_to_text(html_content.html_content)
    text = result["plain_text"]
    return {
        "plain_text": text
    }


@router.post("/nlp/similarity")
async def get_similarity(
    similarity_check: SimilarityCheck
):
    """Calculate semantic similarity between two texts."""
    # Generate embeddings
    embedding_1 = embed_model._get_text_embeddings(texts=[similarity_check.text_1])[0]
    embedding_2 = embed_model._get_text_embeddings(texts=[similarity_check.text_2])[0]
    
    # Calculate similarity score
    similarity_score = similarity(embedding_1, embedding_2)
    
    return {
        "similarity_score": float(similarity_score)
    }


llm_routes = router


    
