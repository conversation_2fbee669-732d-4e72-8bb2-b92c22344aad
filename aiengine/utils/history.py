from llama_index.core.llms import ChatMessage, MessageRole
from typing import Optional, List


def history_formatting(request_history: Optional[List[dict]] = None):
    history = [
        ChatMessage(role=role, content=data.get(role))
        for data in request_history for role in [MessageRole.USER, MessageRole.ASSISTANT]
        if data.get(role) is not None
    ] if request_history else []

    return history