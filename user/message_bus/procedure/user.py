import requests
import json
from loguru import logger
from sqlalchemy import update, delete
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
import uuid

from database.database import SessionFactory
from database.models import User

async def get_user_info(
  user_id: str      
) -> dict:
    db = SessionFactory()
    with db.begin():
        query = select(User).filter(User.user_id==user_id)
        results = db.execute(query)
        user = results.scalars().first()
        if user is not None:
            response = {
                "user_id": str(user.user_id), 
                "user_info": user.user_info,
                "user_linkedin_info": user.user_linkedin_info,
                "unipile_linkedin_id": user.unipile_linkedin_id,
                "is_active": user.is_active,
                "birth_year": user.birth_year,
                "email": user.email,
                "verified": user.verified,
                "linkedin_connection_status": user.linkedin_connection_status
            }
            return response
        else:
            return None
    
    
