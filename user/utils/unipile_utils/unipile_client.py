import requests
import json

from core.config import settings
from utils.unipile_utils.unipile_request_wrapper import unipile_get, unipile_post, unipile_delete


class UnipileClient:
    """
    A base client for interacting with the Unipile API.
    It handles common properties like subdomain, port, API key, and account ID.
    """
    unipile_subdomain: str = settings.UNIPILE_SUBDOMAIN
    unipile_port: str = settings.UNIPILE_PORT    
    unipile_api_key: str = settings.UNIPILE_API_KEY
    unipile_account_id: str = None

    def __init__(self):
        """Initializes the UnipileClient."""
        pass    

    def set_unipile_account_id(self, value: str) -> None:
        """
        Sets the Unipile account ID for the client instance.

        Args:
            value (str): The Unipile account ID.
        """
        self.unipile_account_id = value    

    def retrieve_account(self) -> requests.Response:
        """
        Retrieves the details of the currently set Unipile account.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        endpoint_path = f"/accounts/{self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def delete_account(self, unipile_account_id: str) -> requests.Response:
        """
        Deletes a Unipile account.

        Args:
            unipile_account_id (str): The ID of the Unipile account to delete.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        endpoint_path = f"/accounts/{unipile_account_id}"
        response = unipile_delete(endpoint_path=endpoint_path)
        return response    

class LinkedInClient(UnipileClient):
    """
    A client for handling LinkedIn-specific actions through the Unipile API.
    Inherits from UnipileClient.
    """
    def __init__(self):
        """Initializes the LinkedInClient."""
        pass

    def retrieve_profile(self, profile_url: str) -> requests.Response:
        """
        Retrieves a LinkedIn profile using its URL.

        Args:
            profile_url (str): The full URL of the LinkedIn profile.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        # Extracts the profile ID from the URL. Assumes a specific URL structure.
        profile_id = profile_url.split('/')[-2]
        endpoint_path = f"/users/{profile_id}?account_id={self.unipile_account_id}"

        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_own_profile(self) -> requests.Response:
        """
        Retrieves the profile of the currently authenticated user.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        endpoint_path = f"/users/me?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_connections(self) -> requests.Response:
        """
        Retrieves the connections of the currently authenticated user.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        endpoint_path = f"/users/relations?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def send_message(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        """
        Sends a message to a LinkedIn user.

        Args:
            message_body (str): The content of the message to send.
            recipient_profile_url (str): The LinkedIn profile URL of the recipient.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        # First, retrieve the recipient's profile to get their provider_id.
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]
        
        endpoint_path = f"/chats"
        boundary = "-----011000010111000001101001"
        # Manually construct the multipart/form-data payload.
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"attendees_ids\"\r\n\r\n"
            f"{recipient_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"text\"\r\n\r\n"
            f"{message_body}\r\n"
            f"{boundary}--"
        )
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response

    def send_invitation(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        """
        Sends a connection invitation to a LinkedIn user.

        Args:
            message_body (str): The personalized message to include with the invitation.
            recipient_profile_url (str): The LinkedIn profile URL of the recipient.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """
        endpoint_path = "/users/invite"
        # First, retrieve the recipient's profile to get their provider_id.
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]
        # Construct the JSON payload for the invitation.
        payload = {
            "provider_id": recipient_id,
            "account_id": self.unipile_account_id,
            "message": message_body
        }   
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

class EmailClient(UnipileClient):
    """
    A client for handling email-specific actions through the Unipile API.
    Inherits from UnipileClient.
    """
    def __init__(self):
        """Initializes the EmailClient."""
        pass

    def send_email(self, subject: str, content: str, name_from: str, name_to: str, email_to: str) -> requests.Response:
        """
        Sends an email.

        Args:
            subject (str): The subject of the email.
            content (str): The body content of the email.
            name_from (str): The display name of the sender.
            name_to (str): The display name of the recipient.
            email_to (str): The email address of the recipient.

        Returns:
            requests.Response: The HTTP response from the Unipile API.
        """

        # Retrieve the account details to get the sender's email address.
        resp = self.retrieve_account()     

        if resp.status_code != 200:
            return resp

        email_account = resp.json()
        email_from = email_account['email']

        boundary = "-----011000010111000001101001"
        recipient_info = [
            {
                "display_name": name_to.title(),
                "identifier": email_to
            }            
        ]

        sender_info = {
            "display_name": name_from,
            "identifier": email_from            
        }
        
        endpoint_path = "/emails"
        
        # Manually construct the multipart/form-data payload.
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"subject\"\r\n\r\n"
            f"{subject}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"body\"\r\n\r\n"
            f"{content}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"from\"\r\n\r\n"
            f"{json.dumps(sender_info)}\r\n"
            f"{boundary}\r\n"            
            "Content-Disposition: form-data; name=\"to\"\r\n\r\n"
            f"{json.dumps(recipient_info)}\r\n"
            f"{boundary}--"
        )        
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response