FROM python:3.9.18-slim-bookworm

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONBUFFERED=1
ENV HNSWLIB_NO_NATIVE=1
ENV TOKENIZERS_PARALLELISM=false

# # Install core dependencies, since we use postgres 
RUN apt-get update \
    && apt-get install -y libpq-dev build-essential \
    && apt-get install -y ca-certificates libssl-dev openssl \
    && apt-get install -y curl \
    && apt-get clean

RUN pip install --upgrade pip \
    && pip install --no-cache-dir setuptools wheel
#     && pip install --no-cache-dir --no-deps sentence-transformers==2.2.2 \
#     && pip install --no-cache-dir torch==2.0.1+cpu torchvision==0.15.2+cpu -f https://download.pytorch.org/whl/torch_stable.html \
#     && pip install --no-cache-dir transformers optimum[exporters]

COPY requirements.txt .

RUN pip install -r requirements.txt --no-cache-dir

COPY . .

EXPOSE 8000

CMD python main.py