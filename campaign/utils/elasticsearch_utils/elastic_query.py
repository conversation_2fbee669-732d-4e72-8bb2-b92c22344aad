from core.config import settings
from elasticsearch import Elasticsearch
from typing import List
from loguru import logger
from message_bus.rpc_client import RPCClient
import json
import re


def advanced_string_matching_search(
    es_client: Elasticsearch,
    search_size: int,
    industries: list[str] = None,
    countries: list[str] = None,
    cities_or_states: list[str] = None,
    continents: list[str] = None,
    existing_company_urls: list[str] = None,
):
    """
    Performs an advanced search in Elasticsearch with specific AND/OR conditions
    for industries, countries, cities/states, and continents.

    Args:
        es_client: The Elasticsearch client instance.
        search_size: The maximum number of results to return.
        industries: A list of industries to search for (OR condition).
        countries: A list of countries to search for (OR condition).
        countries: A list of countries to search for (OR condition).
        cities_or_states: A list of cities or states to search for (OR condition).
        continents: A list of continents to search for (OR condition).
        existing_company_urls: A list of company LinkedIn URLs to exclude from the search.
    """
    if industries is None:
        # Initialize lists if they are None to avoid errors
        industries = []
    if countries is None:
        countries = []
    if cities_or_states is None:
        cities_or_states = []
    if continents is None:
        continents = []
    if existing_company_urls is None:
        existing_company_urls = []

    # Base structure for the Elasticsearch query
    query = {
        "query": {
            "bool": {
                "must": [],
                "must_not": [
                    {"match_phrase": {"Company Linkedin Url": url}}
                    for url in existing_company_urls
                ],
            }
        },
        "size": search_size,
        # "_source": {
        #     "excludes": [
        #         "Company Industry embedding",
        #         "Company Location Name embedding",
        #         "Company Location Continent embedding",
        #     ]
        # },
    }

    # Add a "should" clause for industries, requiring at least one match.
    # This creates a query like: (industry_A OR industry_B OR ...)
    if industries:
        industry_query = {
            "bool": {
                "should": [
                    {"match_phrase": {"Company Industry": industry}}
                    for industry in industries
                ],
                "minimum_should_match": 1,
            }
        }
        query["query"]["bool"]["must"].append(industry_query)

    # Add a "should" clause for countries, requiring at least one match.
    # This creates a query like: (country_A OR country_B OR ...)
    if countries:
        country_query = {
            "bool": {
                "should": [
                    {"match_phrase": {"Company Location Country": country}}
                    for country in countries
                ],
                "minimum_should_match": 1,
            }
        }
        query["query"]["bool"]["must"].append(country_query)

    # Add a nested "should" clause for cities or states.
    # This creates a query like: ((city/state_A in Locality OR city/state_A in Region) OR (city/state_B in Locality OR city/state_B in Region)...)
    if cities_or_states:
        # Each city/state can be in either Locality or Region (OR)
        # The document must match at least one of the provided cities/states (OR)
        cities_or_states_query = {
            "bool": {
                "should": [],
                "minimum_should_match": 1,
            }
        }
        for cs in cities_or_states:
            cs_query = {
                "bool": {
                    "should": [
                        {"match_phrase": {"Company Location Locality": cs}},
                        {"match_phrase": {"Company Location Region": cs}},
                    ],
                    "minimum_should_match": 1,
                }
            }
            cities_or_states_query["bool"]["should"].append(cs_query)
        query["query"]["bool"]["must"].append(cities_or_states_query)

    # Add a "should" clause for continents, requiring at least one match.
    # This creates a query like: (continent_A OR continent_B OR ...)
    if continents:
        continent_query = {
            "bool": {
                "should": [
                    {"match_phrase": {"Company Location Continent": continent}}
                    for continent in continents
                ],
                "minimum_should_match": 1,
            }
        }
        query["query"]["bool"]["must"].append(continent_query)

    # If no positive search criteria are provided, return an empty list to avoid an empty search.
    if not query["query"]["bool"]["must"]:
        logger.info("No search criteria provided. Returning empty result.")
        return []

    try:
        # Execute the search query.
        search_result = es_client.search(index=settings.ES_INDEX_NAME, body=query)["hits"]["hits"]
        result = [item["_source"] for item in search_result]

        # Filter out duplicates based on 'Company Linkedin Url' to ensure unique companies.
        unique_results = []
        seen_urls = set()

        for item in result:
            company_linkedin_url = item.get("Company Linkedin Url")
            if company_linkedin_url and company_linkedin_url not in seen_urls:
                unique_results.append(item)
                seen_urls.add(company_linkedin_url)

        logger.info(f"Found {len(unique_results)} companies from advanced string matching")
        return unique_results

    except Exception as e:
        logger.error(f"An error occurred during Elasticsearch search: {e}")
        return []

def test_string_matching_search(
    es_client: Elasticsearch, 
    query: dict
):
    """
    Executes a given Elasticsearch query and returns unique results.

    This function is intended for testing purposes, allowing direct execution
    of a pre-constructed query dictionary. It also handles deduplication
    based on the company's LinkedIn URL.

    Args:
        es_client: The Elasticsearch client instance.
        query: The Elasticsearch query dictionary.

    Returns:
        A list of unique company documents from the search results.
    """
    search_result = es_client.search(index=settings.ES_INDEX_NAME, body=query).body['hits']['hits']
    result = [item['_source'] for item in search_result]

    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = []
    seen_urls = []

    for item in result:
        company_linkedin_url = item['Company Linkedin Url']
        if company_linkedin_url not in seen_urls:
            unique_results.append(item)
            seen_urls.append(company_linkedin_url)
            
    return unique_results


def search_primary_rep(es_client: Elasticsearch, company_linkedin_address: str):
    """
    Searches for a primary representative of a company based on job titles.

    It first searches for top-level executive titles. If none are found,
    it then searches for secondary management titles.

    Args:
        es_client: The Elasticsearch client instance.
        company_linkedin_address: The LinkedIn URL of the company to search within.

    Returns:
        A list containing the primary representative's document, or an empty list if not found.
    """
    # Define job titles, prioritized by seniority.
    top_brass = ['chief executive officer', 'chief operating officer', 'chief marketing officer']
    subordinate = ['president','manager','director']
    cc_list = top_brass + subordinate

    # First, search for top-level executives.
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "match_phrase": {
                            "Company Linkedin Url": company_linkedin_address
                        }
                    }
                ],
                "should" :[
                    {
                        "match_phrase": {
                            "Job title": term
                        }
                    } for term in top_brass
                ]
            }
        },
        "size": 1
    }

    search_result = es_client.search(index=settings.ES_INDEX_NAME, body=query).body['hits']['hits']
    result = [item['_source'] for item in search_result]

    unique_results = [] + result

    # If no top-level execs are found, search for subordinate managers/directors.
    if len(unique_results) < 1:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase": {
                                "Company Linkedin Url": company_linkedin_address
                            }
                        }
                    ],
                    "should" :[
                        {
                            "match_phrase": {
                                "Job title": term
                            }
                        } for term in subordinate
                    ]
                }
            },
            "size": 1
        }
        search_result = es_client.search(index=settings.ES_INDEX_NAME, body=query).body['hits']['hits']
        result = [item['_source'] for item in search_result]    

        unique_results += result
            
    return unique_results