import asyncio
import json
from loguru import logger

from database.models import SenderEmail, Company, Representative, Draft, LinkedInConnection, Campaign
from database.database import get_session
from message_bus.task_publisher import publish_task
from shared_state import app_state
from message_bus.rpc_client import RPCClient
from utils.unipile_utils.unipile_client import LinkedInClient

from datetime import timedelta, date, datetime
from sqlalchemy.future import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import  AsyncSession

async def reset_remaining_emails():
    """
    A scheduled task that resets the daily email sending quota for all sender accounts.
    
    This is typically run once a day at midnight.
    """
    await asyncio.sleep(1)
    async for db in get_session():
        try:
            stmt = update(SenderEmail).values(remaining_emails=21)
            await db.execute(stmt)
            await db.commit()
            logger.info("[CRON JOB] Remaining emails reset successfully")
        except Exception as e:
            await db.rollback()
            raise e

async def get_sender(db: AsyncSession):
    """
    Selects the best available sender email account from the database.

    The "best" sender is determined by the one with the most remaining emails,
    and secondarily by the one that was least recently used.

    Args:
        db: The database session.
    """
    try:
        # Query for a sender with remaining emails, ordered by quota and last usage.
        query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc(), SenderEmail.updated_at.asc())
        results = await db.execute(query)
        senders = results.scalars().all()
        if len(senders) == 0:
            return None
        else:
            sender = senders[0]
            return {
                "sender_address": sender.sender_address,
                "unipile_account_id": sender.unipile_account_id
            }
    except Exception as e:
        raise e

async def start_auto_outreach():
    """
    A scheduled task to initiate the auto-outreach process for new companies.

    This task runs periodically, finds campaigns with auto-outreach enabled,
    and processes a batch of companies that have not yet started the sequence.
    It triggers personality analysis or draft creation as the first step.
    """
    await asyncio.sleep(1)
    try:
        today = date.today()
        # Skip running on weekends.
        if today.weekday() in [5,6]:
            return
        async for db in get_session():
            # Get all campaigns that are set to auto-outreach.
            query = ( # Get all campaigns that are set to auto-outreach.
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                # Find companies in the campaign that are not yet in the auto-outreach sequence.
                query = select(Company).filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==False)
                results = await db.execute(query)
                companies = results.scalars().all()
                if len(companies) > 0:
                    # Process a limited batch of companies to avoid overwhelming the system.
                    query = (
                        select(Company)
                        .filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==False)
                        .order_by(Company.company_name.asc())
                        .limit(10)
                    )
                    results = await db.execute(query)
                    companies = results.scalars().all()

                    for company in companies:
                        # Get the representative and check for existing drafts.
                        query = select(Representative).filter(Representative.company_id==company.company_id)
                        results = await db.execute(query)
                        representative = results.scalars().first()

                        query = select(Draft).filter(Draft.company_id==company.company_id, Draft.draft_type==0)
                        results = await db.execute(query)
                        draft = results.scalars().first()

                        # # If personality analysis is missing, trigger a task to fetch it.
                        # if representative.personality_analysis is None:                        
                        #         message = {
                        #             "rep_linkedin": representative.rep_linkedin_address,
                        #             "company_id": str(company.company_id),
                        #             "auto_outreach": True,
                        #         }
                        #         await publish_task("update_humantic_profile", message, connection_pool=app_state["rabbitmq_conn_pool"])

                        # If analysis exists but no drafts, trigger a task to create them.
                        if draft is None:
                            message = {
                                "campaign_id" : str(campaign.campaign_id),
                                "company_id": str(company.company_id),
                                "prompt_name": campaign.prompt_name,
                                "auto_outreach": True,
                                "user_id": str(campaign.user_id)
                            }
                            await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])

                        # If drafts already exist, mark the company as started in the auto-outreach sequence.
                        elif draft is not None:
                            stmt = update(Company).where(Company.company_id==company.company_id).values(auto_outreach=True)
                            await db.execute(stmt) 
                            await db.commit()
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run handle_auto_outreach job: {str(e)}")


async def continue_auto_linkedin_outreach():
    """
    A scheduled task to continue the automated LinkedIn outreach sequence.

    This task finds companies currently in an auto-outreach sequence and sends
    the next follow-up message if the appropriate time delay has passed.
    """
    await asyncio.sleep(1)
    try:
        today = date.today()
        # Skip running on weekends.
        if today.weekday() in [5,6]:
            return        
        async for db in get_session():
            # Get all campaigns that are set to auto-outreach.
            query = ( # Get all campaigns that are set to auto-outreach.
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                auto_send_linkedin = False
                # Check if the user's LinkedIn account is connected and valid.
                rpc_client = RPCClient()
                rpc = await rpc_client.connect()
                function_name = "get_user_info"
                response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": str(campaign.user_id)})
                response_body = json.loads(response)
                result = response_body.get("result")
                if result != "error":
                    user_info = result
                    if user_info.get("unipile_linkedin_id") is not None:
                        linkedin_client = LinkedInClient()
                        linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
                        own_profile = linkedin_client.retrieve_own_profile().json()
                        if "provider_id" in own_profile and user_info.get("linkedin_connection_status") == "CONNECTED":
                            auto_send_linkedin = True
                        else:
                            logger.warning(f"[CRON JOB] User {campaign.user_id} is not connected to linkedin")
                    else:
                        logger.warning(f"[CRON JOB] User {campaign.user_id} is not connected to linkedin")
                else:
                    logger.warning(f"[CRON JOB] Failed to get user info: {result}")

                if auto_send_linkedin == True:
                    # Find companies in this campaign that are in auto-outreach and have unsent LinkedIn drafts.
                    query = ( # Find companies in this campaign that are in auto-outreach and have unsent LinkedIn drafts.
                        select(Company)
                        .join(Draft, Draft.company_id == Company.company_id)
                        .filter(
                            Draft.linkedin_sent == False,
                            Company.campaign_id == campaign.campaign_id,
                            Company.auto_outreach == True,
                            Company.linkedin_message_status_id.notin_([4, 5])
                        )
                        .distinct(Company.company_id)
                        .order_by(Company.company_id.asc(), Company.company_name.asc(), Draft.draft_type.asc())
                        .limit(10)
                    )
                    
                    results = await db.execute(query)
                    companies = results.scalars().all()
                    logger.info(f"[CRON JOB] Number of companies: {len(companies)}")

                    for company in companies:
                        logger.info(f"[CRON JOB] Processing company: {company.company_name}")
                        # Get the representative and the next unsent draft.
                        query = select(Representative).filter(Representative.company_id==company.company_id)
                        results = await db.execute(query)
                        representative = results.scalars().first()

                        if representative.rep_linkedin_address is None:
                            continue

                        query = select(Draft).filter(Draft.company_id==company.company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
                        results = await db.execute(query)
                        draft = results.scalars().first()

                        if draft is not None:
                            # Check if it's time to send the next message based on the delay.
                            today = date.today()
                            now = datetime.now()

                            to_send = False

                            # last_time_sent = company.last_time_sent_linkedin
                            # if last_time_sent == None:
                            #     to_send = True   
                            # else:                        
                            #     time_to_send = last_time_sent + timedelta(minutes=draft.day_to_send)
                            #     if time_to_send <= now:
                            #         to_send = True

                            last_time_sent = company.last_date_sent_linkedin
                            # If it's the first message, send immediately.
                            if last_time_sent == None:
                                to_send = True
                            else:
                                time_to_send = last_time_sent + timedelta(days=draft.day_to_send)
                                if time_to_send <= today:
                                    to_send = True     

                            if to_send:
                                # Publish a task to send the LinkedIn message.
                                message = {
                                    "company_id": str(company.company_id),
                                    "user_id": str(campaign.user_id)
                                }
                                await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run continue_auto_outreach job: {str(e)}")


async def continue_auto_email_outreach():
    """
    A scheduled task to continue the automated email outreach sequence.

    This task finds companies currently in an auto-outreach sequence and sends
    the next follow-up email if the appropriate time delay has passed.
    """
    await asyncio.sleep(1)
    try:
        today = date.today()
        # Skip running on weekends.
        if today.weekday() in [5,6]:
            return
        async for db in get_session():
            # Check if there are any sender accounts with remaining daily quota.
            sender = await get_sender(db=db)
            if sender is None:
                logger.warning("[CRON JOB] No more email senders available")
                return

            # Get all campaigns that are set to auto-outreach.
            query = ( # Get all campaigns that are set to auto-outreach.
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                # Find companies in this campaign that are in auto-outreach and have unsent email drafts.
                query = ( # Find companies in this campaign that are in auto-outreach and have unsent email drafts.
                    select(Company)
                    .join(Draft, Draft.company_id == Company.company_id)
                    .filter(
                        Draft.email_sent == False,
                        Company.campaign_id == campaign.campaign_id,
                        Company.auto_outreach == True,
                        Company.email_confirmation_status_id.notin_([6, 7])
                    )
                    .distinct(Company.company_id)
                    .order_by(Company.company_id.asc(), Company.company_name.asc(), Draft.draft_type.asc())
                    .limit(10)
                )

                results = await db.execute(query)
                companies = results.scalars().all()
                logger.info(f"[CRON JOB] Number of companies: {len(companies)}")

                for company in companies:
                    logger.info(f"[CRON JOB] Processing company: {company.company_name}")
                    # Get the representative and the next unsent draft.
                    query = select(Representative).filter(Representative.company_id==company.company_id)
                    results = await db.execute(query)
                    representative = results.scalars().first()

                    if representative.rep_email is None:
                        continue

                    query = select(Draft).filter(Draft.company_id==company.company_id, Draft.email_sent==False).order_by(Draft.draft_type.asc())
                    results = await db.execute(query)
                    draft = results.scalars().first()
                    if draft is not None:
                        # Check if it's time to send the next email based on the delay.
                        today = date.today()
                        now = datetime.now()

                        to_send = False

                        # last_time_sent = company.last_time_sent_email
                        # if last_time_sent == None:
                        #     to_send = True
                        # else:
                        #     time_to_send = last_time_sent + timedelta(minutes=draft.day_to_send)
                        #     if time_to_send <= now:
                        #         to_send = True

                        last_time_sent = company.last_date_sent_email                        
                        # If it's the first email, send immediately.
                        if last_time_sent == None:
                            to_send = True
                        else:
                            time_to_send = last_time_sent + timedelta(days=draft.day_to_send)
                            if time_to_send <= today:
                                to_send = True   

                        if to_send:
                            # Publish a task to send the email.
                            message = {
                                "company_id": str(company.company_id)
                            }
                            await publish_task("send_email_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run continue_auto_email_outreach job: {str(e)}")
