import json
import requests
import asyncio
from loguru import logger
from bs4 import BeautifulSou<PERSON>
from sqlalchemy import update, delete, and_
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from llama_index.llms.openai import OpenAI
from llama_index.program.openai import OpenAIPydanticProgram
from llama_index.core.tools import FunctionTool, ToolMetadata

from database.models import SystemPrompt, PromptType
from message_bus.task_publisher import publish_task
from schema.ai_config_schema import EMAIL_GENERATION_V2, FOLLOW_UP, PARSING_FOLLOW_UP
from schema.input_message_schema import FollowUp
from llama_config.aiservice import run_agent
from agent.tools.email_postprocess import postprocess
from message_bus.rpc_client import RPCClient
from core.config import settings
from database.database import get_session
from utils.helpers import get_email_gen_prompt

async def generate_email(message):
    '''
    message_body structure
        from_name: str
        to_name: str
        to_address: str
        core_service: str
        key_benefits: list[str]
        problem_solved: str
        must_have_info: Optional[str]
        output_format: Optional[str]
        personality_analysis: Optional[dict]
        company_id: str
        industry: str
        company_name: str
        auto_send: bool
    '''

    try:
        logger.info(f"[CONSUMER] Processing generate_email task")

        json_string = message.body.decode()
        message_body = json.loads(json_string)
        input_data = message_body

        from_name = input_data["from_name"]
        to_name = input_data["to_name"]
        to_address = input_data["to_address"]
        core_service = input_data["core_service"]
        key_benefits = ', '.join(input_data["key_benefits"])
        problem_solved = input_data["problem_solved"]
        must_have_info = input_data["must_have_info"] if input_data["must_have_info"] else 'none'
        company_id = input_data["company_id"]
        industry = input_data["industry"]
        company_name = input_data["company_name"]
        auto_send = input_data["auto_send"]
        input_str = f'''0. Information
Name of sender or email writer: {from_name}
Name of recipient: {to_name}
Recipient's Company: {company_name}
Recipient's Company's industry: {industry}
Product name: {core_service}
Key Benefits: {key_benefits}
Problem Solved: {problem_solved}
Must have information: {must_have_info}.\n'''
        


        if input_data["personality_analysis"]:
            personal_input = await create_personal_input(input_data["personality_analysis"], input_data["email_personalization"])
            input_str = input_str + personal_input

        async for session in get_session():
            email_gen_prompt = await get_email_gen_prompt(session)    

        model_name = "gpt-4-turbo"
        agent = run_agent(
            model_name=model_name,
            system_prompt=email_gen_prompt,
            history=None
        )   
        resp = agent.chat(input_str)
        email_strings = resp.response.split("----------")

#         email_strings = """Subject: Balance

# <!DOCTYPE html>
# <html>
# <body>
#     <p>Hi Quyen,</p>

#     <p>Our <b>Work-life Balance Coaching and Consulting</b> is designed to put you back in control. By focusing on personalized coaching sessions, you'll discover proven strategies to tackle the overwhelm and burnout caused by a hectic lifestyle.</p>

#     <p>We deliver:</p>
#     <ul>
#         <li>Customized strategies tailored to your needs.</li>
#         <li>Flexible scheduling to accommodate your busy life.</li>
#         <li>A supportive community for ongoing encouragement.</li>
#     </ul>
    
#     <p>When can we discuss how to start this life-changing journey for you?</p>
    
#     <p>Robert Mandev</p>
# </body>
# </html>

# ----------

# Subject: Thrive

# <!DOCTYPE html>
# <html>
# <body>
#     <p>Hi Quyen,</p>

#     <p>Our <b>Work-life Balance Coaching and Consulting</b> equips you with the tools necessary to elevate your productivity and well-being. Overcome the challenges of modern life with sessions that adjust to your fast-paced world.</p>

#     <p>Highlights include:</p>
#     <ul>
#         <li>Adaptive coaching that fits your schedule.</li>
#         <li>Direct approaches that enhance time management.</li>
#         <li>Community support to keep you motivated.</li>
#     </ul>

#     <p>Ready to make the change? Let’s find a time to talk.</p>
    
#     <p>Robert Mandev</p>
# </body>
# </html>

# ----------

# Subject: Decide

# <!DOCTYPE html>
# <html>
# <body>
#     <p>Hi Quyen,</p>

#     <p>Don't let this chance pass—our <b>Work-life Balance Coaching and Consulting</b> can help you claim the life balance you deserve. With our full availability quickly filling, immediate action is crucial to secure your spot.</p>

#     <p>Key offerings:</p>
#     <ul>
#         <li>Exclusive coaching sessions tailored for maximal impact.</li>
#         <li>Effective management strategies backed by success.</li>
#         <li>Access to an empowering community network.</li>
#     </ul>

#     <p>Are you ready to commit to transformative change? Let me know when you're available.</p>
    
#     <p>Robert Mandev</p>
# </body>
# </html>""".split("----------")

        day_of_emails = [0, 3, 6, 10]
        for i in range(len(email_strings)):

            email = postprocess(email_strings[i].strip())
            soup = BeautifulSoup(email["content"])
            body_plain = soup.text
            service = "campaign.*"
            function_name = "create_draft_emails"
            params = {
                "draft_type" : i,
                "company_id" : company_id,
                "to_address" : to_address,
                "to_name" : to_name,
                "subject" : email["head"],
                "body" : email["content"],
                "body_plain" : body_plain,
                "day_to_send": day_of_emails[i]          
            }

            rpc = await RPCClient().connect()
            response = await rpc.call(service=service, function_name=function_name, params=params)
            response_body = json.loads(response)
            logger.info(type(response_body))
            result = response_body.get("result")
            if result == "error":
                logger.error(f"[RPC CLIENT] Failed to generate email {i} for company_id: {company_id}")
                # logger.info(agent_input)
            else:
                logger.info(f"[RPC CLIENT] Successfully {result} email {i} for company_id: {company_id}")

        if auto_send==True:
            rpc = await RPCClient().connect()
            function_name = "auto_send_email"
            params = {
                "company_id" : company_id
            }
            response = await rpc.call(service="campaign.*", function_name=function_name, params=params)
                
    except Exception as e:
        logger.error(f"[RPC_CLIENT] Failed to process generate_email task\n {str(e)}")

async def generate_email_v2(message):
    '''
    message_body structure
        from_name: str\n
        to_name: str
        to_address: str
        core_service: str
        key_benefits: list[str]
        problem_solved: str
        must_have_info: Optional[str]
        output_format: Optional[str]
        personality_analysis: Optional[dict]
        company_id: str
        industry: str
        company_name: str
        prompt_name: Optional[str]
        auto_send: bool
    '''

    try:
        logger.info(f"[CONSUMER] Processing generate_email task")

        json_string = message.body.decode()
        message_body = json.loads(json_string)
        input_data = message_body

        from_name = input_data["from_name"]
        to_name = input_data["to_name"]
        to_address = input_data["to_address"]
        core_service = input_data["core_service"]
        key_benefits = ', '.join(input_data["key_benefits"])
        problem_solved = input_data["problem_solved"]
        must_have_info = input_data["must_have_info"] if input_data["must_have_info"] else 'none'
        company_id = input_data["company_id"]
        industry = input_data["industry"]
        company_name = input_data["company_name"]
        auto_send = input_data["auto_send"]
        input_str = f'''0. Information
Name of sender or email writer: {from_name}
Name of recipient: {to_name}
Recipient's Company: {company_name}
Recipient's Company's industry: {industry}
Product name: {core_service}
Key Benefits: {key_benefits}
Problem Solved: {problem_solved}
Must have information: {must_have_info}.\n'''
        
        number_of_emails = 4
        day_of_emails = [0, 3, 6, 10]

        if input_data["personality_analysis"]:
            personal_input = await create_personal_input(input_data["personality_analysis"], input_data["email_personalization"])
            input_str = input_str + personal_input

        if input_data["communication_advice"] is not None:
            follow_up_advice, day_of_emails = await create_follow_up_input(communication_advice=input_data["communication_advice"], personality_analysis=input_data["personality_analysis"])
            input_str = input_str + follow_up_advice       

        # logger.info(input_str)

        async for session in get_session():
            email_gen_prompt = await get_email_gen_prompt(session, prompt_name=input_data["prompt_name"])

        model_name = "gpt-4-turbo"
        agent = run_agent(
            model_name=model_name,
            system_prompt=email_gen_prompt,
            history=None
        )   
        resp = agent.chat(input_str)
        email_strings = resp.response.split("----------")

        for i in range(len(email_strings)):

            email = postprocess(email_strings[i].strip())
            soup = BeautifulSoup(email["content"])
            body_plain = soup.text
            service = "campaign.*"
            function_name = "create_draft_emails"
            params = {
                "draft_type" : i,
                "company_id" : company_id,
                "to_address" : to_address,
                "to_name" : to_name,
                "subject" : email["head"],
                "body" : email["content"],
                "body_plain" : body_plain,
                "day_to_send": day_of_emails[i]
            }

            # logger.info(params)
            rpc = await RPCClient().connect()
            response = await rpc.call(service=service, function_name=function_name, params=params)
            response_body = json.loads(response)
            logger.info(type(response_body))
            result = response_body.get("result")
            if result == "error":
                logger.error(f"[RPC CLIENT] Failed to generate email {i} for company_id: {company_id}")
                # logger.info(agent_input)
            else:
                logger.info(f"[RPC CLIENT] Successfully {result} email {i} for company_id: {company_id}")

        if auto_send==True:
            rpc = await RPCClient().connect()
            function_name = "auto_send_email"
            params = {
                "company_id" : company_id
            }
            response = await rpc.call(service="campaign.*", function_name=function_name, params=params)                
    except Exception as e:
        logger.error(f"[RPC_CLIENT] Failed to process generate_email_v2 task\n {str(e)}")
    
async def create_follow_up_input(communication_advice: dict, personality_analysis) -> str:
    follow_up_advice = "3. Advice for each email:"
    day_of_emails = [0, 3, 6, 10]
    ocean_assessment = str(personality_analysis["ocean_assessment"])[1:-1]
    disc_assessment = str(personality_analysis["disc_assessment"])[1:-1]

    personality_input = f"1. Recipient's personality analysis:\ndisc_assessment: {disc_assessment}\nocean_assessment: {ocean_assessment}\n"
    follow_up_input = personality_input

    adjectives = ", ".join(communication_advice["adjectives"])
    description = ", ".join(communication_advice["description"])
    follow_up_input = follow_up_input + "\n" + f"2. Communication advice:\n\tAdjectives: {adjectives}\n\tDescription: {description}"    

    # logger.info(follow_up_input)

    model_name = "gpt-4-turbo"
    agent = run_agent(
        model_name=model_name,
        system_prompt=FOLLOW_UP,
        history=None
    )   
    resp = agent.chat(follow_up_input)
    parsing_input = resp.response

    # logger.info(parsing_input)

    try:
        llm = OpenAI(
            model='gpt-4o',
            temperature=0,
            api_key=settings.OPENAI_API_KEY
        )
        program = OpenAIPydanticProgram.from_defaults(
            output_cls=FollowUp,
            verbose=False,
            llm=llm,
            prompt_template_str=PARSING_FOLLOW_UP
        )
        output = program(query=parsing_input) 
        logger.info(str(output.advice_for_emails))
        logger.info(str(output.day_of_emails))
        day_of_emails = output.day_of_emails
        for i in range(len(output.advice_for_emails)):
            follow_up_advice = follow_up_advice + f"\n\t- {output.advice_for_emails[i]}"   
                   
    except Exception as e:
        logger.error(f"Failed to parse follow-up advice: {e}")
    
    return follow_up_advice + "\n", day_of_emails

async def create_personal_input(personality_analysis: dict, email_personalization: dict) -> str:
    ocean_assessment = str(personality_analysis["ocean_assessment"])[1:-1]
    disc_assessment = str(personality_analysis["disc_assessment"])[1:-1]

    personality_input = f"1. Recipient's personality analysis:\ndisc_assessment: {disc_assessment}\nocean_assessment: {ocean_assessment}\n"

    advice = email_personalization["advice"]
    advice.pop("Emojis/GIFs")
    advice.pop("Length Of Mail")
    advice_str = ""

    for key, value in advice.items():
        advice_str += f"\t{key}: {value}\n"

    email_personalization_input = f"2. Email personalization suggestion:\n{advice_str}"

    return personality_input + "\n" + email_personalization_input + "\n"
#just a comment
