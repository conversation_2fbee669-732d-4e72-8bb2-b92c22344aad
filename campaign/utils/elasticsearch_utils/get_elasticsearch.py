from elasticsearch import Elasticsearch
from core.config import settings
from loguru import logger

#local development


def get_elastic() -> Elasticsearch: #type: ignore


    #local development
    # with Elasticsearch(f'http://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), request_timeout=30, retry_on_timeout=True, max_retries=5) as es:
    #uat
    with Elasticsearch(f'https://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), verify_certs=False, request_timeout=30, retry_on_timeout=True, max_retries=5) as es:      
        try:
            yield es
        except Exception as e:
            logger.error(e)
            raise