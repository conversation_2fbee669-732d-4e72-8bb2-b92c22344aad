from config import settings

from loguru import logger
import aiohttp
import async_timeout
import asyncio


async def make_request(
	url: str,
	method: str,
	data: dict = None,
	headers: dict = None
):
	if not data:
		data = {}

	with async_timeout.timeout(settings.GATEWAY_TIMEOUT):
		async with aiohttp.ClientSession() as session:
			request = getattr(session, method)
			async with request(url, json=data, headers=headers) as response:
				data = await response.json()
				return (data, response.status)
		
		
async def streaming(
	url: str,
	method: str,
	data: dict = None,
	headers: dict = None,
):
	if not data:
		data = {}

	with async_timeout.timeout(settings.GATEWAY_TIMEOUT):
		async with aiohttp.ClientSession() as session:
			request = getattr(session, method)
			async with request(url, json=data, headers=headers) as response:
				async for data in response.content.iter_any():
					yield data
					await asyncio.sleep(0.01)


async def file_streaming(
	url: str,
	method: str,
	uploaded_files: dict = None,
	headers: dict = None,
):

    if not uploaded_files:
        uploaded_files = {}

    with async_timeout.timeout(settings.GATEWAY_TIMEOUT):
        async with aiohttp.ClientSession() as session:
            form_data = aiohttp.FormData()

            # Add uploaded files
            for file in uploaded_files:
                file_content = await file.read()
                form_data.add_field('uploaded_files', file_content, filename=file.filename)

            request = getattr(session, method)
            async with request(url, data=form_data, headers=headers) as response:
                async for data_chunk in response.content.iter_any():
                    yield data_chunk


async def make_file_request(
    url: str,
    method: str,
    file_request: dict,
    headers: dict = None
):
    if not file_request:
        file_request = {}
    
    logger.info(file_request)

    file_data = file_request['uploaded_files']
    conversation_id = file_request.get('conversation_id')

    with async_timeout.timeout(settings.GATEWAY_TIMEOUT):
        async with aiohttp.ClientSession() as session:
            form_data = aiohttp.FormData()
            
            if type(file_data) == list:
                for file in file_data:
                    file_content = await file.read()
                    form_data.add_field('uploaded_files', value=file_content, filename=file.filename)
            else:
                file_content = await file_data.read()
                form_data.add_field('uploaded_files', value=file_content, filename=file_data.filename)
            
            if conversation_id:
                form_data.add_field('conversation_id', conversation_id)
                
            request = getattr(session, method)
            async with request(url, data=form_data, headers=headers) as response:
                data = await response.json()
                response_data = (data, response.status)
                
            return response_data

