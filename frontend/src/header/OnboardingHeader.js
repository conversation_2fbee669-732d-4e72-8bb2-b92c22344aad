import React from 'react';
import Logo from '../assets/img/roboman-logo.png'; 
import { useNavigate } from 'react-router-dom';
import VideoPlayer from '../component/onboardingPage/VideoPlayer';
import videonothing from '../assets/saynothing_female.mp4'

const LargeImagePlaceholder = ({videomain}) => {
    return (
      <div className="w-[600px] h-[600px] bg-transparent flex items-center justify-center lg:ml-16 lg:mr-4 pb-4 rounded-md overflow-hidden"> 
      {/* bg-[#223F90] */}
        {/* <img 
          src={manimage} 
          alt="Man" 
          className="w-full h-full object-cover transform scale-105"
        /> */}
        <VideoPlayer video1Url={videomain} sayNothingUrl={videonothing} />
      </div>
    );
};

const OnboardingHeaderPart = ({ enableLogout = true }) => {
  const navigate = useNavigate();
  const handleLogout = () => {
    // Handle the logout logic here, e.g., clear tokens, redirect, etc.
    console.log("User logged out");
    localStorage.clear();
    navigate('/login');

  };

return (
  <header
    className={`w-full h-20 flex items-center justify-between px-4 md:px-12 
      backdrop-blur-lg sticky top-0 z-50 border-b border-blue-800 animate-glow 
      bg-[#102A43]/95`}
  >
    {/* Logo */}
    <div className="flex-none w-[100px] md:w-[150px] flex justify-start items-center">
      <img
        src={Logo} // updated to match main header logo
        alt="Logo"
        className="w-[80px] md:w-[130px] h-[40px] md:h-[50px] object-contain"
      />
    </div>

    {/* Right Side (Logout) */}
    {enableLogout && (
      <button
        onClick={handleLogout}
        className="ml-auto py-2 px-5 bg-blue-600 text-white rounded-full hover:bg-blue-700 
                   font-medium shadow-md transition-all duration-300"
      >
        Logout
      </button>
    )}

    {/* Glow animation */}
    <style jsx>{`
      @keyframes glowPulse {
        0%, 100% {
          box-shadow: 0 0 8px rgba(0, 102, 255, 0.4);
        }
        50% {
          box-shadow: 0 0 14px rgba(0, 102, 255, 0.7);
        }
      }
      .animate-glow {
        animation: glowPulse 4s ease-in-out infinite;
      }
    `}</style>
  </header>
);


};

// Export LargeImagePlaceholder as a named export
export { OnboardingHeaderPart, LargeImagePlaceholder };
