import React, { useEffect, useState } from 'react';
import { OnboardingHeaderPart } from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { AiOutlineLoading } from 'react-icons/ai';

const BillingPage = () => {
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  const [stripePrices, setStripePrices] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getStripePrices = async () => {
      try {
        const response = await fetch(`${roboman_api}/stripe/prices`, {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            Authorization: localStorage.getItem('access_token'),
          },
        });

        if (!response.ok) {
          console.error('Request failed');
        } else {
          const data = await response.json();
          setStripePrices(data);
        }
      } catch (error) {
        console.error('Network or parsing error:', error);
      } finally {
        setLoading(false);
      }
    };
    getStripePrices();
  }, [roboman_api]);

  const createPaymentSession = async (payment_id) => {
    if (!stripePrices[payment_id]) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Selected plan data not available. Please try again.',
      });
      return;
    }

    try {
      const response = await fetch(
        `${roboman_api}/stripe/checkout/session/create`,
        {
          method: 'POST',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: localStorage.getItem('access_token'),
          },
          body: JSON.stringify({
            price_id: stripePrices[payment_id]['price_id'],
            cancel_url: 'https://outreach.roboman.ai/dashboard',
            success_url:
              'https://outreach.roboman.ai/successpayment?session_id={CHECKOUT_SESSION_ID}',
          }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        console.error('Checkout session creation failed');
        Swal.fire({
          icon: 'error',
          title: 'Payment Error',
          text: data.message || 'Failed to initiate payment session.',
        });
      } else {
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Network or parsing error:', error);
      Swal.fire({
        icon: 'error',
        title: 'Network Error',
        text: 'Unable to process payment. Please try again later.',
      });
    }
  };

  const PricingPlans = ({ loading, stripePrices }) => {
    const [selectedPlan, setSelectedPlan] = useState(null);

    if (loading) {
      return (
        <div className="flex flex-col justify-center items-center h-full min-h-[200px]">
          <AiOutlineLoading className="text-5xl text-blue-500 animate-spin" />
          <span className="mt-4 text-lg text-gray-700">Loading pricing plans...</span>
        </div>
      );
    }

    // Sample plans data (static for demo; adjust as needed)
    // Replace with actual stripePrices mapping if available
    const plans = [
      {
        id: 0,
        name: 'Starter Plan',
        price: '£49.99',
        subtitle: '+ VAT',
        features: [
          '200 prospects',
          'Hyper-personalised email + LinkedIn messaging',
          'Automated follow-ups',
          'AI avatar setup (Anna)',
          'Cancel anytime',
          'Ideal for freelancers, solo founders, boutique agencies',
        ],
      },
      {
        id: 1,
        name: 'Growth Plan',
        price: '£119.99',
        subtitle: '+ VAT',
        features: [
          '500 prospects',
          'Everything in Starter',
          'Priority prospect matching',
          'Personality-based A/B testing (DISC & OCEAN)',
          '1 strategy call/month (optional)',
          'Ideal for consultants, agencies, startups',
        ],
      },
      {
        id: 2,
        name: 'Scale Plan',
        price: '£239.99',
        subtitle: '+ VAT',
        features: [
          '1000 prospects',
          'Everything in Growth',
          'Multi-account LinkedIn/email sending',
          'Custom ICP refinement with Anna',
          'Early access to new features',
          'Ideal for scaling startups, lead-gen agencies, recruitment firms',
        ],
      },
    ];

    return (
      <div className="min-h-screen flex flex-col justify-center items-center bg-gradient-to-b from-gray-50 to-gray-100 px-6 py-12">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h1 className="text-4xl md:text-5xl font-extrabold text-gray-900 mb-3">
            Roboman Pricing Plans
          </h1>
          <p className="text-gray-600 text-lg">
            Choose a plan that fits your growth. Upgrade anytime.
          </p>
        </div>

        <div className="grid gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 max-w-6xl w-full">
          {plans.map(({ id, name, price, subtitle, features }) => (
            <div
              key={id}
              className={`cursor-pointer rounded-2xl shadow-md hover:shadow-2xl transition-all duration-300 p-8 flex flex-col bg-white ${
                selectedPlan === id ? 'border-2 border-[#223F9E] scale-105' : ''
              }`}
              onClick={() => setSelectedPlan(id)}
            >
              <h2 className="text-xl font-semibold text-gray-900 text-center mb-3">
                {name}
              </h2>
              <p className="text-3xl font-bold text-center text-[#223F9E]">
                {price} <span className="text-base text-gray-500">{subtitle}</span>
              </p>

              <ul className="text-gray-700 text-sm space-y-3 mt-6 flex-grow">
                {features.map((feat, idx) => (
                  <li
                    key={idx}
                    className={`${
                      feat.startsWith('Ideal')
                        ? 'font-medium text-green-600'
                        : ''
                    }`}
                  >
                    {feat.startsWith('Ideal') ? '✅ ' : '✔ '} {feat}
                  </li>
                ))}
              </ul>

              <button
                type="button"
                className="mt-8 w-full py-3 bg-[#223F9E] text-white font-semibold rounded-xl hover:bg-blue-700 transition"
                onClick={(e) => {
                  e.stopPropagation(); // prevent triggering card click
                  createPaymentSession(id);
                }}
                aria-label={`Buy the ${name}`}
              >
                Buy The Plan
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

const Footer = () => {
  const showBillPage = localStorage.getItem('showbillpage');

  return (
    <footer className="py-4 bg-white flex justify-end shadow-2xl sticky bottom-0">
      <div className="mb-3 mt-3">
        {showBillPage ? (
          // Show "Back" button if showbillpage is true
          <button
            className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-12 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
            onClick={() => navigate('/dashboard')}
          >
            Back
          </button>
        ) : (
          // If you want to keep the back button regardless, you can repeat:
          <button
            className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-12 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
            onClick={() => navigate('/dashboard')}
          >
            Back
          </button>
        )}
      </div>
    </footer>
  );
};


  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <OnboardingHeaderPart isLogout={false} />
      <main className="flex-grow">
        <PricingPlans loading={loading} stripePrices={stripePrices} />
      </main>
      <Footer />
    </div>
  );
};

export default BillingPage;
