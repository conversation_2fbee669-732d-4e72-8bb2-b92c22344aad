import React, { useState } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import AuthHeader from "./AuthHeader"; // ✅ Full-page header

const ForgotPassword = () => {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  const handleForgotPassword = async (event) => {
    event.preventDefault();

    try {
      const response = await fetch(`${roboman_api}/forgot-password`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
        setError("");
      } else {
        const errorData = await response.json();
        setError(errorData.detail);
        setMessage("");
      }
    } catch (err) {
      console.error("Error:", err);
      setError("An error occurred. Please try again.");
      setMessage("");
    }
  };

  return (
    <div className="h-screen w-screen flex flex-col">
      {/* ✅ Full Page Header */}
      <AuthHeader />

      {/* Page Content */}
      <div className="flex flex-1">
        {/* Left Section */}
        <div className="hidden md:flex w-1/2 bg-gradient-to-br from-blue-700 to-purple-600 text-white flex-col justify-center items-center p-12">
          <h1 className="text-4xl font-bold mb-4">
            Reset <span className="text-yellow-400">Password</span>
          </h1>
          <p className="text-lg max-w-md text-center">
            Enter your registered email address, and we'll send you a reset link.
          </p>
          <img
            src="https://cdn-icons-png.flaticon.com/512/295/295128.png"
            alt="Forgot Password Illustration"
            className="mt-10 w-72"
          />
        </div>

        {/* Right Section (Form) */}
        <div className="flex w-full md:w-1/2 justify-center items-center bg-white shadow-lg">
          <div className="w-full max-w-md p-8">
            <h2 className="text-2xl font-bold mb-2 text-center">Forgot Password</h2>
            <p className="text-gray-500 text-center mb-6">
              We'll send you a reset link
            </p>

            {/* Forgot Password Form */}
            <form className="space-y-4" onSubmit={handleForgotPassword}>
              <input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
                required
              />

              {message && <p className="text-green-500 text-sm">{message}</p>}
              {error && <p className="text-red-500 text-sm">{error}</p>}

              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg shadow-md hover:opacity-90 transition"
              >
                Send Reset Link
              </button>
            </form>

            {/* Back to Login */}
            <p className="text-center text-gray-600 mt-6">
              Remembered your password?{" "}
              <Link
                to="/login"
                className="text-purple-600 font-semibold hover:underline"
              >
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
