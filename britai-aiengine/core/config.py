from functools import lru_cache
import secrets
from dotenv import load_dotenv
from pydantic_settings import BaseSettings
# load_dotenv(".env")

# from api_keys import GEMINI_API_KEY

class Settings(BaseSettings):
    SERVER_PORT: int = 8000
    DOMAIN_NAME:str 
    
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 60 * 24 * 8
    ACCESS_TOKEN_URL: str = "/api/token"
    BACKEND_CORS_ORIGINS: list = ["*"]
    BACKEND_CORS_ALLOW_METHODS: list = ["*"]
    BACKEND_CORS_ALLOW_HEADERS: list = ["*"]
    PROJECT_NAME: str
    
    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int    

    JWT_SECRET: str
    JWT_ALGORITHM: str
    TIMEOUT: int = 99999999  
    
    # AI_API_KEY : dict = {
    #     "google": GEMINI_API_KEY
    # }
    GOOGLE_GENAI_USE_VERTEXAI:  str ="False"

    GOOGLE_API_KEY: str

    AGENT_DEBUG_MODE: bool = True

    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()