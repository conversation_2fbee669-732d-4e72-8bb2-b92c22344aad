from fastapi import HTTPException, status


WRONG_PASSWORD = HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You have entered a wrong password",
                )

USER_NOT_FOUND = HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found in the database",
                )

NO_HISTORY = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f'No history',
        )

BAD_REQUEST = HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Bad request",
                )

NOT_AUTHORIZED = HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Insufficient privileges",
                )
