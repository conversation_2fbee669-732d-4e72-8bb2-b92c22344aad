from typing import Optional, Any
from pydantic import BaseModel
from pydantic import EmailStr
from enum import Enum


class Role(str,Enum):
    USER = 1
    ADMIN = 2
    MANAGER = 3
    SUPERADMIN = 4
    TRIAL = 5

# Shared properties
class RoleBase(BaseModel):
    role_id: Optional[str] = None
    role_name: Optional[str] = None
    role_description: str
    routes: Any = None


# Properties to receive on item update
class RoleUpdate(BaseModel):
    role_id: int

# Properties to receive on item creation
class RoleCreate(RoleUpdate):
    email: EmailStr
    password: str

# class NewRoleIn(BaseModel):
#     role_name: str
#     role_description: str