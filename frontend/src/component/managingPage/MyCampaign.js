import React, { useState, useEffect, useRef } from 'react';
import HeaderMainPage, { newCampaign } from '../../header/HeaderMainPage';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { IoArrowBack } from "react-icons/io5";
import CampaignSummary from "./campaignSummary";
import { HiDotsVertical } from "react-icons/hi";
const StarIcon = ({ filled }) => (
  <svg className={`w-6 h-6 ${filled ? 'text-yellow-400' : 'text-gray-300'}`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
  </svg>
);

// Define column widths
const columnWidths = {
  checkbox: '50px',
  star: '50px',
  campaign: '300px',
  deliveryDate: '150px',
  coreproduct: '150px',
  actions: '100px',
  value: '100px',
};

const MyCampaign = () => {
  const navigate = useNavigate();
  const [campaigns, setCampaigns] = useState([]);
  const [activeTab, setActiveTab] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(null);
  const [anySelected, setAnySelected] = useState(false); // New state to track if any checkbox is selected
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const campaignSelectionRef = useRef(null);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (campaignSelectionRef.current && !campaignSelectionRef.current.contains(event.target)) {
        setOpenDropdown(null);
      }
    };

    const handleScroll = () => {
      setOpenDropdown(null); // Close dropdown when scrolling
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener("wheel", handleScroll);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener("wheel", handleScroll);
    };
  }, []);
  
  const updateColumnWidth = (column, width) => {
    // Function to update column width if needed in the future
    columnWidths[column] = width;
    // Force re-render
    setCampaigns([...campaigns]);
  };

  const fetchCampaignDataFromAPI = async (access_token) => {
    /*
    * Fetches user campaign data from the API.
    * Returns the data if successful, or null if there was an error.
    */
    try {
      const response = await fetch(`${roboman_api}/campaign/personal`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': access_token,
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        return []
      }
      const data = await response.json();
      return data;

    } catch (error) {
      console.error('Error fetching user campaign data:', error);
      return null;
    }
  };

  const formatDate = (dateString) => {
    // Convert the date string to a Date object
    const date = new Date(dateString);
    
    // Format time
    const time = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false // Use 24-hour format
    });
    
    // Format date
    const formattedDate = date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
    
    // Combine time and date
    return `${time} ${formattedDate}`;
  };

  const fetchCampaignData = async () => {
    /*
    * Fetches campaign data from the API and updates the state.
    * If the API call fails, it will return an empty array.
    * You can also implement a fallback to localStorage if needed.
    */

    // First, try to load data from localStorage
    const access_token = localStorage.getItem('access_token')
    const userCampaign = await fetchCampaignDataFromAPI(access_token);
    if (userCampaign) {
      // If API fetch is successful, use the data

      const staticCampaigns = userCampaign.map((item, index) => ({
        id: item.campaign_id,
        name: item.campaign_name,
        deliveryDate: formatDate(item.created_at),
        isStarred: false,
        isSelected: false,
        campaigns_id: item.campaign_id,
        campaigns_info: item.campaign_info,
        email_format_info: item.email_format,
        must_have_info: item.campaign_contact?.campaign_email || "",
        stats: item.stats

      }));
      setCampaigns(staticCampaigns);
    }
  };

  useEffect(() => {
    fetchCampaignData();
  }, []);

  const handleTabClick = (tab) => {
    // Update the active tab state
    setActiveTab(tab);
  };

  const handleStarClick = (id) => {
    // Toggle the isStarred state of the clicked campaign
    setCampaigns(campaigns.map(campaign => 
      campaign.id === id ? { ...campaign, isStarred: !campaign.isStarred } : campaign
    ));
  };

  const handleSelectAll = () => {
    // Toggle the selectAll state and update all campaigns accordingly
    setSelectAll(!selectAll);
    setCampaigns(campaigns.map(campaign => ({ ...campaign, isSelected: !selectAll })));
    setAnySelected(!selectAll); // Update anySelected state
  };

  const handleSearch = (e) => {
    // Update the search term state
    setSearchTerm(e.target.value);
  };

  const handleDelete = async (id) => {
    /*
    * Handle the delete action for a specific campaign.
    * Displays a confirmation dialog before proceeding with the deletion.
    * If the deletion is successful, updates the UI.
    * If the deletion fails, shows an error message.
    */
    try {
      const response = await fetch(`${roboman_api}/campaign/${id}/delete`, {
        method: 'DELETE',
        headers: {
          'accept': 'application/json',
          'Authorization': localStorage.getItem('access_token'), 
        },
      });

      if (!response.ok) {
        console.error('Failed to delete campaign', response);
        
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: 'There was a problem deleting the campaign. Please try again later.',
          confirmButtonText: 'OK',
        });

        return;
      }

      // If the delete is successful, update the UI
      setCampaigns(campaigns.filter(campaign => campaign.id !== id));
      
      // Show success alert using SweetAlert
      Swal.fire({
        icon: 'success',
        title: 'Deleted!',
        text: 'Your campaign has been deleted successfully.',
        confirmButtonText: 'OK',
      });

    } catch (error) {
      console.error('Error deleting campaign:', error);
      
      // Show error alert using SweetAlert
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'There was a problem deleting the campaign. Please try again later.',
        confirmButtonText: 'OK',
      });
    }

    setOpenDropdown(null);
  };

  const handleDetails = (campaign) => {
    // Save the selected campaign name in local storage

    localStorage.setItem('campaign_name', campaign.name);
    localStorage.setItem('campaign_id', campaign.id);  // Save the campaign id
    // localStorage.setItem('core_service', campaign.campaigns_info.core_service)
    // localStorage.setItem('key_benefits', campaign.campaigns_info.key_benefits)
    // localStorage.setItem('problem_solved', campaign.campaigns_info.problem_solved)
    // localStorage.setItem('campaign_info',JSON.stringify(campaign.campaigns_info))
    // localStorage.setItem('campaignOutputFormat', campaign.email_format_info)
    // const must_have_info = `My contact email is ${campaign.must_have_info}`
    // localStorage.setItem('campaignMustHaveInfo', must_have_info)
    // Navigate to the details page
    navigate(`/email-status`);
  };

  const handleCheckboxChange = (id) => {
    // Toggle the isSelected state of the clicked campaign
    setCampaigns(campaigns.map(campaign => 
      campaign.id === id ? { ...campaign, isSelected: !campaign.isSelected } : campaign
    ));
    const newAnySelected = campaigns.some(campaign => campaign.id === id ? !campaign.isSelected : campaign.isSelected);
    setAnySelected(newAnySelected);
  };

  const handleDeleteSelected = async () => {
    /*
    * Handle the delete action for selected campaigns.
    * Displays a confirmation dialog before proceeding with the deletion.
    * If the deletion is successful, updates the UI.
    * If the deletion fails, shows an error message.
    */
    const campaign_delete_list_id = campaigns
        .filter((campaign) => campaign.isSelected)
        .map((campaign) => campaign.id);

    if (campaign_delete_list_id.length === 0) {
        Swal.fire({
            icon: 'warning',
            title: 'No Campaigns Selected',
            text: 'Please select at least one campaign to delete.',
        });
        return;
    }

    try {
        Swal.fire({
            title: 'Deleting Campaigns...',
            text: 'Please wait while the selected campaigns are being deleted.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading(); // Show loading spinner
            },
        });

        for (let i = 0; i < campaign_delete_list_id.length; i++) {
            try {
                const response = await fetch(
                    `${roboman_api}/campaign/${campaign_delete_list_id[i]}/delete`,
                    {
                        method: 'DELETE',
                        headers: {
                            accept: 'application/json',
                            Authorization: localStorage.getItem('access_token'),
                        },
                    }
                );

                if (!response.ok) {
                    console.error('Failed to delete campaign', response);
                }
            } catch (error) {
                console.error('Error deleting campaign:', error);
            }

            // Add a delay of 1 second between each deletion
            await new Promise((resolve) => setTimeout(resolve, 1000));
        }
        // Close the loading indicator and show success
        Swal.fire({
            icon: 'success',
            title: 'Deleted!',
            text: 'Selected campaigns have been deleted successfully.',
        });
    } catch (error) {
        console.error('Error during deletion process:', error);

        // Show error alert
        Swal.fire({
            icon: 'error',
            title: 'Error!',
            text: 'There was a problem deleting the campaigns. Please try again later.',
        });
    }

    // Fetch updated campaign data after deletion
    await fetchCampaignData();
};

  const handleBack = () => {
    navigate('/dashboard');
  };

  const filteredCampaigns = campaigns.filter(campaign => {
    if (activeTab === 'All') return true;
    if (activeTab === 'Important') return campaign.isStarred;
    return true; // For other tabs, show all for now
  }).filter(campaign => 
      (campaign.name && campaign.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (campaign.library && campaign.library.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  const handleDropdownToggle = (campaignId, event) => {
    if (openDropdown === campaignId) {
      setOpenDropdown(null);
    } else {
      const rect = event.currentTarget.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX - 30,
      });
      setOpenDropdown(campaignId);
    }
  };


return (
  <div className="bg-gray-50 min-h-screen">
    <HeaderMainPage />

    <div className="max-w-7xl mx-auto px-6 lg:px-8 mt-20">
      {/* Header Section */}
      <div className="bg-white shadow-md rounded-2xl p-6 flex justify-between items-center mb-8 border border-gray-100">
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 bg-gray-100 hover:bg-gray-200 rounded-full transition"
            aria-label="Back"
          >
            <IoArrowBack size={20} className="text-gray-700" />
          </button>
          <h1 className="text-2xl font-extrabold text-gray-800 tracking-tight">
            Campaigns
          </h1>
        </div>

        <button
          onClick={() => newCampaign(navigate)}
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-2.5 rounded-full font-semibold shadow-lg transition"
        >
          + New Campaign
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6 space-x-6">
        {['All', 'Important'].map(tab => (
          <button
            key={tab}
            onClick={() => handleTabClick(tab)}
            className={`pb-3 text-sm font-medium transition ${
              activeTab === tab
                ? 'border-b-2 border-blue-600 text-blue-600'
                : 'text-gray-500 hover:text-gray-800'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Search + Actions */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <input
            type="checkbox"
            checked={selectAll}
            onChange={handleSelectAll}
            className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
          />

          <div className="relative">
            <input
              type="text"
              placeholder="Search campaigns..."
              value={searchTerm}
              onChange={handleSearch}
              className="w-72 pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 
                     3.476l4.817 4.817a1 1 0 01-1.414 
                     1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        <button
          className={`bg-red-600 text-white px-5 py-2 rounded-xl shadow font-medium hover:bg-red-700 transition ${
            anySelected ? '' : 'opacity-50 cursor-not-allowed'
          }`}
          onClick={handleDeleteSelected}
          disabled={!anySelected}
        >
          Delete
        </button>
      </div>

      {/* Campaigns Table */}
      <div className="bg-white shadow-lg rounded-2xl overflow-hidden border border-gray-100">
        <div className="max-h-[70vh] overflow-y-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50 sticky top-0 z-10 border-b border-gray-200">
              <tr>
                <th className="w-8"></th>
                <th className="w-8"></th>
                <th className="px-4 py-3 text-left text-gray-600 font-semibold">
                  Campaign
                </th>
                <th className="px-4 py-3 text-left text-gray-600 font-semibold">
                  Created
                </th>
                <th className="px-4 py-3 text-left text-gray-600 font-semibold">
                  Core Product
                </th>
                <th className="px-4 py-3 text-center text-gray-600 font-semibold">
                  Prospects
                </th>
                <th className="px-4 py-3 text-center text-gray-600 font-semibold">
                  Sent
                </th>
                <th className="px-4 py-3 text-center text-gray-600 font-semibold">
                  Open
                </th>
                <th className="px-4 py-3 text-center text-gray-600 font-semibold">
                  Reply
                </th>
                <th className="px-4 py-3 text-center text-gray-600 font-semibold">
                  Interested
                </th>
                <th className="w-8"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {filteredCampaigns?.length === 0 ? (
                <tr>
                  <td
                    colSpan="11"
                    className="px-6 py-10 text-center text-gray-500"
                  >
                    No campaigns found.
                  </td>
                </tr>
              ) : (
                filteredCampaigns.map(campaign => (
                  <tr
                    key={campaign.id}
                    className="hover:bg-gray-50 transition"
                  >
                    <td className="px-2">
                      <input
                        type="checkbox"
                        checked={campaign.isSelected}
                        onChange={() => handleCheckboxChange(campaign.id)}
                        className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-2">
                      <button
                        onClick={() => handleStarClick(campaign.id)}
                        className="focus:outline-none"
                      >
                        <StarIcon filled={campaign.isStarred} />
                      </button>
                    </td>
                    <td className="px-4 py-3">
                      <button
                        onClick={() => handleDetails(campaign)}
                        className="text-gray-800 font-medium hover:text-blue-600"
                      >
                        {campaign.name}
                      </button>
                      <div className="text-xs text-gray-500">
                        {campaign.library}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-gray-500">
                      {campaign.deliveryDate}
                    </td>
                    <td
                      className="px-4 py-3 text-gray-500 truncate max-w-xs"
                      title={campaign.campaigns_info.core_service}
                    >
                      {campaign.campaigns_info.core_service}
                    </td>
                    <td className="px-4 py-3 text-center text-gray-600">
                      {campaign.stats.prospects_count}
                    </td>
                    <td className="px-4 py-3 text-center text-gray-600">
                      {campaign.stats.sent.count}
                    </td>
                    <td className="px-4 py-3 text-center text-gray-600">
                      {campaign.stats.opened.count}
                    </td>
                    <td className="px-4 py-3 text-center text-gray-600">
                      {campaign.stats.replied.count}
                    </td>
                    <td className="px-4 py-3 text-center text-gray-600">
                      {campaign.stats.interested.count}
                    </td>
                    <td className="px-2 text-center">
                      <button
                        onClick={e =>
                          handleDropdownToggle(campaign.id, e)
                        }
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <HiDotsVertical className="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Dropdown menu */}
      {openDropdown && (
        <div
          className="absolute z-50 bg-white shadow-xl rounded-lg border border-gray-200 mt-2 w-36"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left,
          }}
        >
          <button
            onMouseDown={() => setSelectedCampaign(openDropdown)}
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
          >
            Summary
          </button>
          <button
            onMouseDown={() =>
              handleDetails(
                filteredCampaigns.find(c => c.id === openDropdown)
              )
            }
            className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100"
          >
            Details
          </button>
          <button
            onMouseDown={() => handleDelete(openDropdown)}
            className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50"
          >
            Delete
          </button>
        </div>
      )}

      {/* Campaign Summary Modal */}
      {selectedCampaign && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <CampaignSummary
            campaignId={selectedCampaign}
            onClose={() => setSelectedCampaign(null)}
          />
        </div>
      )}
    </div>
  </div>
);

};

export default MyCampaign;
