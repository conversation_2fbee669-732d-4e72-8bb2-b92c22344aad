from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class StripeProductCreate(BaseModel):
    product_name: str
    product_description: Optional[str] = None
    unit_label: Optional[str]
    product_details: Dict[str, Any] = {"type": "credits", "amount": 200}

class StripeProductUpdate(BaseModel):
    product_name: Optional[str] = None
    product_description: Optional[str] = None
    class Config:
        orm_mode = True


class StripePriceCreate(BaseModel):
    product_id: str
    price_name: str
    price_description: Optional[str] = None
    price_amount: float

class StripePriceUpdate(BaseModel):
    price_amount: Optional[float] = None
    