import React from 'react';
import discInfo from '../../assets/disc_info.json';
import oceanInfo from '../../assets/ocean_info.json';

export function userProfileDisplay(userData, closePopup) {

    const UserBasicInfo = () => {
        // This part is for the user basic info section
        // Check if userData is available before rendering
        if (!userData) return null;

        return (
        <div className="w-full max-w-[1300px] h-auto flex flex-col md:flex-row p-6 bg-white items-center space-x-4 space-y-4 md:space-y-0 rounded-2xl shadow-md">
            {/* Avatar Logo Placeholder */}
            <div className="w-20 h-20 text-center bg-gray-300 flex items-center justify-center rounded-full ml-5 mr-5">
                {userData.user_profile_image ? (
                    <img src={userData.user_profile_image} alt="User Profile" className="w-full h-full object-cover rounded-full" />
                ) : (
                    "Avatar Logo"
                )}
            </div>

            <div className="flex flex-col space-y-2">
            <div className="text-lg md:text-xl font-bold">{userData.display_name}</div>
            <div className="flex flex-col md:flex-row items-start md:items-center space-y-2 md:space-y-0 md:space-x-4">
                {/* Gender */}
                <div className="flex items-center space-x-1">
                    {/* Note: Import Gender Logo Here */}
                    <div className="w-5 h-5 bg-gray-300 rounded-xl"></div>
                    <div className="px-2">{userData.work_history}</div>
                </div>
                {/* Location */}
                <div className="md:p-3 flex items-center space-x-1">
                    {/* Note: Import Location Logo Here */}
                    <div className="w-5 h-5 bg-gray-300 rounded-xl"></div>
                    <div className="px-2">{userData.location}</div>
                </div>
            </div>
            <div className="flex flex-wrap space-x-2">
                {userData.skills.map((skill, index) => (
                <div key={index} className="bg-blue-500 text-white px-2 py-1 rounded mb-2">
                    {skill}
                </div>
                ))}
            </div>
            </div>
        </div>
        );
    };

    const ContentBox = ({ title, content }) => {
        // This part is for the content box in the disc and ocean profile section
        return (
          <div className="p-4 w-[250px] h-[280px] min-w-[250px] min-h-[280px] bg-gray-100 rounded-lg">
            <div className="text-blue-800 text-sm font-bold mb-2">{title}</div>
            <div className="text-sm mb-2">{content}</div>
          </div>
        );
      };

    const DiscProfile = () => {
        // This part is for the disc profile section
        // Check if userData is available before rendering
        if (!userData) return null;

        const getDiscDescription = (label) => {
            const trait = discInfo.disc.find(t => t.abbreviation.toLowerCase() === label.toLowerCase());
            return trait ? trait.description : "Description not available";
        };

        return (
        <div className="p-3 w-full bg-white flex flex-col space-y-4 rounded-xl shadow-md items-center ">
            {/* Title */}
            <div className="text-center text-lg font-bold">Disc Profile</div>
            {/* Disc Label */}
            <div className="flex flex-wrap justify-center items-center space-x-2">
                {userData.disc_label.map((label, index) => (
                    <div key={index} className="bg-yellow-400 text-white py-1 px-4 rounded mb-2">
                        {label}
                    </div>
                ))}
            </div>
            {userData.disc_label.map((label, index) => (
                <ContentBox key={index} title={`${label} - ${discInfo.disc.find(t => t.abbreviation.toLowerCase() === label.toLowerCase())?.trait}`} content={getDiscDescription(label)} />
            ))}
        </div>
        );
    };

    const OceanProfile = () => {
        // This part is for the ocean profile section
        // Check if userData is available before rendering
        if (!userData) return null;

        const getOceanDescription = (label) => {
            const trait = oceanInfo.ocean.find(t => t.abbreviation.toLowerCase() === label.toLowerCase());
            return trait ? trait.description : "Description not available";
        };

        return (
        <div className="p-3 bg-white flex flex-col space-y-4 rounded-xl h-full items-center shadow-md ">
            {/* Title */}
            <div className="text-center text-lg font-bold">Ocean Profile</div>
            {/* OCEAN Label */}
            <div className="flex flex-wrap justify-center items-center space-x-2">
                {userData.ocean_label.map((label, index) => (
                    <div key={index} className="bg-yellow-400 text-white py-1 px-4 rounded">
                        {label}
                    </div>
                ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 md:gap-2 lg:gap-4 p-2">
                {userData.ocean_label.map((label, index) => (
                    <ContentBox key={index} title={`${label} - ${oceanInfo.ocean.find(t => t.abbreviation.toLowerCase() === label.toLowerCase())?.trait}`} content={getOceanDescription(label)} />
                ))}
            </div>
        </div>
        );
    };

    const ProfilesPart = () => {
        // This part is for the profiles section
        return (
          <div className="w-full max-w-[1300px] flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="w-full md:w-1/3">
              <DiscProfile />
            </div>
            <div className="w-full md:w-2/3">
              <OceanProfile />
            </div>
          </div>
        );
    };

    const Footer = () => {
        // This part is for the footer section
        return (
            <div className="mb-3 mt-3 flex justify-center">
                <button 
                className="bg-[#223F9E] text-white font-semibold py-2 px-10 rounded-full "
                onClick={closePopup}
                >
                Close
                </button>
            </div>
        )
    }

    return (
    <div>
    {/* Render user data or other components here */}
        <div className="flex flex-col h-[90vh] w-[90vw] bg-gray-100">
            {/* This part is for the main/body section */}
            <div className="flex-grow flex flex-col items-center space-y-8 py-4 px-4 md:px-8">
                {/* First Part */}
                <div>
                    <h2 className="text-2xl font-bold text-center">
                    Company Representative Profile
                    </h2>
                </div>
                {/* Second Part */}
                <UserBasicInfo />
                {/* Third Part */}
                <ProfilesPart />
            </div>
            {/* Footer is placed here to ensure it stays at the bottom */}
            <Footer />
        </div>
    </div>      

    )
};