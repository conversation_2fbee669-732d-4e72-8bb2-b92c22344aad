from io import Bytes<PERSON>
from fastapi import UploadFile
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>

def merge_multiple_bytesios(streams: list[BytesIO]) -> BytesIO:
    merged = BytesIO()
    for b in streams:
        b.seek(0)
        merged.write(b.read())
    merged.seek(0)
    return merged

async def merge_upload_files(uploaded_files: list[UploadFile]) -> BytesIO:

    file_objs = []

    for file in uploaded_files:
        if not file.filename.endswith('.pdf'):
            raise HTTPException(status_code=400, detail=f"File '{file.filename}' must be a PDF")

        contents = await file.read()
        file_obj = BytesIO(contents)
        file_objs.append(file_obj)

    file_obj = merge_multiple_bytesios(file_objs)
    return file_obj