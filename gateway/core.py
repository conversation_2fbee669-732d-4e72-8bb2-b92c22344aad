from network import make_request, streaming, make_file_request, file_streaming

import aiohttp.payload
import functools
from importlib import import_module
from fastapi import Request, Response, HTTPException, status, UploadFile
from typing import List
from fastapi.responses import StreamingResponse
from exceptions import (AuthTokenMissing, AuthTokenExpired, AuthTokenCorrupted)


def route(
        request_method, path: str, status_code: int,
        payload_key: str, service_url: str,
        authentication_required: bool = False,
        post_processing_func: str = None,
        authentication_token_decoder: str = 'auth.decode_jwt',
        service_authorization_checker: str = 'auth.is_admin_user',
        service_header_generator: str = 'auth.generate_request_header',
        response_model: str = None,
        response_list: bool = False,
        response_stream: bool = False,
        tags: List[str] = ['default']
):
    """
    A decorator that creates a FastAPI route to act as a gateway, forwarding
    requests to a downstream service. It handles authentication, authorization,
    payload processing, and response handling.

    Args:
        request_method: The FastAPI request method (e.g., app.post).
        path (str): The API endpoint path.
        status_code (int): The expected success status code.
        payload_key (str): The key in the decorated function's **kwargs that holds the request payload.
                           Can be a single key or comma-separated for multiple.
        service_url (str): The base URL of the downstream service.
        authentication_required (bool): If True, authentication and authorization checks are performed.
        post_processing_func (str): Path to a function for post-processing the response from the service (e.g., 'auth.sign_jwt').
        authentication_token_decoder (str): Path to the function used to decode the JWT.
        service_authorization_checker (str): Path to the function used to check user permissions.
        service_header_generator (str): Path to the function that generates headers for the downstream request.
        response_model (str): Path to the Pydantic response model for documentation.
        response_list (bool): If True, the response is a list of `response_model`.
        response_stream (bool): If True, the response from the service will be streamed.
        tags (List[str]): A list of tags for grouping endpoints in the API docs.
    """

    if response_model:
        response_model = import_function(response_model)
        if response_list:
            response_model = List[response_model]

    # Register the route with FastAPI.
    app_any = request_method(
        path, status_code=status_code,
        response_model=response_model,
        tags = tags
    )

    def wrapper(f):
        @app_any
        @functools.wraps(f)
        async def inner(request: Request, response: Response, **kwargs):
            service_headers = {}

            if authentication_required:
                # --- Authentication ---
                authorization = request.headers.get('Authorization')
                authorization = authorization.split(" ")[-1]
                token_decoder = import_function(authentication_token_decoder)
                exc = None
                try:
                    token_payload = token_decoder(authorization)
                    print(token_payload)
                except (AuthTokenMissing,
                        AuthTokenExpired,
                        AuthTokenCorrupted) as e:
                    exc = str(e)
                except Exception as e:
                    # in case a new decoder is used by dependency injection and
                    # there might be an unexpected error
                    exc = str(e)
                finally:
                    if exc:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail=exc,
                            headers={'WWW-Authenticate': 'Bearer'},
                        )

                # --- Authorization ---
                if service_authorization_checker:
                    authorization_checker = import_function(
                        service_authorization_checker
                    )
                    try:
                        is_user_eligible = authorization_checker(token_payload)
                    except:
                        is_user_eligible = False
                    if not is_user_eligible:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail='You are not allowed to access this scope.',
                            headers={'WWW-Authenticate': 'Bearer'},
                        )

                # --- Service Headers ---
                if service_header_generator:
                    header_generator = import_function(
                        service_header_generator
                    )
                    service_headers = header_generator(token_payload)

            scope = request.scope

            method = scope['method'].lower()
            path = scope['path']
            
            # --- Payload Construction ---
            payload = {}
            if payload_key:
                if not "," in payload_key:
                    payload_obj = kwargs.get(payload_key)
                    payload = payload_obj.dict() if payload_obj else {}
                else:
                    try:
                        payload = {plk: kwargs.get(plk) for plk in payload_key.split(",")}
                    except:
                        pass
            
            # Construct the full URL for the downstream service.
            url = f'{service_url}{path}'
            print(url)
            if not response_stream:
                # --- Standard Request/Response ---
                try:
                    resp_data, status_code_from_service = await make_request(
                        url=url,
                        method=method,
                        data=payload,
                        headers=service_headers,
                    )
                except aiohttp.client_exceptions.ClientConnectorError:
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail='Service is unavailable.',
                        headers={'WWW-Authenticate': 'Bearer'},
                    )

                response.status_code = status_code_from_service

                if all([
                    status_code_from_service == status_code,
                    post_processing_func
                ]):
                    post_processing_f = import_function(post_processing_func)
                    resp_data = post_processing_f(resp_data)

                return resp_data

            elif response_stream:
                # --- Streaming Response ---
                return StreamingResponse(
                    streaming(
                        url=url,
                        method=method,
                        data=payload,
                        headers=service_headers,
                    ),
                    media_type='text/event-stream',
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail='Service error.',
                    headers={'WWW-Authenticate': 'Bearer'},
                )
    return wrapper



def import_function(method_path: str):
    """
    Dynamically imports a function from a string path.

    Args:
        method_path (str): The full dot-separated path to the function (e.g., 'module.submodule.function_name').

    Returns:
        Callable: The imported function, or a no-op lambda if not found.
    """
    module, method = method_path.rsplit('.', 1)
    mod = import_module(module)
    # Return the function, or a lambda that does nothing if it's not found.
    return getattr(mod, method, lambda *args, **kwargs: None)

def file_route(
        request_method, path: str, status_code: int,
        payload_key: str, service_url: str,
        authentication_required: bool = False,
        post_processing_func: str = None,
        authentication_token_decoder: str = 'auth.decode_jwt',
        service_authorization_checker: str = 'auth.is_admin_user',
        service_header_generator: str = 'auth.generate_request_header',
        response_model: str = None,
        response_list: bool = False,
        response_stream: bool = False,
        tags: List[str] = ['default']
):
    """
    A decorator that creates a FastAPI route for forwarding file uploads
    to a downstream service. It handles multipart/form-data.

    Args:
        request_method: The FastAPI request method (e.g., app.post).
        path (str): The API endpoint path.
        status_code (int): The expected success status code.
        payload_key (str): The key in the decorated function's **kwargs that holds the `UploadFile` object(s).
        service_url (str): The base URL of the downstream service.
        authentication_required (bool): If True, authentication and authorization checks are performed.
        post_processing_func (str): Path to a function for post-processing the response.
        authentication_token_decoder (str): Path to the function used to decode the JWT.
        service_authorization_checker (str): Path to the function used to check user permissions.
        service_header_generator (str): Path to the function that generates headers for the downstream request.
        response_model (str): Path to the Pydantic response model for documentation.
        response_list (bool): If True, the response is a list of `response_model`.
        response_stream (bool): If True, the response from the service will be streamed.
        tags (List[str]): A list of tags for grouping endpoints in the API docs.
    """

    if response_model:
        response_model = import_function(response_model)
        if response_list:
            response_model = List[response_model]

    app_any = request_method(
        path, status_code=status_code,
        response_model=response_model,
        tags=tags
    )

    def wrapper(f):
        @app_any
        @functools.wraps(f)
        async def inner(request: Request, response: Response, **kwargs):
            service_headers = {}

            if authentication_required:
                # --- Authentication ---
                authorization = request.headers.get('Authorization')
                authorization = authorization.split(" ")[-1]
                token_decoder = import_function(authentication_token_decoder)
                exc = None
                try:
                    token_payload = token_decoder(authorization)
                    print(token_payload)
                except (AuthTokenMissing,
                        AuthTokenExpired,
                        AuthTokenCorrupted) as e:
                    exc = str(e)
                except Exception as e:
                    # in case a new decoder is used by dependency injection and
                    # there might be an unexpected error
                    exc = str(e)
                finally:
                    if exc:
                        raise HTTPException(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            detail=exc,
                            headers={'WWW-Authenticate': 'Bearer'},
                        )

                # --- Authorization ---
                if service_authorization_checker:
                    authorization_checker = import_function(
                        service_authorization_checker
                    )
                    try:
                        is_user_eligible = authorization_checker(token_payload)
                    except:
                        is_user_eligible = False
                    if not is_user_eligible:
                        raise HTTPException(
                            status_code=status.HTTP_403_FORBIDDEN,
                            detail='You are not allowed to access this scope.',
                            headers={'WWW-Authenticate': 'Bearer'},
                        )

                # --- Service Headers ---
                if service_header_generator:
                    header_generator = import_function(
                        service_header_generator
                    )
                    service_headers = header_generator(token_payload)

            scope = request.scope

            method = scope['method'].lower()
            path = scope['path']

            # --- Payload Construction for Files ---
            payload = {}
            if payload_key:
                payload_keys = payload_key.split(',')
                for key in payload_keys:
                    payload_obj = kwargs.get(key)
                    if payload_obj and isinstance(payload_obj, UploadFile):
                        payload[key] = {"filename": payload_obj.filename, "content": await payload_obj.read()}
                    else:
                        payload[key] = payload_obj

            # Construct the full URL for the downstream service.
            url = f'{service_url}{path}'
            print(url)
            if not response_stream:
                try:
                    # --- Standard File Request/Response ---
                    resp_data, status_code_from_service = await make_file_request(
                        url=url,
                        method=method,
                        file_request=payload,
                        headers=service_headers,
                    )
                except aiohttp.client_exceptions.ClientConnectorError:
                    raise HTTPException(
                        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                        detail='Service is unavailable.',
                        headers={'WWW-Authenticate': 'Bearer'},
                    )

                response.status_code = status_code_from_service

                if all([
                    status_code_from_service == status_code,
                    post_processing_func
                ]):
                    # Apply post-processing to the response data if specified.
                    post_processing_f = import_function(post_processing_func)
                    resp_data = post_processing_f(resp_data)

                return resp_data

            elif response_stream:
                # --- Streaming File Response ---
                return StreamingResponse(
                    file_streaming(
                        url=url,
                        method=method,
                        uploaded_files=payload,
                        headers=service_headers,
                    ),
                    media_type='text/event-stream',
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail='Service error.',
                    headers={'WWW-Authenticate': 'Bearer'},
                )
    return wrapper
