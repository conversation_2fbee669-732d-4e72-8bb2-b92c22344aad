import asyncio
import pandas as pd
import numpy as np
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from pathlib import Path
from core.config import settings
from loguru import logger
import json
from io import String<PERSON>
from message_bus.rpc_client import RPC<PERSON>lient
# Connect to Elasticsearch

async def ingest_data(message):
    """
    Asynchronous task that processes messages containing company data and indexes them into Elasticsearch.
    
    Args:
        message: Message object containing JSON data with company information to be indexed
    """
    await asyncio.sleep(1)

    try:
        #local development
        # es = Elasticsearch(f'http://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), request_timeout=30, retry_on_timeout=True, max_retries=5)
        #uat
        es = Elasticsearch(f'https://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), verify_certs=False, request_timeout=30, retry_on_timeout=True, max_retries=5)
        try:        
            # Parse message body and extract dataframe
            json_string = message.body.decode()
            message_body = json.loads(json_string)        

            df_json_str = message_body["df_json_str"]
            embedding_dim = 384
            df = pd.read_json(StringIO(df_json_str))

            # Create mapping properties based on dataframe columns
            properties = {}
            for column in df.columns:
                if column != "Company Linkedin Url":
                    properties[column] = {"type": "text"}
                else:
                    properties[column] = {"type": "keyword"}  
            
            # Define index settings and mappings
            index_settings = {
                "settings": {
                    "number_of_shards": 4,
                    "number_of_replicas": 0
                },
                "mappings": {
                    "properties": properties
                }
            }        
            # Create index if it doesn't exist
            if not es.indices.exists(index=settings.ES_INDEX_NAME):
                es.indices.create(index=settings.ES_INDEX_NAME, body=index_settings)

            df = df.fillna('')

            unique_field = "Company Linkedin Url"

            # Prepare the data for bulk indexing
            def doc_generator(df):
                """Generate documents for bulk indexing using LinkedIn URL as unique ID"""
                for index, document in df.iterrows():
                    unique_field_value = document.get(unique_field)
                    yield {
                        "_index": settings.ES_INDEX_NAME,
                         "_id": unique_field_value,
                        "_source": document.to_dict(),
                    }

                # Read CSV using pandas
                
            df_filled = df.fillna('')
            # Perform bulk indexing operation
            success_count, failed_count = bulk(es, doc_generator(df_filled), 
                                               raise_on_error=False, 
                                               stats_only=True, 
                                               chunk_size=100)
            total = success_count + failed_count
            logger.info(f"[CONSUMER] indexed {success_count}/{total} documents into '{settings.ES_INDEX_NAME}'")

            
        except Exception as e:
            raise e
        finally:
            es.close()

    except Exception as e:
        logger.error("[CONSUMER] failed to process ingest_data task: " + str(e))
