from core.config import settings

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_scoped_session
from typing import AsyncIterator
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from asyncio import current_task

# Database connection strings for both sync and async database access
POSTGRES_DATABASEURL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"
ASYNC_POSTGRES_DATABASEURL = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"

# if not database_exists(POSTGRES_DATABASEURL):
#     create_database(POSTGRES_DATABASEURL)

# Create async SQLAlchemy engine with PostgreSQL + asyncpg driver
async_engine = create_async_engine(
    ASYNC_POSTGRES_DATABASEURL,
    echo=False,
    future=True
)

# Create scoped session factory tied to the current asyncio task
SessionFactory = async_scoped_session(
    sessionmaker(async_engine, class_=AsyncSession, expire_on_commit=False),
    scopefunc=current_task,
)

# Base class for all ORM models
Base = declarative_base()


async def get_session() -> AsyncIterator[AsyncSession]:
    """
    Async context manager that provides a database session.
    
    Yields:
        AsyncSession: SQLAlchemy async session for database operations
        
    Handles session lifecycle including commit/rollback and cleanup.
    """
    async with SessionFactory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()