import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { FaCoins } from "react-icons/fa";
import whiteLogo from "../assets/img/roboman-logo.png";
import profileImage from "../assets/profile.png";
import Swal from "sweetalert2";

// 🔹 Utility: Keep only some keys
export const newCampaign = (navigate) => {
  const keysToKeep = [
    "access_token", "isLoggedIn", "havedata",
    "nickname", "linkedinUrl", "email", "userType",
    "lnk_act", "page3_audio_data"
  ];

  Object.keys(localStorage).forEach((key) => {
    if (!keysToKeep.includes(key)) localStorage.removeItem(key);
  });

  localStorage.removeItem("conversation_id");
  const haveData = JSON.parse(localStorage.getItem("havedata"));
  const haveLinkedIN = JSON.parse(localStorage.getItem("lnk_act"));

  if (haveData || haveLinkedIN) {
    navigate("/campinitselect");
  } else {
    navigate("/onboarding-userinfo");
  }
};

const HeaderMainPage = () => {
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  // 🔹 State
  const [showCampaignMenu, setShowCampaignMenu] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showCreditDetails, setShowCreditDetails] = useState(false);
  const [userCredit, setUserCredit] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);

  const campaignMenuRef = useRef(null);
  const profileMenuRef = useRef(null);

  // 🔹 Logout
  const handleLogout = () => {
    localStorage.clear();
    navigate("/login");
  };

  // 🔹 Credits
  const fetchUserCredits = async () => {
    try {
      const res = await fetch(`${roboman_api}/users/credits`, {
        headers: { Authorization: localStorage.getItem("access_token") },
      });
      const data = await res.json();
      if (res.ok) setUserCredit(data);
    } catch (err) {
      console.error("Credit fetch error:", err);
    }
  };

  // 🔹 Format Date
  const formatDate = (dateString) =>
    new Date(dateString).toLocaleDateString("en-GB", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });

  // 🔹 Effects
  useEffect(() => {
    fetchUserCredits();
    const onScroll = () => setIsScrolled(window.scrollY > 20);
    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (campaignMenuRef.current && !campaignMenuRef.current.contains(e.target)) {
        setShowCampaignMenu(false);
      }
      if (profileMenuRef.current && !profileMenuRef.current.contains(e.target)) {
        setShowProfileMenu(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

    return (
    <header
      className={`w-full h-20 flex justify-between items-center px-6 md:px-24
        sticky top-0 z-50 transition-all duration-400
        border-b border-blue-200/40
        shadow-sm bg-gradient-to-r from-[#192038]/90 via-[#121829]/95 to-[#162347]/90
        ${isScrolled ? "backdrop-blur-2xl border-blue-500/50" : "backdrop-blur-lg"}
      `}
      style={{ letterSpacing: 0.2, fontFamily: "Inter, Segoe UI, sans-serif", fontWeight: 500 }}
    >
      {/* 🔹 Logo */}
      <img
        src={whiteLogo}
        alt="Roboman Logo"
        className="w-[110px] md:w-[150px] h-[44px] object-contain drop-shadow-xl transition-transform duration-300 hover:scale-105"
        style={{ filter: "drop-shadow(0 1px 10px #2557fa33)" }}
      />

      {/* 🔹 Desktop Nav */}
      <nav className="hidden md:flex flex-1 justify-center items-center gap-12 text-base font-medium text-neutral-100">
        <button
          onClick={() => navigate("/dashboard")}
          className="transition text-gray-300 hover:text-blue-500 px-2 py-1"
        >
          Dashboard
        </button>

        <div className="relative" ref={campaignMenuRef}>
          <button
            onClick={() => setShowCampaignMenu(!showCampaignMenu)}
            className="transition hover:text-blue-500 px-2 py-1 flex items-center gap-2"
          >
            <span>Campaign</span>
            <span className="text-sm opacity-75">▼</span>
          </button>
          {showCampaignMenu && (
            <div className="absolute top-full left-0 mt-3 bg-white border border-blue-100 rounded-xl shadow-xl z-40 w-52 py-2">
              <button
                onClick={() => newCampaign(navigate)}
                className="w-full px-4 py-2 text-left text-blue-900 hover:bg-blue-50 transition rounded"
              >
                New Campaign
              </button>
              <button
                onClick={() => {
                  localStorage.removeItem("campaign_id");
                  navigate("/my-campaign");
                }}
                className="w-full px-4 py-2 text-left text-blue-900 hover:bg-blue-100 transition rounded"
              >
                List of Campaign
              </button>
            </div>
          )}
        </div>

        <button
          onClick={() => navigate("/billing")}
          className="transition text-gray-300 hover:text-blue-500 px-2 py-1"
        >
          Billing
        </button>
      </nav>

      {/* 🔹 Credits + Profile */}
      <div className="flex items-center gap-6 relative" ref={profileMenuRef}>
        {/* Credits */}
        <div
          className="flex items-center bg-gradient-to-r from-blue-600 via-blue-500 to-blue-400 text-white text-base font-semibold py-1 px-5 rounded-full shadow-md cursor-pointer hover:scale-105 transition"
          onClick={() => setShowCreditDetails(!showCreditDetails)}
        >
          <span>{userCredit ? userCredit.current_credits : "--"}</span>
          <FaCoins className="ml-2 text-yellow-400 text-lg" />
        </div>

        {showCreditDetails && userCredit && (
          <div className="absolute top-full right-20 mt-2 bg-white text-gray-900 rounded-lg shadow-xl p-6 z-50 w-72 border border-blue-100">
            <div className="grid grid-cols-2 gap-y-2">
              <div className="font-semibold">Current Credit:</div>
              <div className="flex items-center">{userCredit.current_credits}
                <FaCoins className="ml-1 text-yellow-400" />
              </div>
              <div className="font-semibold">Used Credit:</div>
              <div>{userCredit.used_credits}</div>
              <div className="font-semibold">Bought Credit:</div>
              <div>{userCredit.bought_credits}</div>
              <div className="font-semibold">Updated At:</div>
              <div>{formatDate(userCredit.updated_at)}</div>
            </div>
          </div>
        )}

        {/* Profile Dropdown */}
        <div
          className="flex items-center cursor-pointer gap-2 group"
          onClick={() => setShowProfileMenu(!showProfileMenu)}
        >
          <img
            src={profileImage}
            alt="Profile"
            className="w-10 h-10 rounded-full object-cover border-2 border-blue-300 shadow transition group-hover:scale-105"
          />
          <span className="text-xs opacity-70">▼</span>
        </div>

        {showProfileMenu && (
          <div className="absolute top-full right-0 mt-2 bg-white border border-neutral-200 rounded-lg shadow-2xl z-50 w-56 overflow-hidden">
            <button
              onClick={() => navigate("/settings")}
              className="w-full px-6 py-3 text-left hover:bg-blue-50 transition text-neutral-700 font-semibold"
            >
              User Account
            </button>
            <button
              onClick={handleLogout}
              className="w-full px-6 py-3 text-left hover:bg-red-50 transition text-red-500 font-semibold"
            >
              Logout
            </button>
          </div>
        )}
      </div>
      {/* 🔹 Modern Glow Animation */}
      <style jsx>{`
        @keyframes glowPulse {
          0%, 100% { box-shadow: 0 0 16px #2557fa66; }
          50% { box-shadow: 0 0 24px #2557fa99; }
        }
        .animate-glow { animation: glowPulse 4s ease-in-out infinite; }
      `}</style>
    </header>
  );
};

export default HeaderMainPage;