import requests
from core.config import settings

# Base URL for all Unipile API requests, constructed from settings.
BASE_URL = f"https://{settings.UNIPILE_SUBDOMAIN}.unipile.com:{settings.UNIPILE_PORT}/api/v1"


def unipile_get(endpoint_path: str) -> requests.Response:
    """
    Sends a GET request to a specified Unipile API endpoint.

    Args:
        endpoint_path (str): The path of the API endpoint to request (e.g., "/accounts").

    Returns:
        requests.Response: The response object from the API call.
    """
    url = BASE_URL + endpoint_path
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    # Perform the GET request.
    response = requests.get(url=url, headers=headers)
    return response

def unipile_post(endpoint_path: str, form_data: str = None, json: dict = None) -> requests.Response:
    """
    Sends a POST request to a specified Unipile API endpoint.

    This function can handle both 'multipart/form-data' and 'application/json'
    content types. The caller must provide either `form_data` or `json`, but not both.

    Args:
        endpoint_path (str): The path of the API endpoint to post to (e.g., "/emails").
        form_data (str, optional): The payload for a 'multipart/form-data' request. Defaults to None.
        json (dict, optional): The payload for an 'application/json' request. Defaults to None.

    Returns:
        requests.Response: The response object from the API call.

    Raises:
        ValueError: If both `form_data` and `json` are provided, or if neither is provided.
    """
    url = BASE_URL + endpoint_path
    
    # Validate that exactly one payload type is provided.
    if form_data is not None and json is not None:
        raise ValueError("Provide either form_data or json, not both.")
    
    if form_data is None and json is None:
        raise ValueError("Either form_data or json must be provided as payload.")
    
    # Common headers for all POST requests.
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    
    # Handle multipart/form-data requests.
    if form_data is not None:
        headers["content-type"] = "multipart/form-data; boundary=---011000010111000001101001"
        response = requests.post(url=url, headers=headers, data=form_data)
    # Handle application/json requests.
    else:
        headers["content-type"] = "application/json"
        response = requests.post(url=url, headers=headers, json=json)
    
    return response
    
def unipile_delete(endpoint_path: str) -> requests.Response:
    """
    Sends a DELETE request to a specified Unipile API endpoint.

    Args:
        endpoint_path (str): The path of the API endpoint to send a delete request to (e.g., "/accounts/some_id").

    Returns:
        requests.Response: The response object from the API call.
    """
    url = BASE_URL + endpoint_path
    headers = {
        "accept": "application/json",
        "X-API-KEY": settings.UNIPILE_API_KEY
    }
    # Perform the DELETE request.
    response = requests.delete(url=url, headers=headers)
    return response