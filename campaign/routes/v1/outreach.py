from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    CampaignSendLinkedInInvitation,
                                    TestScheduleJob)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail, WebhooksNewMessage)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach, StartAutoOutreach,
                                    UpdateDraftEmail, SendEmailRequest, SendLinkedInRequest, 
                                    RespondEmail, RespondMessage)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile, LinkedInConnection, SentMessage, ReceivedMessage)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json
from bs4 import BeautifulSoup

router = APIRouter(
    prefix = "/api",
    tags=['Outreach Management'],
    responses={404: {'description': 'Not found'}},
)

#region EMAIL OUTREACH
@router.post("/campaign/emails/generate")
async def generate_email(
    email_generation: EmailGeneration,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Triggers the asynchronous generation of email drafts for a list of companies.

    This endpoint iterates through a list of company IDs and publishes a task
    to a message queue for each company that has a personality analysis available
    for its representative.

    Args:
        email_generation: The request body containing campaign and company IDs.
        request_user_id: The ID of the user making the request.
        db: The database session.
    """
    try:
        company_id_list = email_generation.company_id_list
        count = 0
        for company_id in company_id_list:
            # Fetch the representative to check for personality analysis.
            message = {
                "campaign_id" : str(email_generation.campaign_id),
                "company_id": str(company_id),
                "prompt_name": email_generation.prompt_name if email_generation.prompt_name else None,         
                "auto_outreach": False,
                "user_id": str(request_user_id),
            }
            # Publish a task to the message queue to create drafts.
            await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])
            count += 1

        return {
            "status": "success",
            "detail": f"{count} out of {len(company_id_list)} prospects have personality analysis and are set to generate drafts"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/emails/draft/generate", status_code=status.HTTP_201_CREATED)
async def generate_email(
    email_generation: EmailGeneration,
    db: AsyncSession = Depends(get_session)
):
    """
    Generates email drafts for a single company by calling the AI service directly.

    This is a synchronous endpoint that collects all necessary information for a
    single prospect and calls the AI service to get a list of email drafts.

    Args:
        email_generation: The request body containing the company ID.
        db: The database session.
    """
    company_id = email_generation.company_id_list[0]
    # Fetch all necessary data from the database (Company, Campaign, Representative).
    query = select(Company).filter(Company.company_id==company_id)
    results = await db.execute(query)
    company = results.scalars().first()
    query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
    results = await db.execute(query)
    campaign = results.scalars().first()
    query = select(Representative).filter(Representative.company_id==company_id)
    results = await db.execute(query)
    representative = results.scalars().first()

    # Prepare parameters for the AI service call.
    from_name = campaign.campaign_contact["user_nick_name"]
    to_name = representative.rep_name
    core_service = campaign.campaign_info["core_service"]
    key_benefits = campaign.campaign_info["key_benefits"]
    problem_solved = campaign.campaign_info["problem_solved"]
    must_have_info = campaign.campaign_info["must_have_info"]
    output_format = campaign.email_format
    personality_analysis = representative.personality_analysis
    email_personalization = representative.email_personalization
    communication_advice = representative.communication_advice
    industry = company.industry
    company_name = company.company_name

    params = {
        "from_name": from_name,
        "to_name": to_name,
        "core_service": core_service,
        "key_benefits": key_benefits,
        "problem_solved": problem_solved,
        "must_have_info": must_have_info,
        "output_format": output_format,
        "personality_analysis": personality_analysis,
        "email_personalization": email_personalization,
        "communication_advice": communication_advice,
        "industry": industry,
        "company_name": company_name,
        "prompt_name": email_generation.prompt_name if email_generation.prompt_name else None,
    }

    # Make a synchronous request to the AI service to generate drafts.
    response = requests.post(
        url = settings.AI_SERVICE_URL + "/api/nlp/generate_drafts/v2",
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        },
        json = params
    )

    if response.status_code not in [200, 201]:
        raise HTTPException(
            status_code=response.status_code,
            detail=response.text
        )
    
    drafts = response.json()["email_list"]
    # Clean up newline characters from the generated email bodies.
    for i in range(len(drafts)):
        drafts[i]["body"] = drafts[i]["body"].replace("\n","") 
    return drafts



@router.post("/campaign/emails/draft/create", status_code=status.HTTP_201_CREATED)
async def create_draft_email(
    create_draft: CreateDraftEmail,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    """
    Creates and saves a new email draft to the database.

    Args:
        create_draft: The data for the new draft.
        db: The database session.
    """
    try:    
        # Create a new Draft object from the request payload.
        draft = Draft(
            draft_type = create_draft.draft_type,
            company_id = create_draft.company_id,
            from_address = create_draft.from_address,
            from_name = create_draft.from_name,
            to_address = create_draft.to_address,
            to_name = create_draft.to_name,
            subject = create_draft.subject,
            body = create_draft.body,
            body_plain = create_draft.body_plain
        )
        db.add(draft)

        # Update the company's status to indicate drafts are waiting for review.
        stmt = update(Company).where(Company.company_id==create_draft.company_id).values(email_confirmation_status_id=2)
        await db.execute(stmt)
        await db.commit()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/emails/draft/update")
async def update_draft_email(
    update_draft_payload: UpdateDraftEmail,
    db: AsyncSession = Depends(get_session)
):
    """
    Updates an existing email draft in the database.

    Args:
        update_draft_payload: The data for updating the draft, including the draft ID.
        db: The database session.
    """
    try:
        data = {
            "subject": update_draft_payload.subject,
            "body": update_draft_payload.body
        }

        # Update the specified draft.
        stmt = (
            update(Draft)
            .filter(Draft.draft_id==update_draft_payload.draft_id)
            .values(**data)
            .execution_options(synchronize_session="fetch")
        )
        await db.execute(stmt)

        # Update the company's status to indicate the draft has been reviewed.
        stmt = (
            update(Company)
            .filter(Company.company_id==update_draft_payload.company_id)
            .values(email_confirmation_status_id=3)
        )
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/campaign/emails/draft/{company_id}")
async def get_draft_emails(
    company_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Retrieves all draft emails for a specific company.

    Args:
        company_id: The ID of the company whose drafts are to be retrieved.
        db: The database session.
    """
    try:
        query = select(Draft).where(Draft.company_id==company_id).order_by(Draft.draft_type.asc())
        results = await db.execute(query)
        draft_emails = results.scalars().all()
        return draft_emails
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/emails/send")
async def send_email(
    email_request: SendEmailRequest,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    """
    Queues emails to be sent to a list of companies.

    This endpoint iterates through a list of company IDs and publishes a task
    to send an email for each valid prospect.

    Args:
        email_request: The request body containing the list of company IDs.
        request_user_id: The ID of the user making the request.
        db: The database session.
    """
    try:
        email_sent = 0
        for company_id in email_request.company_id_list:
            # Fetch representative to check if an email address exists.
            query = select(Representative).filter(Representative.company_id==company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            if representative.rep_email is None:
                continue

            # Check for available, unsent drafts.
            query = (
                select(Draft)
                .filter(Draft.company_id==company_id, Draft.email_sent==False)
                .order_by(Draft.draft_type.asc())
            )
            result = await db.execute(query)
            draft_list = result.scalars().all()
            query = select(Company).where(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()

            # Perform checks to ensure an email should be sent.
            if company.auto_outreach == True:
                logger.warning("company is already set to auto email outreach")
            elif company.email_confirmation_status_id == 6:
                logger.warning("the prospect has already replied to the outreach attempt")
            elif len(draft_list) <= 0:
                logger.warning("no more draft email to send for company")
            else:
                message = {
                    "company_id": str(company_id),
                }
                # Publish a task to handle the actual email sending.
                await publish_task("send_email_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
                email_sent += 1

        return {
            "status": "success",
            "message": f"{email_sent} emails are set to be sent out of {len(email_request.company_id_list)} companies as requested"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.post("/campaign/{campaign_id}/outreach/auto")
async def start_auto_outreach(
    campaign_id: uuid.UUID,
    email_outreach: StartAutoOutreach,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    """
    Starts an automated outreach sequence for a given campaign.

    This function checks user credits, marks the campaign for auto-outreach,
    and then triggers the initial steps (like fetching personality profiles or
    generating drafts) for a batch of companies in that campaign.

    Args:
        campaign_id: The ID of the campaign to start.
        email_outreach: The request body, which can include a prompt name.
        db: The database session.
    """
    try:
        # Check if the user has enough credits to start the campaign.
        headers = {
            "accept": "application/json",
            "request-user-id": str(request_user_id)
        }
        response = requests.get(
            f"{settings.USER_SERVICE_URL}/api/users/credits",
            headers=headers
        )
        if response.status_code not in [200, 201]:
            raise HTTPException(
                status_code=503,
                detail=f"Failed to retrieve user credits: {response.text}"
            )
        else:
            user_credit = response.json()
            if user_credit["current_credits"] < 1:
                raise HTTPException(
                    status_code=400,
                    detail="User does not have enough credits"
                )

        # Check if the campaign is already in auto-outreach mode.
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()
        if campaign.campaign_auto_outreach == True:
            return {
                "status": "success",
                "detail": "campaign is already set to auto outreach"
            }

        # Check if there are any companies left to process.
        query = select(Company).filter(Company.campaign_id==campaign_id, Company.auto_outreach==False)
        results = await db.execute(query)
        companies = results.scalars().all()
        if len(companies) <= 0:
            return {
                "status": "success",
                "detail": "no more companies to set auto outreach"
            }

        # Set the campaign to auto-outreach mode.
        stmt = update(Campaign).where(Campaign.campaign_id==campaign_id).values(campaign_auto_outreach=True, prompt_name=email_outreach.prompt_name)
        await db.execute(stmt)
        await db.commit()

        # Process a batch of companies (limit 10).
        query = (
            select(Company)
            .filter(Company.campaign_id==campaign_id, Company.auto_outreach!=True)
            .order_by(Company.company_name.asc())
            .limit(10)
        )
        results = await db.execute(query)
        companies = results.scalars().all()

        for company in companies:
            query = select(Representative).filter(Representative.company_id==company.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            query = select(Draft).filter(Draft.company_id==company.company_id, Draft.draft_type==0)
            results = await db.execute(query)
            draft = results.scalars().first()

            if draft is None:
                message = {
                    "campaign_id" : str(campaign_id),
                    "company_id": str(company.company_id),
                    "prompt_name": email_outreach.prompt_name if email_outreach.prompt_name else None,
                    "auto_outreach": True,
                    "user_id": str(request_user_id)
                }
                await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])

        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/{campaign_id}/outreach/stop-auto")
async def stop_auto_outreach(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Stops the automated outreach for a specific campaign.

    Args:
        campaign_id: The ID of the campaign to stop.
        db: The database session.
    """
    try:
        stmt = update(Campaign).where(Campaign.campaign_id==campaign_id).values(campaign_auto_outreach=False)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/{campaign_id}/outreach/get-auto-status")
async def get_auto_outreach_status(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Gets the auto-outreach status for a specific campaign.

    Args:
        campaign_id: The ID of the campaign to check.
        db: The database session.
    """
    try:
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()
        auto_status = True if campaign.campaign_auto_outreach == True else False
        return {
            "status": "success",
            "auto_outreach": auto_status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.post("/campaign/emails/reply-mails/respond")
async def respond_reply_email(
    respond_email: RespondEmail,
    db: AsyncSession = Depends(get_session)
):
    """
    Sends a manual response to a received email.

    Args:
        respond_email: The request body containing the company ID and response content.
        db: The database session.
    """
    try:
        # Get the last received email for the company.
        query = (
            select(ReceivedEmail)
            .filter(ReceivedEmail.company_id==respond_email.company_id)
            .order_by(ReceivedEmail.created_at.desc())
        )
        result = await db.execute(query)
        received_email = result.scalars().first()

        query = select(Company).filter(Company.company_id==received_email.company_id)
        result = await db.execute(query)
        company = result.scalars().first()

        # Prevent manual response if auto-outreach is on.
        if company.auto_outreach:
            raise Exception("The company is set to auto outreach. You cannot respond to a reply email manually.")
        
        # Prevent sending another response if one has already been sent.
        if company.email_confirmation_status_id == 7:
            raise Exception("The prospect's reply has already been responded to.")

        query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
        result = await db.execute(query)
        campaign = result.scalars().first()
        campaign_contact = campaign.campaign_contact

        if received_email:
            # Find an available sender email account.
            query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc())
            results = await db.execute(query)
            sender = results.scalars().first()
            if sender is None:
                raise Exception("No more email senders available")

            from_name = "Robert Mandev"
            if campaign_contact is not None:
                if campaign_contact.get("user_nick_name") is not None:
                        from_name = campaign_contact["user_nick_name"]

            # Send the email using the Unipile client.
            email_client = EmailClient()
            email_client.set_unipile_account_id(sender.unipile_account_id)            
            response = email_client.send_email(
                subject=respond_email.subject,
                content=respond_email.body,
                name_from=from_name,
                name_to=received_email.from_info["display_name"],
                email_to=received_email.from_address
            )
            if response.status_code not in [200,201]:
                raise Exception(f"failed to send response to reply email from {received_email.from_address}")   
            else:
                # Create a record of the sent email.
                soup = BeautifulSoup(respond_email.body)
                body_plain = soup.text
                new_sent_email = SentEmail(
                    subject=respond_email.subject,
                    body=respond_email.body,
                    body_plain=body_plain,
                    to_info=[received_email.from_info],
                    from_address=sender.sender_address,
                    to_addresses=[received_email.from_address],
                    company_id=received_email.company_id,
                    tracking_id=response.json()["tracking_id"],
                    account_id=sender.unipile_account_id
                )
                db.add(new_sent_email)

                # Update the company status to "Responded".
                stmt = update(Company).filter(Company.company_id==received_email.company_id).values(email_confirmation_status_id=7)
                await db.execute(stmt)

                # Decrement the remaining emails for the sender.
                stmt = update(SenderEmail).filter(SenderEmail.sender_id==sender.sender_id).values(remaining_emails=SenderEmail.remaining_emails-1)
                await db.execute(stmt)
                await db.commit()
                return {
                    "status": "success"
                }
        else:
            raise Exception("no reply email found")

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) 
            
    

@router.get("/campaign/{campaign_id}/emails/sent-mails")
async def get_sent_emails(
    campaign_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    """
    Retrieves all sent emails for a given campaign, grouped by company.

    Args:
        campaign_id: The ID of the campaign.
        db: The database session.
    """
    try:
        query = select(Company).where(Company.campaign_id==campaign_id).order_by(Company.created_at.desc())
        results = await db.execute(query)
        company_list = results.scalars().all()

        result = []

        # Iterate through each company in the campaign.
        for company in company_list:
            # Fetch the representative and all sent emails for the company.
            query = select(Representative).filter(Representative.company_id==company.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            query = (
                select(SentEmail)
                .filter(SentEmail.company_id==company.company_id)
                .order_by(SentEmail.created_at.desc())
            )
            results = await db.execute(query)
            sent_emails_list = results.scalars().all()
            # If emails were sent, add the company's data to the result list.
            if len(sent_emails_list) > 0:
                result.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "rep_name": representative.rep_name,
                    "rep_email": representative.rep_email,
                    "sent_emails": sent_emails_list
                })

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/companies/{company_id}/emails/reply-mails")
async def get_reply_emails(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    """
    Retrieves all reply emails for a specific company.

    Args:
        company_id: The ID of the company.
        db: The database session.
    """
    try:
        query = select(ReceivedEmail).where(ReceivedEmail.company_id==company_id)
        results = await db.execute(query)
        reply_emails_list = results.scalars().all()
        return reply_emails_list 

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    
    

@router.get("/campaign/companies/{company_id}/emails/conversation")
async def get_email_conversation(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    """
    Retrieves and reconstructs the full email conversation for a company.

    Args:
        company_id: The ID of the company.
        db: The database session.
    """
    try:
        # Find the initial outreach email to determine the start of the conversation.
        query = select(SentEmail.created_at).filter(SentEmail.company_id==company_id, SentEmail.draft_type==0)
        results = await db.execute(query)
        start_date = results.scalars().first()

        # Fetch all sent emails for the company since the conversation started.
        query = (
            select(SentEmail)
            .filter(SentEmail.company_id==company_id, SentEmail.created_at>=start_date)
            .order_by(SentEmail.created_at.asc())
        )
        results = await db.execute(query)
        sent_emails_list = results.scalars().all()

        # Fetch all received emails for the company since the conversation started.
        query = (
            select(ReceivedEmail)
            .filter(ReceivedEmail.company_id==company_id, ReceivedEmail.created_at>start_date)
            .order_by(ReceivedEmail.created_at.asc())
        )
        results = await db.execute(query)
        received_emails_list = results.scalars().all()

        # Combine sent and received emails into a single list.
        emails_list = []
        for sent_email in sent_emails_list:
            email = {
                "internal_id": sent_email.internal_id,
                "subject": sent_email.subject,
                "body": sent_email.body,
                "timestamp": sent_email.created_at,
                "from_address": sent_email.from_address,
                "from_info": sent_email.from_info,
                "to_addresses": sent_email.to_addresses,
                "to_info": sent_email.to_info,
                "role": "user"
            }
            emails_list.append(email)

        for received_email in received_emails_list:
            email = {
                "internal_id": received_email.internal_id,
                "sent_email_internal_id": received_email.sent_email_internal_id,
                "subject": received_email.subject,
                "body": received_email.body,
                "timestamp": received_email.created_at,
                "from_address": received_email.from_address,
                "from_info": received_email.from_info,
                "to_addresses": received_email.to_addresses,
                "to_info": received_email.to_info,
                "suggested_response": received_email.suggested_response,
                "explanation": received_email.explanation,
                "emotions": received_email.emotions,
                "sentiment": received_email.sentiment,
                "key_takeaways": received_email.key_takeaways,                
                "role": "prospect"
            }
            emails_list.append(email)

        # Sort the combined list by timestamp to create a chronological conversation.
        emails_list = sorted(emails_list, key=lambda x: x["timestamp"])
        return emails_list

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/campaign/emails/{sent_email_id}/delete")
async def delete_sent_email(
    sent_email_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    """
    Deletes a specific sent email record from the database.

    Args:
        sent_email_id: The ID of the sent email to delete.
    """
    try:
        stmt = delete(SentEmail).where(SentEmail.internal_id==sent_email_id)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/campaign/{campaign_id}/emails/outreach-stats")
async def email_outreach_statistic(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    """
    Calculates and returns email outreach statistics for a campaign.

    Args:
        campaign_id: The ID of the campaign to get statistics for.
        db: The database session.
    """
    try:
        query = select(Company.company_id).filter(Company.campaign_id==campaign_id)
        results = await db.execute(query)
        companies = results.scalars().all()

        stats = {
            "campaign_id": str(campaign_id),
            "prospects_count": len(companies),
            "sent": {
                "count": 0,
                "rate": 0.0
            },
            "opened": {
                "count": 0,
                "rate": 0.0
            },
            "replied": {
                "count": 0,
                "rate": 0.0
            },
            "interested": {
                "count": 0,
                "rate": 0.0            
            }
        }

        # Calculate sent, opened, and replied counts and rates.
        query = select(func.count(distinct(SentEmail.company_id))).filter(SentEmail.company_id.in_(companies))
        result = await db.execute(query)
        stats["sent"]["count"] = result.scalar()
        stats["sent"]["rate"] = stats["sent"]["count"] / stats["prospects_count"] if stats["prospects_count"] > 0 else 0

        query = select(func.count(distinct(SentEmail.company_id))).filter(SentEmail.company_id.in_(companies), SentEmail.email_opened==True)
        result = await db.execute(query)
        stats["opened"]["count"] = result.scalar()
        stats["opened"]["rate"] = stats["opened"]["count"] / stats["sent"]["count"] if stats["opened"]["count"] > 0 else 0

        query = select(func.count(distinct(ReceivedEmail.company_id))).filter(ReceivedEmail.company_id.in_(companies))
        result = await db.execute(query)
        stats["replied"]["count"] = result.scalar()
        stats["replied"]["rate"] = stats["replied"]["count"] / stats["sent"]["count"] if stats["opened"]["count"] > 0 else 0

        return stats
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion



outreach_routes = router
