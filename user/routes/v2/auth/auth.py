from database.database import get_db
from database.models import User, user_role, UserCredit
from database.models import SystemRole
from routes.v2.system_error import (
    USER_EXIST, USER_NOT_FOUND,
    WRONG_PASSWORD, INVALID_PASSWORD,
)
from schema.role_schema import Role
from schema.user_schema import (
    LoginDataResponse, LoginDataCreate, Login,
    SignUser,
    UserPasswordUpdate, UserPasswordReset,
    LinkedInConnect,
    UnipileCreateAuthUrl
)
from schema.message_schema import Message
from core.security import hash_password, verify_password
from core.config import settings
from utils.unipile_utils.unipile_request_wrapper import unipile_post, unipile_get
from utils.unipile_utils.unipile_client import LinkedInClient
from fastapi import status, APIRouter, Header, UploadFile, File, Depends, HTTPException
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from azure.communication.email import EmailClient
import pandas as pd
import jwt
from loguru import logger
import uuid
import requests

# Create an API router
router = APIRouter(
    prefix="/api",
    tags=['Authentication Services'],
    responses={400: {'description': 'Bad request'}},
)

async def decode_token(token, secret_key):
    """Decodes a JWT token and returns the payload."""
    try:
        decoded = jwt.decode(token, secret_key, algorithms=['HS256'])
        return decoded
    except jwt.ExpiredSignatureError:
        logger.error('Token has expired')
        return None
    except jwt.InvalidTokenError:
        logger.error('Invalid token')
        return None

async def change_password(
    db: Session,
    db_user: User,
    new_password: str,
):
    """Hashes and updates a user's password in the database."""
    db_user.password = hash_password(new_password).decode("utf-8")
    db_user.pwd_updated_at = datetime.now()

    db.commit()

    return db_user

# Endpoint for creating a user login data, create login data also create new user 
@router.post('/signup', response_model=LoginDataResponse, status_code=status.HTTP_201_CREATED)
async def create_account(
    user: LoginDataCreate,
    db: Session = Depends(get_db)
):
    """Creates a new user account, assigns the default role, and initializes credits."""
    # Check if a user with the same email already exists
    db_item = db.query(
        User
    ).filter(
        User.email == user.email
    ).first()

    if db_item is not None:
        raise USER_EXIST
    
    if len(user.password) < 8:
        raise INVALID_PASSWORD

    else:
    # Hash the user's password
        hashed_password = hash_password(user.password)

        user_info = {
            "user_type": None,
            "nick_name": None,
            "company_type": None,
            "billing_plan": None,
            "linkedin_address": None
        }  

        new_user = User(
            email = user.email,
            password = hashed_password.decode("utf-8"),
            user_info = user_info
        )

        # Assign the default 'USER' role to the new user
        user_role_id = db.query(
            SystemRole
        ).filter(
            SystemRole.role_id == Role.USER
        ).first()
        
        new_user.system_role.append(user_role_id)
        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        #create user credit
        new_credit = UserCredit(
            user_id = new_user.user_id,
            current_credits = 0,
            bought_credits = 0,
            used_credits = 0
        )
        db.add(new_credit)

        db.commit()

    return {'status': 'success', 'message': 'Account created.'}


# Endpoint for account login, use in web 
@router.post('/login')
async def login_account(login: Login, db: Session = Depends(get_db)):
    """Authenticates a user and returns user details for token creation."""
    try:
        db_user = db.query(
                User
            ).filter(
                User.email == login.email
            ).first()

        if db_user is not None:
            # Verify the user's password
            is_password_valid = verify_password(login.password, db_user.password)

            if not is_password_valid:
                raise WRONG_PASSWORD
            
            if is_password_valid:
                # Prepare user data for JWT generation
                user_login = SignUser(
                    user_id = str(db_user.user_id),
                    role = db.query(
                        SystemRole
                    ).join(
                        user_role
                    ).join(
                        User
                    ).filter(
                        User.user_id == db_user.user_id
                    ).first().role_id
                )
                    
                return user_login
            else:
                raise WRONG_PASSWORD
        else:
            raise USER_NOT_FOUND
    except SQLAlchemyError:
        raise USER_NOT_FOUND


# Endpoint for changing password by user id
@router.put('/users/me/password/change', response_model=Message, status_code=status.HTTP_201_CREATED)
async def change_current_password(
    pwd_update: UserPasswordUpdate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    """Allows an authenticated user to change their own password."""
    # Check if user exist
    acc_to_update = db.query(
        User
    ).filter(
        User.user_id == request_user_id
    ).first()

    if not acc_to_update:
        return Message(msg=f"status: error - User not found")
    
    is_password_valid = verify_password(pwd_update.new_password, acc_to_update.password)

    # Check if the new password is different from the old password
    if is_password_valid:
        return Message(msg=f"status: error - New password is the same as the old one")

    if not is_password_valid:
        if pwd_update.new_password != pwd_update.confirm_password:
            return Message(msg=f"status: error - New passsword and confirm password do not match")
        else:
            acc_to_update = await change_password(db, acc_to_update, pwd_update.new_password)
            acc_to_update.pwd_updated_at = datetime.now()
            db.commit()

    return Message(msg=f"Account password changed")


@router.put('/users/admin/password/change', response_model=Message, status_code=status.HTTP_201_CREATED)
async def admin_change_password(
    pwd_update: UserPasswordReset,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    """Allows an administrator to reset a user's password."""
    # Check if user exist
    acc_to_update = db.query(
        User
    ).filter(
        User.user_id == pwd_update.user_id
    ).first()

    if not acc_to_update:
        return Message(msg=f"status: error - User not found")
    
    acc_to_update = await change_password(db, acc_to_update, pwd_update.password)
    acc_to_update.pwd_updated_at = datetime.now()
    db.commit()

    return Message(msg=f"Account password changed")


# Endpoint for changing password by forgot_password_token
@router.put('/users/password/change/{forgot_token}', response_model=Message, status_code=status.HTTP_201_CREATED)
async def change_password_with_verify_token(
    forgot_token: str,
    pwd_update: UserPasswordUpdate,
    db: Session = Depends(get_db)
):
    """Changes a user's password using a 'forgot password' verification token."""
    
    if pwd_update.new_password != pwd_update.confirm_password:
        return {'message':'The new password and confirm password do not match'}
    
    # Decode the token to get user information
    decoded_user_info = await decode_token(forgot_token, settings.SECRET_KEY)

    if decoded_user_info:
        acc_to_update = db.query(
            User
        ).filter(
            User.user_id == uuid.UUID(decoded_user_info["user_id"])
        ).first()

        if pwd_update.new_password != pwd_update.confirm_password:
            return Message(msg=f"status: error - New password and confirm password do not match")
        else:
            acc_to_update = await change_password(db, acc_to_update, pwd_update.new_password)
            acc_to_update.pwd_updated_at = datetime.now()
            db.commit()

    return Message(msg=f"Password updated successfully")


@router.post("/users/unipile/linkedin/auth-url",status_code=status.HTTP_201_CREATED)
async def generate_linkedin_auth_url(
    unipile_auth_url : UnipileCreateAuthUrl,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    """Generates a Unipile URL for a user to connect their LinkedIn account."""
    try:

        this_user = db.query(User).where(User.user_id==request_user_id).first()
        if this_user.unipile_linkedin_id:
            return {
                "msg": "this account is already connected to linkedin"
            }

        # Get the current UTC time
        current_time = datetime.now(timezone.utc)
        # Add 20 minutes to the current time for token expiration
        future_time = current_time + timedelta(minutes=20)
        # Format it to ISO 8601 with milliseconds and Z
        iso_8601_future_time = future_time.strftime(r'%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'    

        endpoint_path = f"/hosted/accounts/link"

        payload = {
            "type": "create",
            "providers": ["LINKEDIN"],
            "success_redirect_url": unipile_auth_url.success_redirect_url,
            "name": request_user_id,
            "expiresOn": iso_8601_future_time,
            "api_url": f" https://{settings.UNIPILE_SUBDOMAIN}.unipile.com:{settings.UNIPILE_PORT}",
            "notify_url": "https://api.roboman.ai/api/users/unipile/linkedin/connect",
            # "notify_url": "https://f9cb5dc723f1.ngrok-free.app/api/users/unipile/linkedin/connect",
            "failure_redirect_url": unipile_auth_url.failure_redirect_url
        }          

        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        if response.status_code in [200,201]:
            return response.json()
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=response.text
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/users/unipile/linkedin/reconnect-auth-url",status_code=status.HTTP_201_CREATED)
async def generate_reconnect_linkedin_auth_url(
    unipile_auth_url : UnipileCreateAuthUrl,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    """Generates a Unipile URL for a user to reconnect their LinkedIn account."""
    try:

        this_user = db.query(User).where(User.user_id==request_user_id).first()
        if this_user.unipile_linkedin_id is None:
            return {
                "msg": "this account is not connected to linkedin"
            }

        linkedin_client = LinkedInClient()
        linkedin_client.set_unipile_account_id(this_user.unipile_linkedin_id)
        response = linkedin_client.retrieve_account()
        if response.status_code not in [200,201]:
            raise HTTPException(
                status_code=response.status_code,
                detail=response.text
            )
        
        acc_info = response.json()
        if acc_info["sources"][0]["status"] == "OK":
            stmt = (
                update(User)
                .where(User.user_id==request_user_id)
                .values(linkedin_connection_status="CONNECTED")
            )
            db.execute(stmt)
            db.commit() 
            return {
                "status": "success"
            }

        else:
            # Get the current UTC time
            current_time = datetime.now(timezone.utc)
            # Add 20 minutes to the current time for token expiration
            future_time = current_time + timedelta(minutes=20)
            # Format it to ISO 8601 with milliseconds and Z
            iso_8601_future_time = future_time.strftime(r'%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'    

            endpoint_path = f"/hosted/accounts/link"

            payload = {
                "type": "reconnect",
                "providers": ["LINKEDIN"],
                "success_redirect_url": unipile_auth_url.success_redirect_url,
                "name": request_user_id,
                "expiresOn": iso_8601_future_time,
                "api_url": f" https://{settings.UNIPILE_SUBDOMAIN}.unipile.com:{settings.UNIPILE_PORT}",
                "notify_url": "https://api.roboman.ai/api/users/unipile/linkedin/reconnect",
                # "notify_url": "https://f9cb5dc723f1.ngrok-free.app/api/users/unipile/linkedin/reconnect",
                "failure_redirect_url": unipile_auth_url.failure_redirect_url,
                "reconnect_account": this_user.unipile_linkedin_id
            }     

            response = unipile_post(endpoint_path=endpoint_path, json=payload)
            if response.status_code in [200,201]:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=response.text
                )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

# Export the router
auth_routes = router
