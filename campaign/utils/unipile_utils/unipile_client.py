import requests
import json

from core.config import settings
from utils.unipile_utils.unipile_request_wrapper import unipile_get, unipile_post, unipile_delete


class UnipileClient:
    """
    A base client for interacting with the Unipile API.

    This class handles the basic configuration and provides methods for account management.
    """
    unipile_subdomain: str = settings.UNIPILE_SUBDOMAIN
    unipile_port: str = settings.UNIPILE_PORT    
    unipile_api_key: str = settings.UNIPILE_API_KEY
    unipile_account_id: str = None

    def __init__(self):
        """Initializes the UnipileClient."""
        pass    

    def set_unipile_account_id(self, value: str) -> None:
        """
        Sets the Unipile account ID for subsequent API calls.

        Args:
            value: The Unipile account ID string.
        """
        self.unipile_account_id = value    

    def retrieve_account(self) -> requests.Response:
        """
        Retrieves the details of the currently set Unipile account.

        Returns:
            The requests.Response object from the API call.
        """
        endpoint_path = f"/accounts/{self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def delete_account(self, unipile_account_id: str) -> requests.Response:
        """
        Deletes a specific Unipile account.

        Args:
            unipile_account_id: The ID of the account to delete.
        """
        endpoint_path = f"/accounts/{unipile_account_id}"
        response = unipile_delete(endpoint_path=endpoint_path)
        return response    
    
    def connect_imap_account(
        self,
        imap_user: str,
        smtp_user: str,
        imap_password: str,
        smtp_password: str,
        imap_host: str,
        smtp_host: str,
        imap_port: int,
        smtp_port: int
    ) -> requests.Response:
        """
        Connects a new IMAP email account to Unipile.

        Args:
            imap_user: The IMAP username.
            smtp_user: The SMTP username.
            imap_password: The IMAP password.
            smtp_password: The SMTP password.
            imap_host: The IMAP server host.
            smtp_host: The SMTP server host.
            imap_port: The IMAP server port.
            smtp_port: The SMTP server port.
        """
        endpoint_path = f"/accounts"
        payload = {
            "provider": "MAIL",
            "smtp_port": smtp_port,
            "smtp_host": smtp_host,
            "imap_port": imap_port,
            "imap_host": imap_host,
            "smtp_password": smtp_password,
            "imap_password": imap_password,
            "smtp_user": smtp_user,
            "imap_user": imap_user,
            "imap_encryption": "SSL"
        }
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

    def reconnect_imap_account(
        self,
        account_id: str,
        imap_user: str,
        smtp_user: str,
        imap_password: str,
        smtp_password: str,
        imap_host: str,
        smtp_host: str,
        imap_port: int,
        smtp_port: int
    ) -> requests.Response:
        """
        Reconnects or updates an existing IMAP email account in Unipile.

        Args:
            account_id: The Unipile account ID to reconnect.
            imap_user: The IMAP username.
            smtp_user: The SMTP username.
            imap_password: The IMAP password.
            smtp_password: The SMTP password.
            imap_host: The IMAP server host.
            smtp_host: The SMTP server host.
            imap_port: The IMAP server port.
            smtp_port: The SMTP server port.
        """
        endpoint_path = f"/accounts/{account_id}"
        payload = {
            "provider": "MAIL",
            "smtp_port": smtp_port,
            "smtp_host": smtp_host,
            "imap_port": imap_port,
            "imap_host": imap_host,
            "smtp_password": smtp_password,
            "imap_password": imap_password,
            "smtp_user": smtp_user,
            "imap_user": imap_user,
            "imap_encryption": "SSL"
        }
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

class LinkedInClient(UnipileClient):
    """
    A client for handling LinkedIn-specific operations via the Unipile API.
    """
    def __init__(self):
        """Initializes the LinkedInClient."""
        pass

    def retrieve_profile(self, profile_url: str) -> requests.Response:
        """
        Retrieves a LinkedIn user's profile information.

        Args:
            profile_url: The full URL of the LinkedIn profile.
        """
        # Extract the public profile ID from the URL.
        profile_id = profile_url.split('/')[-2] if profile_url.endswith('/') else profile_url.split('/')[-1]
        endpoint_path = f"/users/{profile_id}?account_id={self.unipile_account_id}"

        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_own_profile(self) -> requests.Response:
        """
        Retrieves the profile information of the authenticated user's account.
        """
        endpoint_path = f"/users/me?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_connections(self) -> requests.Response:
        """
        Retrieves the list of connections for the authenticated user's account.
        """
        endpoint_path = f"/users/relations?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def send_message(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        """
        Sends a message to a LinkedIn user.

        Args:
            message_body: The content of the message to send.
            recipient_profile_url: The URL of the recipient's LinkedIn profile.
        """
        # First, retrieve the recipient's profile to get their provider ID.
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]
        
        endpoint_path = f"/chats"
        boundary = "-----011000010111000001101001"
        # Construct the multipart/form-data payload manually.
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"attendees_ids\"\r\n\r\n"
            f"{recipient_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"text\"\r\n\r\n"
            f"{message_body}\r\n"
            f"{boundary}--"
        )
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response

    def send_invitation(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        """
        Sends a connection invitation to a LinkedIn user.

        Args:
            message_body: The personalized message to include with the invitation.
            recipient_profile_url: The URL of the recipient's LinkedIn profile.
        """
        endpoint_path = "/users/invite"
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]        
        payload = {
            "provider_id": recipient_id,
            "account_id": self.unipile_account_id,
            "message": message_body
        }   
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

class EmailClient(UnipileClient):
    """
    A client for handling email-specific operations via the Unipile API.
    """
    def __init__(self):
        """Initializes the EmailClient."""
        pass

    def send_email(self, subject: str, content: str, name_from: str, name_to: str, email_to: str) -> requests.Response:
        """
        Sends an email through a connected Unipile account.

        Args:
            subject: The subject of the email.
            content: The HTML body of the email.
            name_from: The display name of the sender.
            name_to: The display name of the recipient.
            email_to: The email address of the recipient.
        """
        # Retrieve the sender's account details to get the 'from' email address.
        resp = self.retrieve_account()     

        if resp.status_code != 200:
            return resp

        email_account = resp.json()
        email_from = email_account['name']

        # Prepare recipient, sender, and tracking information.
        boundary = "-----011000010111000001101001"
        recipient_info = [
            {
                "display_name": name_to.title(),
                "identifier": email_to
            }            
        ]

        sender_info = {
            "display_name": name_from,
            "identifier": email_from            
        }
        
        tracking_options = {
            "opens" : True,
            "label" : "new_email_sent"
        }

        endpoint_path = "/emails"
        
        # Construct the multipart/form-data payload manually.
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"subject\"\r\n\r\n"
            f"{subject}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"body\"\r\n\r\n"
            f"{content}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"from\"\r\n\r\n"
            f"{json.dumps(sender_info)}\r\n"
            f"{boundary}\r\n"            
            "Content-Disposition: form-data; name=\"to\"\r\n\r\n"
            f"{json.dumps(recipient_info)}\r\n"
            f"{boundary}\r\n"              
            "Content-Disposition: form-data; name=\"tracking_options\"\r\n\r\n"
            f"{json.dumps(tracking_options)}\r\n"
            f"{boundary}--"
        )             
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response
    
    def retrieve_email(self, email_id: str) -> requests.Response:
        """
        Retrieves a specific email by its Unipile ID.

        Args:
            email_id: The Unipile ID of the email to retrieve.
        """
        endpoint_path = f"/emails/{email_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response