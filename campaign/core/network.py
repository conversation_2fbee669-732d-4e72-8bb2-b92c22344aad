from schema.conversation_chatmessage_schema import ChatMessageCreate
from crud.crud_chat import get_lastest_messages
from core.config import settings

from sqlalchemy.ext.asyncio import AsyncSession
import aiohttp
import async_timeout
import asyncio
from loguru import logger


async def create_request_data(
    db: AsyncSession,
    message_data: ChatMessageCreate,
):	
    """
    Prepare request data for AI service by retrieving conversation history.
    
    Args:
        db: Async database session
        message_data: Chat message data containing conversation ID and content
        
    Returns:
        dict: Formatted input data with message content and conversation history
    """
    # Retrieve conversation history for the given conversation ID
    history = await get_lastest_messages(db, message_data.conversation_id)

    if not history:
        history = []

    # Format the input data structure for the AI service request
    input_data = {
        "request_message": message_data.message_content,
        "model_id": message_data.model_id,
        "history": history,
        "conversation_id": message_data.conversation_id,
    }

    return input_data


async def streaming(
    user_id: str,
    url: str,
    method: str,
    data: dict = None,
):
    """
    Stream data from an API endpoint with timeout handling.
    
    Args:
        user_id: User identifier for request header
        url: API endpoint URL
        method: HTTP method (get, post, etc.)
        data: Optional request payload
        
    Yields:
        bytes: Chunks of streaming response data
    """
    # Set user ID in request headers
    headers = {'request-user-id': user_id}

    if not data:
        data = {}

    # Set timeout for the request based on settings
    with async_timeout.timeout(settings.TIMEOUT):
        async with aiohttp.ClientSession() as session:
            # Get the appropriate HTTP method from session
            request = getattr(session, method)
            # Make the request and stream the response
            async with request(url, json=data, headers=headers) as response:
                try:
                    # Yield each chunk of data as it arrives
                    async for data in response.content.iter_any():
                        yield data
                        # Small delay to prevent overwhelming the client
                        await asyncio.sleep(0.01)
                except asyncio.CancelledError as e:
                    # Log if the streaming is cancelled
                    logger.error('Canceled!!!!!')
                    logger.error(f'{e}')