from celery_app.celery_setup import celery_app
import base64
from io import BytesIO
from loguru import logger

from procedures.mortgage_extraction.mortgage_transactions_extraction import (
    mgt_payment_year_extraction
)
from procedures.mortgage_extraction.mortgage_offer_extraction import (
    mortgage_offer_extraction
)

@celery_app.task(name="extract_mgt_transaction_years")
def extract_mgt_transaction_years(file_base64: str):
    try:
        file_bytes = base64.b64decode(file_base64)
        file_obj = BytesIO(file_bytes)
        result = mgt_payment_year_extraction(file_obj)
        logger.info(f"Extracted years: {result}")
        return result
    except Exception as e:
        return {"error": str(e)}
    
@celery_app.task(name="extract_mortgage_offer")
def extract_mortgage_offer_task(file_base64: str):
    try:
        file_bytes = base64.b64decode(file_base64)
        file_obj = BytesIO(file_bytes)
        result = mortgage_offer_extraction(file_obj)
        logger.info(f"Extracted mortgage offer: {result}")
        return result
    except Exception as e:
        return {"error": str(e)}
