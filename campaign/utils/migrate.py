from database.database import POSTGRES_DATABASEURL

from sqlalchemy import create_engine, text
from alembic.config import Config
from alembic import command
from loguru import logger
import os


def run_migration() -> None:
    drop_alembic_table()

    remove_migration_versions()

    alembic_cfg = Config("./alembic.ini")
    command.revision(alembic_cfg, message="ROBOMAN Chat DB Migration", autogenerate=True)
    command.upgrade(alembic_cfg, "head")
    
    logger.success("Chat DB Migrated")


def drop_alembic_table() -> None:
    logger.info('Dropping alembic table.....')
    try:
        engine = create_engine(POSTGRES_DATABASEURL)
        with engine.connect() as connection:
            connection.execute(text("DROP TABLE alembic_version"))
            connection.commit()
        logger.success('Drop success!')
    except Exception as e:
        logger.error('Delete failed!')
        logger.error(e)


def remove_migration_versions() -> None:
    folder_path = './migrations/versions'
    files = os.listdir(folder_path)
    logger.info(files)
    migration_files = [file for file in files if file != '__init__.py']
    logger.info(migration_files)
    if migration_files:
        logger.info('True')
        for migration_file in migration_files:
            migration_file_path = f'{folder_path}/{migration_file}'
            os.remove(migration_file_path)
            logger.info(f"Removed: {migration_file_path}")
    else:
        logger.info('False')