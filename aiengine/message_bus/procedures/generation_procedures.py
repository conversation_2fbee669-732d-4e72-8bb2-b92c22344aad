import json
import requests
import asyncio
from typing import Optional
from loguru import logger
from bs4 import BeautifulSoup
from sqlalchemy import update, delete, and_
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession
from llama_index.llms.openai import OpenAI
from llama_index.program.openai import OpenAIPydanticProgram
from llama_index.core.tools import FunctionTool, ToolMetadata

from database.models import SystemPrompt, PromptType
from message_bus.task_publisher import publish_task
from schema.ai_config_schema import EMAIL_GENERATION_V2, FOLLOW_UP, PARSING_FOLLOW_UP, RESPOND_PROMPT, EMAIL_GENERATION_V3
from schema.generation_schema import Email<PERSON>raft, EmailList
from schema.input_message_schema import FollowUp, GenerateResponseOutput
from llama_config.aiservice import run_agent
from agent.tools.email_postprocess import postprocess
from message_bus.rpc_client import RPC<PERSON>lient
from core.config import settings
from database.database import get_session
from utils.helpers import get_email_gen_prompt


async def create_follow_up_input(communication_advice: dict, personality_analysis: dict) -> str:
    """
    Create follow-up email advice based on personality analysis and communication advice.
    
    Args:
        communication_advice (dict): Communication style recommendations
        personality_analysis (dict): DISC and OCEAN personality profiles
        
    Returns:
        tuple: Follow-up advice string and recommended email sending intervals
    """
    # Initialize follow-up advice structure
    follow_up_advice = "3. Advice for each email:"
    day_of_emails = [0, 3, 3, 4]
    
    # Extract personality assessments
    ocean_assessment = str(personality_analysis["ocean_assessment"])[1:-1]
    disc_assessment = str(personality_analysis["disc_assessment"])[1:-1]

    # Format personality input
    personality_input = f"1. Recipient's personality analysis:\ndisc_assessment: {disc_assessment}\nocean_assessment: {ocean_assessment}\n"
    follow_up_input = personality_input

    # Format communication advice
    adjectives = ", ".join(communication_advice["adjectives"])
    description = ", ".join(communication_advice["description"])
    follow_up_input = follow_up_input + "\n" + f"2. Communication advice:\n\tAdjectives: {adjectives}\n\tDescription: {description}"    

    # Generate follow-up recommendations using LLM
    model_name = "gpt-4-turbo"
    agent = run_agent(
        model_name=model_name,
        system_prompt=FOLLOW_UP,
        history=None
    )   
    resp = agent.chat(follow_up_input)
    parsing_input = resp.response

    # Parse the LLM response into structured follow-up advice
    try:
        llm = OpenAI(
            model='gpt-4o',
            temperature=0,
            api_key=settings.OPENAI_API_KEY
        )
        program = OpenAIPydanticProgram.from_defaults(
            output_cls=FollowUp,
            verbose=False,
            llm=llm,
            prompt_template_str=PARSING_FOLLOW_UP
        )
        output = program(query=parsing_input) 
        logger.info(str(output.advice_for_emails))
        logger.info(str(output.day_of_emails))
        day_of_emails = output.day_of_emails
        for i in range(len(output.advice_for_emails)):
            follow_up_advice = follow_up_advice + f"\n\t- {output.advice_for_emails[i]}"   
                   
    except Exception as e:
        logger.error(f"Failed to parse follow-up advice: {e}")
    
    return follow_up_advice + "\n", day_of_emails

async def create_personal_input(personality_analysis: dict, email_personalization: dict) -> str:
    """
    Create personalized email input based on personality analysis and email personalization.
    
    Args:
        personality_analysis (dict): DISC and OCEAN personality profiles
        email_personalization (dict): Email style recommendations
        
    Returns:
        str: Formatted personalization input for email generation
    """
    # Extract personality assessments
    ocean_assessment = str(personality_analysis["ocean_assessment"])[1:-1]
    disc_assessment = str(personality_analysis["disc_assessment"])[1:-1]

    # Format personality input
    personality_input = f"1. Recipient's personality analysis:\ndisc_assessment: {disc_assessment}\nocean_assessment: {ocean_assessment}\n"

    # Format email personalization advice
    advice = email_personalization["advice"]
    advice.pop("Emojis/GIFs")
    advice.pop("Length Of Mail")
    advice_str = ""

    for key, value in advice.items():
        advice_str += f"\t{key}: {value}\n"

    email_personalization_input = f"2. Email personalization suggestion:\n{advice_str}"

    return personality_input + "\n" + email_personalization_input + "\n"

async def generate_drafts(
    from_name: str,
    to_name: str,
    core_service: str,
    key_benefits: list[str],
    problem_solved: str,
    must_have_info: Optional[str],
    output_format: Optional[str],
    personality_analysis: Optional[dict],
    communication_advice: Optional[dict],
    email_personalization: Optional[dict],
    industry: str,
    company_name: str,
    prompt_name: Optional[str],
):
    """
    Generate a sequence of email drafts for an outreach campaign.
    
    Args:
        from_name (str): Sender's name
        to_name (str): Recipient's name
        core_service (str): Main product/service being offered
        key_benefits (list[str]): List of key benefits
        problem_solved (str): Problem the product/service solves
        must_have_info (Optional[str]): Required information to include
        output_format (Optional[str]): Desired output format
        personality_analysis (Optional[dict]): Recipient's personality profile
        communication_advice (Optional[dict]): Communication style recommendations
        email_personalization (Optional[dict]): Email style recommendations
        industry (str): Recipient's industry
        company_name (str): Recipient's company name
        prompt_name (Optional[str]): Name of custom prompt to use
        
    Returns:
        dict: List of generated email drafts with sending schedule
    """
    # Commented out code contains a sample implementation that returns mock drafts

#     drafts_list = []
#     interval = [0, 3, 3, 4]
#     sample_body = """<!DOCTYPE html>
# <html>
# <body>
#     <p>[Salutation]</p>
    
#     <p>[Greeting]. [Purpose of the email]. [Brief introduction of the product/service]</p>
    
#     <p>With <b>[Product Name]</b>, you can:</p>
#     <ul>
#         <li><b>[Benefit 1]</b> – How it saves time/money/improves efficiency.</li>
#         <li><b>[Benefit 2]</b> – Unique advantage over competitors.</li>
#         <li><b>[Benefit 3]</b> – Testimonial/statistics to back it up.</li>
#     </ul>
    
#     <p>We’d love to show you how this can help. [Offer next step]</p>
    
#     <p>When works best for you?</p>
    
#     <p>[Closing line]</p>
    
#     <p>[Signature line with provided information]</p>
# </body>
# </html>"""
#     soup = BeautifulSoup(sample_body, "html.parser")
#     sample_body_plain = soup.text
#     for i in range(4):
#         drafts_list.append({
#             "draft_type": i,
#             "subject": f"Test email {i}",
#             "body": sample_body,
#             "body_plain": sample_body_plain,
#             "day_to_send": interval[i]
#         })

#     return {
#         "drafts_list": drafts_list
#     }

    try:
        # Format basic input information
        input_str = f'''0. Information
Name of sender or email writer: {from_name}
Name of recipient: {to_name}
Recipient's Company: {company_name}
Recipient's Company's industry: {industry}
Product name: {core_service}
Key Benefits: {key_benefits}
Problem Solved: {problem_solved}
Must have information: {must_have_info}.\n'''
        
        # Default email sequence timing
        number_of_emails = 4
        day_of_emails = [0, 3, 4, 5]         # interval days for each email to be sent after the previous email. 0 means the email will be sent immediately after the previous email.

        # Add personality-based customization if available
        if personality_analysis:
            personal_input = await create_personal_input(personality_analysis=personality_analysis, email_personalization=email_personalization)
            input_str = input_str + personal_input

        # Add follow-up advice if available
        if communication_advice is not None:
            follow_up_advice, day_of_emails = await create_follow_up_input(communication_advice=communication_advice, personality_analysis=personality_analysis)
            input_str = input_str + follow_up_advice       

        # Override with default timing
        day_of_emails = [0, 3, 4, 5]

        # Get custom prompt if specified
        async for session in get_session():
            email_gen_prompt = await get_email_gen_prompt(session, prompt_name=prompt_name)

        # Generate email drafts using LLM
        model_name = "gpt-4-turbo"
        agent = run_agent(
            model_name=model_name,
            system_prompt=email_gen_prompt,
            history=None
        )   
        resp = agent.chat(input_str)
        email_strings = resp.response.split("----------")

        # Process generated emails into structured format
        drafts_list = []
        for i in range(len(email_strings)):
            email = postprocess(email_strings[i].strip())
            soup = BeautifulSoup(email["content"], "html.parser")
            body_plain = soup.text
            drafts_list.append({
                "draft_type": i,
                "subject": email["head"],
                "body": email["content"],
                "body_plain": body_plain,
                "day_to_send": day_of_emails[i]
            })

        return {
            "drafts_list": drafts_list
        }     
    except Exception as e:
        raise e

async def generate_drafts_test(
    from_name: str,
    to_name: str,
    core_service: str,
    key_benefits: list[str],
    problem_solved: str,
    industry: str,
    company_name: str,
    unique_selling_proposition: str,
    target_audience: str,
    prompt_name: Optional[str],
):
    """
    Generate a sequence of email drafts for an outreach campaign.
    
    Args:
        from_name (str): Sender's name
        to_name (str): Recipient's name
        core_service (str): Main product/service being offered
        key_benefits (list[str]): List of key benefits
        problem_solved (str): Problem the product/service solves
        must_have_info (Optional[str]): Required information to include
        output_format (Optional[str]): Desired output format
        personality_analysis (Optional[dict]): Recipient's personality profile
        communication_advice (Optional[dict]): Communication style recommendations
        email_personalization (Optional[dict]): Email style recommendations
        industry (str): Recipient's industry
        company_name (str): Recipient's company name
        prompt_name (Optional[str]): Name of custom prompt to use
        
    Returns:
        dict: List of generated email drafts with sending schedule
    """

    try:
        # Format basic input information
        input_str = f'''0. Information
Name of sender or email writer: {from_name}
Name of recipient: {to_name}
Recipient's Company: {company_name}
Recipient's Company's industry: {industry}
Product name: {core_service}
Key Benefits: {key_benefits}
Problem Solved: {problem_solved}
Unique Selling Proposition: {unique_selling_proposition}
Target Audience: {target_audience}.\n'''
        
        # Default email sequence timing
        number_of_emails = 4
        day_of_emails = [0, 3, 4, 5]         # interval days for each email to be sent after the previous email. 0 means the email will be sent immediately after the previous email.
        
        # Get custom prompt if specified
        async for session in get_session():
            email_gen_prompt = await get_email_gen_prompt(session, prompt_name=prompt_name)

        # Generate email drafts using LLM
        model_name = "gpt-4-turbo"
        agent = run_agent(
            model_name=model_name,
            system_prompt=email_gen_prompt,
            history=None
        )   
        resp = agent.chat(input_str)
        email_strings = resp.response.split("----------")

        # Process generated emails into structured format
        drafts_list = []
        for i in range(len(email_strings)):
            email = postprocess(email_strings[i].strip())
            soup = BeautifulSoup(email["content"], "html.parser")
            body_plain = soup.text
            drafts_list.append({
                "draft_type": i,
                "subject": email["head"],
                "body": email["content"],
                "body_plain": body_plain,
                "day_to_send": day_of_emails[i]
            })

        return {
            "drafts_list": drafts_list
        }     
    except Exception as e:
        raise e

async def generate_drafts_v2(
    from_name: str,
    to_name: str,
    core_service: str,
    key_benefits: list[str],
    problem_solved: str,
    must_have_info: Optional[str],
    output_format: Optional[str],
    personality_analysis: Optional[dict],
    communication_advice: Optional[dict],
    email_personalization: Optional[dict],
    industry: str,
    company_name: str,
    prompt_name: Optional[str],        
):
    """
    Version 2 of email draft generation using Pydantic models for structured output.
    
    Args:
        Same parameters as generate_drafts()
        
    Returns:
        EmailList: Structured list of email drafts
    """
    try:
        # Format basic input information
        input_str = f'''0. Information
Name of sender or email writer: {from_name}
Name of recipient: {to_name}
Recipient's Company: {company_name}
Recipient's Company's industry: {industry}
Product name: {core_service}
Key Benefits: {key_benefits}
Problem Solved: {problem_solved}
Must have information: {must_have_info}.\n'''
        
        # Default email sequence timing
        number_of_emails = 4
        day_of_emails = [0, 3, 4, 5]         # interval days for each email to be sent after the previous email. 0 means the email will be sent immediately after the previous email.

        # Add personality-based customization if available
        if personality_analysis:
            personal_input = await create_personal_input(personality_analysis=personality_analysis, email_personalization=email_personalization)
            input_str = input_str + personal_input

        # Add follow-up advice if available
        if communication_advice is not None:
            follow_up_advice, day_of_emails = await create_follow_up_input(communication_advice=communication_advice, personality_analysis=personality_analysis)
            input_str = input_str + follow_up_advice       

        # Override with default timing
        day_of_emails = [0, 3, 4, 5]

        # Use predefined prompt template
        email_gen_prompt = EMAIL_GENERATION_V3 + "\n\n User Input: {query}"

        # Generate structured email drafts using Pydantic program
        llm = OpenAI(
            model='gpt-4o',
            api_key=settings.OPENAI_API_KEY
        )
        program = OpenAIPydanticProgram.from_defaults(
            output_cls=EmailList,
            verbose=False,
            llm=llm,
            prompt_template_str=email_gen_prompt
        )
        output = program(query=input_str)  

        # drafts_list = []

        # for i in range(len(email_strings)):

        #     email = postprocess(email_strings[i].strip())
        #     soup = BeautifulSoup(email["content"], "html.parser")
        #     body_plain = soup.text
        #     drafts_list.append({
        #         "draft_type": i,
        #         "subject": email["head"],
        #         "body": email["content"],
        #         "body_plain": body_plain,
        #         "day_to_send": day_of_emails[i]
        #     })

        return output
    except Exception as e:
        raise e


async def generate_response(
    sent_email: dict, 
    reply_email: dict, 
    # personality_analysis: dict=None, 
    # email_personalization: dict=None
):
    """
    Generate a response to an email based on previous correspondence and personality analysis.
    
    Args:
        sent_email (dict): Original sent email details
        reply_email (dict): Reply email that needs a response
        personality_analysis (dict, optional): Recipient's personality profile
        email_personalization (dict, optional): Email style recommendations
        
    Returns:
        dict: Generated response with sentiment analysis and explanation
    """
    # Extract email details
    sent_email_subject = sent_email["subject"]
    sent_email_body = sent_email["body"]
    reply_email_subject = reply_email["subject"]
    reply_email_body = reply_email["body"]
    sender_info = sent_email["sender_info"]
    sender_name = sender_info["name"]

    # Format basic input information
    input_str = f"-Information of the person sending out the response:\nName of sender or email writer: {sender_name}\n\n -Sent email: \nSubject: {sent_email_subject}\nBody: {sent_email_body}\n\n-Received email: \nSubject: {reply_email_subject}\nBody: {reply_email_body}"

    # # Add personality-based customization if available
    # if personality_analysis:
    #     ocean_label = personality_analysis["summary"]["ocean"]["label"]
    #     disc_label = personality_analysis["summary"]["disc"]["label"]
    #     ocean_profile = ', '.join(ocean_label).upper()
    #     disc_profile = ', '.join(disc_label).upper()
    #     user_profile = f"Personal analysis:\nThe person to whom you will send the email has an OCEAN analysis of {ocean_profile} and a DISC analysis of {disc_profile}"
    #     agent_input = f'{input_str}\n{user_profile}'
        
    #     # Add email personalization advice if available
    #     if email_personalization:
    #         email_advice = email_personalization["advice"]
    #         subject = email_advice["Subject"]
    #         subject_length = email_advice["Subject Length"]
    #         salutation = email_advice["Salutation"]
    #         greeting = email_advice["Greeting"]
    #         bullet_points = email_advice["Bullet Points"]
    #         closing_line = email_advice["Closing Line"]
    #         closing_greeting = email_advice["Closing Greeting"]
    #         complimentary_close = email_advice["Complimentary Close"]
    #         tone_of_words = email_advice["Tone Of Words"]
    #         overall_messaging = email_advice["Overall Messaging"]
    #         email_content_advice = f"""Email personalization advice:\n\tSubject: {subject}\n\tSubject Length: {subject_length}\n\tSalutation: {salutation}\n\tGreeting: {greeting}\n\tBullet Points: {bullet_points}\n\tClosing Line: {closing_line}\n\tClosing Greeting: {closing_greeting}\n\tComplimentary Close: {complimentary_close}\n\tTone Of Words: {tone_of_words}\n\tOverall Messaging: {overall_messaging}"""
    #         agent_input = agent_input + "\n" + email_content_advice
    # else:
    #     agent_input = f'{input_str}'

    agent_input = input_str

    logger.info(agent_input)

    # Generate response using structured Pydantic program
    llm = OpenAI(
        model='gpt-4o',
        temperature=0,
        api_key=settings.OPENAI_API_KEY
    )
    program = OpenAIPydanticProgram.from_defaults(
        output_cls=GenerateResponseOutput,
        verbose=False,
        llm=llm,
        prompt_template_str=RESPOND_PROMPT
    )
    output = program(query=agent_input)  
    
    # Return structured response with analysis
    return {
        "sentiment": output.sentiment,
        "emotions": output.emotions,
        "key_takeaways": output.key_takeaways,
        "response_subject": output.response_email_subject,
        "response_body": postprocess(output.response_email_body)["content"],
        "explanation": output.explanation
    }

async def summarize_draft(draft_content: str):
    """
    Summarize email draft content into a brief message under 150 characters.
    
    Args:
        draft_content (str): Original email draft content
        
    Returns:
        dict: Summarized content
    """
    # Prompt for summarizing email content
    summarize_prompt = """You are a helpful assistant that rewrites marketing outreach text. Turn the following content into one or two sentences under 150 characters that:

Greets the recipient

Briefly introduces the product or service

Preserves the tone and conversational, supportive style of the original message

Keep it clear, inviting, and professional while focusing on the core value proposition.

Example Input:
Struggling to juggle work and personal life?
Our Work-life Balance Coaching and Consulting offers a transformative approach:
Personalized coaching sessions – Tailored to your unique needs.
Proven strategies for time management – Enhance productivity and reduce stress.
Supportive community – Connect with like-minded individuals.
Flexible scheduling – Fits seamlessly into your busy life.
Discover how we can help you achieve balance. Let's discuss further.
When works best for you?
Robert Mandev

Example Output:
Struggling to find balance? I offer flexible, personalized coaching to help you thrive. When works best for a quick chat?
"""

    # Generate summary using LLM
    model_name = "gpt-4-turbo"
    agent = run_agent(
        model_name=model_name,
        system_prompt=summarize_prompt,
        history=None
    )
    resp = agent.chat(draft_content)
    summarized_content = resp.response
    # summarized_content = draft_content[:150]

    return {
        "summarized_content": summarized_content
    }

async def html_to_text(html_content: str):
    """
    Convert HTML email content to plain text.
    
    Args:
        html_content (str): HTML formatted email
        
    Returns:
        dict: Plain text version of the email
    """
    # Parse HTML and extract text
    soup = BeautifulSoup(html_content, "html.parser")
    text = soup.get_text()
    
    # Replace "email" with "message" for better tone
    text = text.replace("email","message")
    text = text.replace("Email","Message")

    return {
        "plain_text": text
    }