<h1 style="font-size: 36px; font-family: <PERSON><PERSON>, sans-serif; text-align: center;">
  ROBOMAN - AI Engine Service
</h1>

Welcome to the *AI Engine* documentation of the *ROBOMAN* project. This document provides details on code structures and API references specifically for the AI Engine service.

# Table of Contents

1. [Introduction](#introduction)
    - 1.1. [Overview](#overview)
    - 1.2. [Goals](#goals)

2. [Features](#features)
    - 2.1. [LLM Integration](#llm-integration)
    - 2.2. [Text Processing](#text-processing)
    - 2.3. [Campaign Tools](#campaign-tools)
    - 2.4. [Multimodal Capabilities](#multimodal-capabilities)

3. [Code Structure](#code-structure)

4. [Technologies](#technologies)

5. [API Reference](#api-reference)

6. [Setup and Installation](#setup-and-installation)

7. [Contribution](#contribution)

# Introduction

## Overview

The AI Engine service is the central intelligence component of the ROBOMAN platform, built on *Llama-Index* to facilitate efficient data indexing, retrieval, and AI-powered operations. This service encapsulates all AI-related logic, providing a unified interface for accessing various language models, embedding services, and specialized AI tools.

The service integrates multiple LLM providers (OpenAI, Anthropic, Google) and offers streaming capabilities, text-to-speech conversion, semantic search, and specialized tools for marketing campaign generation and management.

## Goals

The primary goals of the AI Engine service are:

- **Simplify Integration**: Offer an easy-to-use framework that allows for the straightforward integration of any state-of-the-art Deep Learning (DL) model or Large Language Model (LLM), whether accessed via an API or deployed locally.
- **Enhance Flexibility**: Ensure that the service is adaptable to various AI models and can be easily updated or extended to incorporate new advancements in the field.
- **Streamline Operations**: Provide a centralized platform that manages all AI processes, reducing the complexity and overhead associated with integrating multiple AI components.
- **Improve Accessibility**: Make advanced AI capabilities accessible to a broader audience, including developers with varying levels of expertise in AI and machine learning.
- **Enable Multimodal Processing**: Support processing of different data types including text, images, and audio.

# Features

## LLM Integration

- **Multiple Model Support**: Integration with OpenAI (GPT-4o, GPT-4), Anthropic (Claude), and Google (Gemini) models
- **Streaming Responses**: Real-time token streaming for interactive experiences
- **Token Counting**: Accurate tracking of token usage for cost management
- **Agent Framework**: Support for both OpenAIAgent and ReActAgent with tool integration

## Text Processing

- **Embedding Generation**: Vector embeddings for semantic search and similarity analysis
- **Text-to-Speech**: Convert text to audio using OpenAI's TTS API
- **HTML Processing**: Convert HTML to plain text for further processing
- **Semantic Similarity**: Calculate similarity between text passages

## Campaign Tools

- **Campaign Creation**: Tools for parsing and structuring campaign information
- **Email Generation**: Generate personalized email drafts based on campaign parameters
- **Response Generation**: Create structured responses to incoming messages
- **Prompt Management**: Database-backed system prompt management

## Multimodal Capabilities

- **Image Processing**: Handle image inputs alongside text using multimodal models
- **Audio Generation**: Create speech from text for enhanced communication
- **S3 Integration**: Store and retrieve multimedia assets

# Code Structure

<pre>
.
│   .env                      # Environment variables configuration
│   alembic.ini               # Database migration configuration
│   create_db.py              # Database initialization script
│   Dockerfile                # Container definition
│   main.py                   # Application entry point
│   README.md                 # This documentation file
│   requirements.txt          # Python dependencies
│
├───agent                     # Agent-related functionality
│   │   __init__.py
│   │
│   └───tools                 # Custom tools for agents
│           campaign_create.py    # Campaign creation tool
│           email_postprocess.py  # Email post-processing tool
│           __init__.py
│
├───core                      # Core configuration
│       config.py             # Application settings
│       __init__.py
│
├───crud                      # Database operations
│       crud_base.py          # Base CRUD operations
│       __init__.py
│
├───database                  # Database definitions
│       database.py           # Database connection setup
│       models.py             # SQLAlchemy models
│       __init__.py
│
├───llama_config              # Llama-Index configuration
│       aiservice.py          # LLM and embedding service setup
│       __init__.py
│
├───migrations                # Database migration files
│   │   env.py
│   │   README
│   │   script.py.mako
│   │
│   └───versions
│           __init__.py
│
├───routes                    # API routes
│   └───v1
│           llm.py            # LLM model management endpoints
│           nlp_engine.py     # NLP processing endpoints
│
├───schema                    # Data schemas
│       ai_config_schema.py   # AI configuration schemas
│       input_message_schema.py  # Input message schemas
│       system_prompt_schema.py  # System prompt schemas
│
└───utils                     # Utility functions
    │   amazon_storage.py     # S3 storage utilities
    │   history.py            # Conversation history utilities
    │   migrate.py            # Database migration utilities
    │   __init__.py
    │
    └───tools                 # Specialized utility tools
            humantic.py       # Humantic AI personality analysis
            streaming.py      # Token streaming utilities
            stt.py            # Speech-to-text utilities
            summary.py        # Text summarization utilities
            __init__.py
</pre>

# Technologies

The AI Engine service leverages several key technologies:

- **Llama-Index**: Core framework for data indexing, retrieval, and LLM integration
- **FastAPI**: Modern, high-performance web framework for building APIs
- **SQLAlchemy**: SQL toolkit and ORM for database operations
- **Alembic**: Database migration tool
- **OpenAI API**: Access to GPT models and embeddings
- **Anthropic API**: Access to Claude models
- **Google API**: Access to Gemini models
- **pgvector**: PostgreSQL extension for vector operations
- **boto3**: AWS SDK for S3 integration
- **RabbitMQ**: Message broker for service communication

# API Reference

## LLM Endpoints

- **POST /llm/stream/default**: Stream LLM responses for default chat with campaign creation tool
- **POST /llm/campaign/parser**: Parse unstructured text to extract campaign information
- **POST /campaign/initiation**: Initialize campaign from text description using GPT-4

## Text Processing Endpoints

- **POST /openai/text-to-speech**: Convert text to speech and store in S3
- **POST /nlp/text-embedding**: Generate embeddings for sample text
- **POST /nlp/html-to-plain-text**: Convert HTML to plain text
- **POST /nlp/similarity**: Calculate semantic similarity between two texts

## Email Generation Endpoints

- **POST /nlp/generate_drafts**: Generate email drafts based on campaign parameters
- **POST /nlp/generate_drafts/v2**: Generate email drafts using improved v2 algorithm

## Prompt Management Endpoints

- **GET /nlp/prompts/email-gen**: Get all email generation prompts from database
- **PUT /nlp/prompts/email-gen/enable**: Enable specific email prompt and disable all others

## FAQ Management Endpoints

- **POST /nlp/faqs/upload**: Upload CSV of FAQs, generate embeddings, and store in database

## Response Generation Endpoints

- **POST /nlp/response/generate**: Generate structured response to a message using GPT-4

# Setup and Installation

## Prerequisites

- Docker and Docker Compose
- Python 3.9+
- PostgreSQL with pgvector extension
- AWS S3 account (for storage features)
- API keys for OpenAI, Anthropic, and/or Google (depending on which models you plan to use)

## Environment Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd ROBOMAN/aiengine
   ```

2. Create a `.env` file with the following variables:
   ```
   # Database
   DATABASE_NAME=aiengine_db
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=postgres
   DATABASE_HOST=postgres
   DATABASE_PORT=5432

   # RabbitMQ
   RABBITMQ_DEFAULT_USER=guest
   RABBITMQ_DEFAULT_PASS=guest
   RABBITMQ_HOST=rabbitmq
   RABBITMQ_PORT=5672
   EXCHANGE_NAME=roboman_exchange
   QUEUE_NAME=aiengine_queue

   # OpenAI
   OPENAI_API_KEY=your_openai_api_key

   # AWS S3
   S3_ACCESS_ID=your_aws_access_key
   S3_SECRET_KEY=your_aws_secret_key
   S3_BUCKET_NAME=your_bucket_name

   # JWT
   JWT_ALGORITHM=HS256
   JWT_SECRET=your_jwt_secret

   # Project
   PROJECT_NAME=ROBOMAN AI Engine
   ```

3. Build and start the service:
   ```
   docker-compose up --build
   ```

4. The service will be available at `http://localhost:8005` with API documentation at `http://localhost:8005/docs`.

# Contribution

Kindly refer to the contribution section in the overview documentation [here](../README.md#contribution).

