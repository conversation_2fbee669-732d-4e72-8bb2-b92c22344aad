from __future__ import annotations

from typing import Optional, Union
import uuid
from pydantic import BaseModel
from pydantic import constr
from pydantic import EmailStr
from dataclasses import dataclass


# Use to sign JWT
@dataclass
class SignUser:
    user_id: uuid.UUID
    role: int

# Login Data
class LoginDataBase(BaseModel):
    email: EmailStr


class LoginDataCreate(LoginDataBase):
    password: str


class Login(BaseModel):
    email: EmailStr
    password: constr(min_length=8)


class LoginDataUpdate(LoginDataBase):
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    verified: Optional[bool] = None



# Account
class AccountBase(BaseModel):
    user_id: uuid.UUID


class UserResponse(BaseModel):
    email: EmailStr
    user_info: Optional[dict] = None
    user_linkedin_info: Optional[dict] = None
    unipile_linkedin_id: Optional[str] = None
    linkedin_connection_status: Optional[str] = None

    class Config:
        orm_mode = True


class UserUpdate(BaseModel):
    user_type: Optional[str] = None
    nick_name: Optional[str] = None
    company_type: Optional[str] = None
    billing_plan: Optional[str] = None
    linkedin_address: Optional[str] = None    
    class Config:
        orm_mode = True

class LinkedInConnect(BaseModel):
    unipile_account_id: Optional[str] = None

class UnipileCallback(BaseModel):
    status: str
    account_id: str
    name: str

class UnipileStatusUpdate(BaseModel):
    AccountStatus: dict

class UnipileCreateAuthUrl(BaseModel):
    success_redirect_url: str
    failure_redirect_url: str

class AdminAccountUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    gender: Optional[str] = None
    avatar: Optional[str] = None
    is_active: Optional[bool] = None


class AccountCreate(AccountBase):
    first_name: str
    last_name: str
    gender: Optional[str] = None
    avatar: Optional[str] = None
    role_id: Union[int, None] = 1
    is_active: bool = True


class UserPasswordUpdate(BaseModel):
    new_password: str
    confirm_password: str


class UserPasswordReset(BaseModel):
    user_id: str
    password: str