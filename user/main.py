from message_bus.user_info_server import user_rpc_server
from utils.migrate import run_migration
# Import API route modules
from routes.v2.system.user import user_routes
from routes.v2.system.user_credits import user_credits_routes
from routes.v2.system.stripe_payment import stripe_payment_routes
from routes.v2.system.stripe_products import stripe_products_routes
from routes.v2.auth.auth import auth_routes
from core.config import settings
from message_bus.rpc_server import start_rpc_server

# Import necessary libraries
from fastapi import FastAPI
import uvicorn
import asyncio
from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    An asynchronous context manager to handle application startup and shutdown events.
    This is used by FastAPI to manage background tasks.

    Args:
        app (FastAPI): The FastAPI application instance.
    """
    print("Starting task consumers...")
    # Create a background task for the RPC server when the application starts.
    rpc_server = asyncio.create_task(start_rpc_server())

    # The 'yield' statement passes control back to FastAPI to run the application.
    yield

    # The code after 'yield' is executed on application shutdown.
    # Cancel the RPC server task to ensure a graceful shutdown.
    rpc_server.cancel()

    try:
        # Wait for the RPC server task to acknowledge the cancellation.
        await rpc_server
    except asyncio.CancelledError:
        # This exception is expected when a task is cancelled.
        pass

# Initialize the FastAPI application, registering the lifespan manager.
app = FastAPI(lifespan=lifespan)

# Include the various API routers into the main application.
# Each router handles a specific set of related endpoints.
app.include_router(auth_routes)
app.include_router(user_routes)
app.include_router(user_credits_routes)
app.include_router(stripe_payment_routes)
app.include_router(stripe_products_routes)


@app.get('/api/health')
async def health_check():
    """A simple health check endpoint to confirm the API is running."""
    return {'status': 'ok'}


# This block runs when the script is executed directly (e.g., `python main.py`).
if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    # Run database migrations before starting the application.
    run_migration()
    # Start the Uvicorn server to run the FastAPI application.
    # It listens on all available network interfaces (0.0.0.0).
    # `reload=True` enables auto-reloading for development.
    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment
