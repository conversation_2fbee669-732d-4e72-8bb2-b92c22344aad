from config import settings
from core import route, file_route
from schema.input_data_schema import (
    ModelUpdate,
)
from schema.user_schema import (
    Login, LoginDataCreate,
    UserUpdate, UserPasswordUpdate,
    UnipileCallback,
    UnipileCreateAuthUrl,
    UnipileStatusUpdate
)
from schema.chat_schema import  ChatMessageCreate
from schema.message_schema import Email
from schema.input_data_schema import (TextCampaign, CampaignCreate, 
                                      SendEmailRequest, AudioCreate, 
                                      CompanyEmailUpdate, CompanyAddManual, 
                                      CompanyLinkedInMsgUpdate, CampaignDetails,
                                      CampaignContentUpdate, CampaignSendLinkedInMessage, CampaignSendLinkedInInvitation,
                                      CampaignDetailsUpdate, GenerateEmail,
                                      WebhooksNewEmail, EmailGeneration,
                                      WebhooksEmailTracking, EmailSend,
                                      TestScheduleJob, EmailOutreach,
                                      UpdateDraftEmail, GenerateResponse,
                                      EnablePrompt, RespondEmail, CompanyMatching,
                                      SendLinkedInRequest, WebhooksNewMessage,
                                      HtmlToText, RespondMessage, SimilarityCheck,
                                      CampaignContact)
from schema.campaign_schemas.outreach import StartAutoOutreach
from schema.user_schemas.credit_schemas import UsedCreditCreate
from schema.user_schemas.stripe_payment_schema import CheckoutSessionCreate, CheckoutSessionObject, EventObject
from schema.user_schemas.stripe_product_schema import StripeProductCreate, StripePriceCreate, StripePriceUpdate, StripeProductUpdate

import uuid
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Security, FastAPI, status, Request, Response, File, UploadFile
from fastapi.security.api_key import APIKeyHeader
# from pyngrok import ngrok
import requests
from loguru import logger

app = FastAPI()
api_key_header = APIKeyHeader(name='Authorization')

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)
"""
This file is used to publish API from services

Route decorator is high-level wrapper of FastAPI routers

You can have many APIs in your code. However, user can only use APIs publish in this file
"""


maintenance_status = False

@app.get('/api/health', tags=['App Management'])
async def health_check():
    return {'status': 'ok'}


@app.post("/api/maintenance", tags=['App Management'])
async def set_state(status: bool):
    global maintenance_status
    maintenance_status = status
    return {"status": "success"}


@app.get("/api/maintenance", tags=['App Management'])
async def get_state():
    return {"maintenance": maintenance_status}


#################### AUTHENTICATION #####################
#region AUTHENTICATION
@route(
    request_method=app.post,
    path='/api/signup',
    status_code=status.HTTP_201_CREATED,
    payload_key='email_password',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    tags=["Authentication"],
)
async def create_user(
    email_password: LoginDataCreate,
    request: Request,
    response: Response,
):
    """
    Handles user registration.

    This endpoint receives user credentials and forwards the request to the user
    service to create a new user account.
    """
    pass


@route(
    request_method=app.post,
    path='/api/login',
    status_code=status.HTTP_200_OK,
    payload_key='email_password',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func='auth.sign_jwt',
    tags=["Authentication"],
)
async def login(
    email_password: Login,
    request: Request,
    response: Response
):
    """
    Authenticates a user and returns a JWT.

    This endpoint validates user credentials against the user service. On
    successful authentication, it generates and returns a JWT access token.
    """
    pass


@route(
    request_method=app.post,
    path='/api/users/email/forgot_password',
    status_code=status.HTTP_200_OK,
    payload_key= 'email_verify',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    tags=["Authentication"],
)

async def send_email_forgot_password(
    email_verify: Email,
    request: Request,
    response: Response,
):
    """
    Initiates the password reset process for a user.

    This endpoint sends a password reset link to the user's registered email
    address via the user service.
    """
    pass


@route(
    request_method=app.put,
    path='/api/users/password/change/{forgot_token}',
    status_code=status.HTTP_200_OK,
    payload_key='pwd_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    tags=["Authentication"],
)
async def change_password_by_verify_token(
    forgot_token: str,
    pwd_update: UserPasswordUpdate,
    request: Request,
    response: Response,
):
    """
    Changes a user's password using a verification token.

    This endpoint allows a user to set a new password after verifying their
    identity with a token received from the forgot password process.
    """
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/auth-url',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_auth_url",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Authentication"],
)
async def generate_linkedin_auth_url(
    unipile_auth_url: UnipileCreateAuthUrl,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Generates a Unipile authentication URL for connecting a LinkedIn account.

    This endpoint requires user authentication and forwards the request to the
    user service to get a unique URL for the LinkedIn OAuth flow.
    """
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/reconnect-auth-url',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_auth_url",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Authentication"],
)
async def generate_reconnect_linkedin_auth_url(
    unipile_auth_url: UnipileCreateAuthUrl,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Generates a Unipile URL to reconnect a LinkedIn account.

    This endpoint is for users who need to re-authenticate their existing
    LinkedIn connection, forwarding the request to the user service.
    """
    pass

#endregion 




#################### PERSONAL #####################
#region PERSONAL
@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/connect',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_callback",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def create_linkedin_connection(
    unipile_callback: UnipileCallback,
    request: Request,
    response: Response
):
    """
    Handles the callback from Unipile to connect a LinkedIn account.

    This is a webhook endpoint that receives data from Unipile after a user
    authorizes the connection and forwards it to the user service.
    """
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/reconnect',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_callback",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def reconnect_linkedin_connection(
    unipile_callback: UnipileCallback,
    request: Request,
    response: Response
):
    """
    Handles the callback from Unipile to reconnect a LinkedIn account.

    This webhook receives data to re-establish a user's LinkedIn connection
    and forwards it to the user service.
    """
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/status-update',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_status_update",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def update_linkedin_connection_status(
    unipile_status_update: UnipileStatusUpdate,
    request: Request,
    response: Response
):
    """
    Receives and processes status updates for a LinkedIn connection from Unipile.

    This webhook is used by Unipile to send asynchronous updates, such as
    when credentials become invalid.
    """
    pass


@route(
    request_method=app.put,
    path='/api/users/unipile/linkedin/disconnect',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def remove_linkedin_connection(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Allows an authenticated user to disconnect their LinkedIn account.

    This endpoint forwards the disconnect request to the user service for the
    currently authenticated user.
    """
    pass

@route(
    request_method=app.put,
    path='/api/users/me/password/change',
    status_code=status.HTTP_200_OK,
    payload_key='pwd_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def change_password(
    pwd_update: UserPasswordUpdate,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Allows an authenticated user to change their own password.

    The user must be logged in to use this endpoint.
    """
    pass


@route(
    request_method=app.get,
    path='/api/users/me',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.user_schema.UserResponse',
    tags=["Personal"],
)
async def get_personal_information(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Retrieves the profile information for the currently authenticated user.

    Forwards the request to the user service to get the user's details.
    """
    pass


@route(
    request_method=app.put,
    path='/api/users/me/update-info',
    status_code=status.HTTP_200_OK,
    payload_key="update_info",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def update_personal_information(
    update_info: UserUpdate,
    request: Request, response: Response,
    token = Security(api_key_header)
):
    """
    Allows an authenticated user to update their personal information.

    Forwards the update request to the user service.
    """
    pass

#endregion








#################### ADMIN #####################
#region ADMIN



#region ELASTICSEARCH
@route(
    request_method=app.get,
    path='/api/campaign/elastic-search/records/{page_size}/{from_}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"],
)
async def get_es_records(
    page_size: int,
    from_: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    """
    Retrieves records from Elasticsearch with pagination.

    Forwards the request to the campaign service to fetch a paginated list of records.
    """
    pass

@file_route(
    request_method=app.post,
    path="/api/campaign/elastic-search/ingest",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"],        
)
async def ingest_companies(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    """
    Ingests company data from an uploaded file into Elasticsearch.

    This endpoint accepts a file upload and forwards it to the campaign service
    to populate the Elasticsearch index with company information.
    """
    pass


@route(
    request_method=app.delete,
    path="/api/campaign/elastic-search/remove-index",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"]        
)
async def remove_es_index(
    request: Request,
    response: Response,
    token=Security(api_key_header)        
):
    """
    Removes the Elasticsearch index.

    Forwards a request to the campaign service to delete the entire Elasticsearch index.
    """
    pass
#endregion




#region EMAIL SENDERS
@file_route(
    request_method=app.post,
    path="/api/admin/senders/add",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"],        
)
async def add_email_sender(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass

#get email senders
@route(
    request_method=app.get,
    path="/api/admin/senders",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"]
)
async def get_email_senders(
    request: Request,
    response: Response,
    token=Security(api_key_header)    
):
    pass

@route(
    request_method=app.delete,
    path="/api/admin/senders/{sender_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"]
)
async def delete_email_sender(
    sender_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)    
):
    pass

#endregion

@file_route(
    request_method=app.post,
    path="/api/nlp/faqs/upload",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Admin"],        
)
async def upload_faqs(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass

@route(
    request_method=app.get,
    path='/api/nlp/prompts/email-gen',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def get_email_gen_prompts(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path='/api/nlp/prompts/email-gen/enable',
    status_code=status.HTTP_200_OK,
    payload_key='enable_prompt',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def enable_email_gen_prompt(
    enable_prompt: EnablePrompt,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass



@route(
    request_method=app.get,
    path='/api/users',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def get_total_users(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


# @route(
#     request_method=app.get,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model='schema.user_schema.UserResponse',
#     response_list=False,
#     tags=["Admin"],
# )
# async def get_user(
#     user_id: uuid.UUID, 
#     request: Request, 
#     response: Response, 
#     token = Security(api_key_header)
# ):
#     """
#     Retrieves the details of a specific user by their ID.

#     This endpoint is restricted to admin users.
#     """
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key='user',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model='schema.user_schema.AccountResponse',
#     tags=["Admin"],
# )
# async def update_user(
#     user_id: uuid.UUID,
#     user: AdminAccountUpdate,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     """
#     Updates a user's account information.

#     This endpoint allows an admin to modify the details of a specific user.
#     """
#     pass


# @route(
#     request_method=app.delete,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )
# async def delete_user(
#     user_id: uuid.UUID,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     """
#     Deletes a user account.

#     This endpoint allows an admin to permanently remove a user from the system.
#     """
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/role/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key='role_update',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )

# async def update_user_role(
#     user_id: uuid.UUID,
#     role_update: RoleUpdate,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     """
#     Updates the role of a specific user.

#     This endpoint allows an admin to change a user's permissions by assigning
#     a new role.
#     """
#     pass


# @route(
#     request_method=app.post,
#     path='/api/users/role/create',
#     status_code=status.HTTP_200_OK,
#     payload_key='user_create',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )

# async def create_user_role(
#     user_create: RoleCreate,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     """
#     Creates a new user with a specified role.

#     This endpoint allows an admin to create a new user account and assign
#     a role simultaneously.
#     """
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/admin/password/change',
#     status_code=status.HTTP_200_OK,
#     payload_key='pwd_update',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     tags=["Admin"],
# )
# async def admin_change_password(
#     pwd_update: UserPasswordReset,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     """
#     Resets a user's password.

#     This endpoint allows an admin to change the password for any user account.
#     """
#     pass

#endregion



#endregion




#################### CONVERSATION #####################
#region CONVERSATION
@route(
    request_method=app.post,
    path='/api/conversation/new',
    status_code=status.HTTP_201_CREATED,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.chat_schema.ConversationResponse',
    tags=["Conversation"],
)
async def create_conversation(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Creates a new conversation for the authenticated user.

    Forwards the request to the campaign service to initialize a conversation.
    """
    pass


@route(
    request_method=app.get,
    path='/api/conversation-list',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.chat_schema.ConversationResponse',
    response_list=True,
    tags=["Conversation"],
)
async def get_conversation_list_by_user_id(
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    """
    Retrieves the list of conversations for the authenticated user.

    Forwards the request to the campaign service.
    """
    pass


@route(
    request_method=app.get,
    path='/api/conversation/{conversation_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_list=True,
    tags=["Conversation"],
)
async def get_history_by_conversation_id(
    conversation_id: uuid.UUID,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    """
    Retrieves the message history for a specific conversation.

    Forwards the request to the campaign service to get all messages
    associated with the given conversation ID.
    """
    pass


@route(
    request_method=app.post,
    path='/api/conversation/message',
    status_code=status.HTTP_201_CREATED,
    payload_key="message_data",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=True,
    tags=["Conversation"],
)
async def send_message(
    message_data: ChatMessageCreate,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    """
    Sends a message in a conversation and streams the response.

    This endpoint forwards a user's message to the campaign service and
    streams the AI-generated response back to the client.
    """
    pass


@route(
    request_method=app.post,
    path='/api/openai/text-to-speech',
    status_code=status.HTTP_201_CREATED,
    payload_key="message_data",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=False,
    tags=["Conversation"],
)
async def get_audio(
    message_data: AudioCreate,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    """
    Converts text to speech.

    Forwards a text payload to the AI service to generate an audio file.
    """
    pass

@route(
    request_method=app.post,
    path='/api/campaign/initiation',
    status_code=status.HTTP_200_OK,
    payload_key="input_data",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=False,
    tags=["Campaign Management"],
)
async def campaign_initiation(
    input_data: TextCampaign,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    """
    Initiates a campaign based on text input.

    Forwards the initial campaign text to the AI service for processing and setup.
    """
    pass

#endregion




#################### CAMPAIGN MANAGEMENT #####################
#region CAMPAIGN
@route(
    request_method=app.post,
    path='/api/campaign/webhooks/test',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=None,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def webhook(
    request: Request,
    response: Response,
):
    pass
    """A test endpoint for receiving webhook calls."""
@route(
    request_method=app.post,
    path='/api/llm/campaign/parser',
    status_code=status.HTTP_200_OK,
    payload_key='request_data',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def parse_campaign_information(
    request_data: TextCampaign,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Parses campaign information from text using the AI service."""
    pass


@route(
    request_method=app.post,
    path='/api/campaign/new',
    status_code=status.HTTP_200_OK,
    payload_key='campaign_create',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def create_new_campaign(
    campaign_create: CampaignCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Creates a new campaign in the campaign service."""
    pass


@route(
    request_method=app.get,
    path='/api/campaign/personal',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_personal_campaign(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all campaigns belonging to the authenticated user."""
    pass


@route(
    request_method=app.get,
    path='/api/campaign/{campaign_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_campaign(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)    

):
    """Retrieves the details of a specific campaign by its ID."""
    pass

#START CAMPAIGN                              
@route(
    request_method=app.get,
    path="/api/campaign/status/",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_user_campaign_status(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Gets the status of all campaigns for the authenticated user."""
    pass


@route(
    request_method=app.get,
    path="/api/campaign/status/{campaign_id}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_single_campaign_status(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Gets the status of a single campaign by its ID."""
    pass


@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-content-format",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_content_update',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_content_format(
    campaign_id: uuid.UUID,
    campaign_content_update: CampaignContentUpdate,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the content format for a specific campaign."""
    pass

@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-details",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_update',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_campaign_details(
    campaign_id: uuid.UUID,
    campaign_update: CampaignDetailsUpdate,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the general details for a specific campaign."""
    pass

@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-calendly",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_contact',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_campaign_contact(
    campaign_id: uuid.UUID,
    campaign_contact: CampaignContact,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the contact (e.g., Calendly link) for a specific campaign."""
    pass

@route(
    request_method=app.delete,
    path="/api/campaign/{campaign_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def delete_campaign_by_id(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Deletes a campaign by its ID."""
    pass

@route(
    request_method=app.post,
    path="/api/campaign/companies/match",
    status_code=status.HTTP_200_OK,
    payload_key='company_matching',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_matching_companies(
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Finds companies matching the provided criteria."""
    pass

#endregion




#################### EMAIL OUTREACH #####################
#region EMAIL OUTREACH
@route(
    request_method=app.post,
    path='/api/campaign/emails/generate',
    status_code=status.HTTP_200_OK,
    payload_key="email_generation",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def generate_email(
    email_generation: EmailGeneration,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Generates a draft email for a campaign based on provided details."""
    pass


@route(
    request_method=app.put,
    path='/api/campaign/emails/draft/update',
    status_code=status.HTTP_200_OK,
    payload_key="update_draft_payload",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def update_draft_email(
    update_draft_payload: UpdateDraftEmail,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the content of a previously generated draft email."""
    pass


@route(
    request_method=app.get,
    path="/api/campaign/emails/draft/{company_id}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_draft_emails(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all draft emails for a specific company within a campaign."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/send',
    status_code=status.HTTP_200_OK,
    payload_key="email_request",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def send_email(
    email_request: SendEmailRequest,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Sends a specific email from a campaign."""
    pass


@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/outreach/auto',
    status_code=status.HTTP_200_OK,
    payload_key="email_outreach",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def start_auto_outreach(
    campaign_id: uuid.UUID,
    email_outreach: StartAutoOutreach,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Starts an automated email outreach sequence for a campaign."""
    pass


@route(
    request_method=app.put,
    path='/api/campaign/{campaign_id}/outreach/stop-auto',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def stop_auto_outreach(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Stops an ongoing automated email outreach sequence for a campaign."""
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/outreach/get-auto-status",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_auto_outreach_status(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Gets the status of the automated outreach for a specific campaign."""
    pass



@route(
    request_method=app.post,
    path="/api/campaign/emails/reply-mails/respond",
    status_code=status.HTTP_200_OK,
    payload_key="respond_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def respond_reply_email(
    respond_email: RespondEmail,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Sends a reply to a received email within a campaign."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/emails/sent-mails",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_sent_emails(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all emails sent for a specific campaign."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/emails/reply-mails",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_reply_emails(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all reply emails received for a specific company."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/emails/conversation",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_email_conversation(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the full email conversation history for a specific company."""
    pass

@route(
    request_method=app.delete,
    path="/api/campaign/emails/{sent_email_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def delete_sent_email(
    sent_email_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Deletes a specific sent email by its ID."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/emails/outreach-stats",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def email_outreach_statistic(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the email outreach statistics for a specific campaign."""
    pass

#endregion




#region LINKEDIN OUTREACH
@route(
    request_method=app.post,
    path='/api/campaign/linkedin/messages/send',
    status_code=status.HTTP_200_OK,
    payload_key="linkedin_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def send_linked_message(
    linkedin_message: SendLinkedInRequest,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Sends a LinkedIn message as part of a campaign."""
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/linkedin/message/sent-messages",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_sent_messages(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all LinkedIn messages sent for a specific campaign."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/linkedin/reply-messages",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_reply_messages(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all LinkedIn reply messages received for a specific company."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/linkedin/messages/chat",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_chat_messages(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the full LinkedIn message chat history for a specific company."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/linkedin/message/respond',
    status_code=status.HTTP_200_OK,
    payload_key="respond_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def respond_linkedin_message(
    respond_message: RespondMessage,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Sends a reply to a received LinkedIn message."""
    pass


#endregion


#################### CAMPAIGN-COMPANY MANAGEMENT #####################
#region CAMPAIGN-COMPANY
@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/companies/count",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_company_count(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Gets the total count of companies within a specific campaign."""
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/companies/{page}/{page_size}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_companies_by_campaign_id(
    campaign_id: uuid.UUID,
    page: int,
    page_size: int,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves a paginated list of companies for a specific campaign."""
    pass


@route(
    request_method=app.put,
    path="/api/campaign/companies/update-email-status",
    status_code=status.HTTP_200_OK,
    payload_key="company_email_update",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_company_email(   
    company_email_update: CompanyEmailUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the email outreach status for a specific company in a campaign."""
    pass

@route(
    request_method=app.put,
    path="/api/campaign/companies/update-linkedin-msg-status",
    status_code=status.HTTP_200_OK,
    payload_key="company_linkedin_update",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_company_linkedin_msg(
    company_linkedin_update: CompanyLinkedInMsgUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates the LinkedIn message outreach status for a specific company."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/companies/add',
    status_code=status.HTTP_200_OK,
    payload_key='company_add',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def add_company_manual(
    campaign_id: uuid.UUID,
    company_add : CompanyAddManual,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Manually adds a new company to a specific campaign."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/companies/add-multiple',
    status_code=status.HTTP_200_OK,
    payload_key="company_matching",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def add_companies(
    campaign_id: uuid.UUID,
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Adds multiple companies to a campaign based on matching criteria."""
    pass


@file_route(
    request_method=app.post,
    path="/api/campaign/{campaign_id}/companies/add/file",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],        
)
async def ingest_companies(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    """Adds companies to a campaign by ingesting data from an uploaded file."""
    pass


@route(
    request_method=app.delete,
    path="/api/campaign/companies/{company_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def delete_company_by_id(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Deletes a company from a campaign by its ID."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/representative/",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_company_representative(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the representatives for a specific company."""
    pass

@route(
    request_method=app.put,
    path="/api/campaign/companies/{company_id}/representatives/{rep_id}/humantic/update",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_humantic_profile(
    company_id: uuid.UUID,
    rep_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Triggers an update for the Humantic profile of a company representative."""
    pass

@route(
    request_method=app.get,
    path="/api/campaign/humantic/profiles",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_humantic_cached_profiles(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all cached Humantic profiles for the authenticated user."""
    pass

#endregion




#################### WEBHOOKS #####################
#region WEBHOOKS
@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/sent',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_sent_email(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    """Handles webhook notifications for sent emails."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/sent/v2',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_sent_email_v2(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    """Handles version 2 of the webhook notifications for sent emails."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/received',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_received_email(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    """Handles webhook notifications for received emails."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/tracking',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_email_tracking",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_track_email(
    webhooks_email_tracking: WebhooksEmailTracking,
    request: Request,
    response: Response
):
    """Handles webhook notifications for email tracking events (e.g., opens)."""
    pass

@route(
    request_method=app.post,
    path='/api/campaign/linkedin/webhooks/message/new',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_new_linkedin_message(
    webhooks_new_message: WebhooksNewMessage,
    request: Request,
    response: Response
):
    """Handles webhook notifications for new LinkedIn messages."""
    pass


#endregion




#region TEST ENDPOINT
@route(
    request_method=app.post,
    path='/api/nlp/similarity',
    status_code=status.HTTP_200_OK,
    payload_key="similarity_check",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"]    
)
async def get_similarity(
    similarity_check: SimilarityCheck,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Creates a new AI model. Admin only."""
    pass


#endregion





#region MODEL
#################### MODEL MANAGEMENT #####################
@route(
    request_method=app.post,
    path='/api/model/new',
    status_code=status.HTTP_200_OK,
    payload_key='model_infor',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def create_model(
    model_infor: ModelUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves a list of all available AI models."""
    pass


@route(
    request_method=app.get,
    path='/api/model',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def get_all_model(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the details of a specific AI model by its ID."""
    pass


@route(
    request_method=app.get,
    path='/api/model/{model_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def get_model(
    model_id: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    """Updates an existing AI model. Admin only."""
    pass


@route(
    request_method=app.put,
    path='/api/model/edit',
    status_code=status.HTTP_200_OK,
    payload_key='model_infor',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def update_model(
    model_infor: ModelUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.delete,
    path='/api/model/{model_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def delete_model(
    model_id: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    """Deletes a specific AI model by its ID. Admin only."""
    pass
#endregion


#region CREDIT MANAGEMENT
@route(
    request_method=app.get,
    path='/api/users/credits',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credits(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the current credit balance for the authenticated user."""
    pass

@route(
    request_method=app.get,
    path='/api/users/credits/usage',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_usage(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the credit usage history for the authenticated user."""
    pass

@route(
    request_method=app.get,
    path='/api/users/credits/transactions',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_transactions(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the credit transaction history for the authenticated user."""
    pass

@route(
    request_method=app.get,
    path='/api/users/{user_id}/credits/transactions',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_transactions(
    user_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the credit transaction history for a specific user by ID."""
    pass
#endregion


#region PAYMENT MANAGEMENT
@route(
    request_method=app.post,
    path='/api/stripe/checkout/session/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='checkout_session_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def create_checkout_session(
    checkout_session_create: CheckoutSessionCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Creates a Stripe checkout session for purchasing credits."""
    pass

@route(
    request_method=app.post,
    path='/api/stripe/checkout/session/success',
    status_code=status.HTTP_200_OK,
    payload_key='event_object',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def handle_checkout_session_success(
    event_object: EventObject,
    request: Request,
    response: Response,
):
    """Handles the webhook for a successful Stripe checkout session."""
    pass

@route(
    request_method=app.get,
    path='/api/stripe/checkout/session/{checkout_session_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def get_checkout_session(
    checkout_session_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves the details of a specific Stripe checkout session."""
    pass


#endregion

#region STRIPE PRODUCT MANAGEMENT
@route(
    request_method=app.post,
    path='/api/stripe/products/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='product_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def create_stripe_product(
    product_create: StripeProductCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Creates a new product in Stripe. Admin only."""
    pass

@route(
    request_method=app.put,
    path='/api/stripe/products/{product_id}/update',
    status_code=status.HTTP_200_OK,
    payload_key='product_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def update_stripe_product(
    product_id: str,
    product_update: StripeProductUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates an existing product in Stripe. Admin only."""
    pass

@route(
    request_method=app.delete,
    path='/api/stripe/products/{product_id}/delete',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def delete_stripe_product(
    product_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Deletes a product from Stripe. Admin only."""
    pass

@route(
    request_method=app.get,
    path='/api/stripe/products/{product_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_product(
    product_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves a specific product from Stripe."""
    pass

@route(
    request_method=app.get,
    path='/api/stripe/products',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_products(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all products from Stripe."""
    pass

@route(
    request_method=app.post,
    path='/api/stripe/prices/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='price_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def create_stripe_price(
    price_create: StripePriceCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Creates a new price for a product in Stripe. Admin only."""
    pass

@route(
    request_method=app.put,
    path='/api/stripe/prices/{price_id}/update',
    status_code=status.HTTP_200_OK,
    payload_key='price_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def update_stripe_price(
    price_id: str,
    price_update: StripePriceUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Updates an existing price in Stripe. Admin only."""
    pass

@route(
    request_method=app.get,
    path='/api/stripe/prices/{price_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_price(
    price_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves a specific price from Stripe."""
    pass

@route(
    request_method=app.get,
    path='/api/stripe/prices',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_prices(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    """Retrieves all prices from Stripe."""
    pass



#endregion

UNIPILE_SUBDOMAIN = "api15"
UNIPILE_PORT = "14599"
UNIPILE_API_KEY = "tfKWJRUc.x0E7IhKeKDegFMCAbZJARD4aTikr6tZNVpGMy3ifTSk="

def delete_ngrok_webhooks():
    """
    Deletes all existing ngrok-related webhooks from Unipile.

    This function is used to clean up old webhooks before setting new ones,
    preventing duplicate or outdated webhook URLs from being active.
    """
    url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks"

    headers = {
        "accept": "application/json",
        "X-API-KEY": UNIPILE_API_KEY
    }
    # Fetch all existing webhooks from Unipile
    response = requests.get(url=url, headers=headers)
    if response.status_code not in [200]:
        logger.info(response.text)
        raise Exception(response.text)
    
    # Iterate through the webhooks and delete the ones created for ngrok
    webhooks_list = response.json()["items"]
    for webhook in webhooks_list:
        if webhook["name"] in ["ngrok_new_email_sent", "ngrok_new_email_received", "ngrok_email_tracking", "ngrok_new_message", "ngrok_linkedin_status_update"]:
            webhook_id = webhook["id"]
            delete_url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks/{webhook_id}"
            response = requests.delete(url=delete_url, headers=headers)
            logger.info(response.text)

def set_ngrok_webhooks(base_url):
    """
    Registers all necessary webhooks with Unipile using the provided ngrok URL.

    Args:
        base_url (str): The public base URL provided by ngrok.
    """
    url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks"

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "X-API-KEY": UNIPILE_API_KEY
    }    

    # Set up 'mail_sent' webhook
    email_sent_url = base_url + '/api/campaign/emails/webhooks/sent'
    payload = {
        "source": "email",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_sent"],
        "name": "ngrok_new_email_sent",
        "request_url": email_sent_url,
        "format": "json",
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    # Set up 'mail_received' webhook
    email_received_url = base_url + '/api/campaign/emails/webhooks/received'
    payload = {
        "source": "email",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_received"],
        "name": "ngrok_new_email_received",
        "request_url": email_received_url,
        "format": "json",
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    # Set up 'email_tracking' webhook for opened mails
    email_tracking_url = base_url + '/api/campaign/emails/webhooks/tracking'
    payload = {
        "source": "email_tracking",
        "request_url": email_tracking_url,
        "name": "ngrok_email_tracking",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_opened"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    # Set up 'message_received' webhook for LinkedIn messages
    linkedin_message_url = base_url + '/api/campaign/linkedin/webhooks/message/new'
    payload = {
        "source": "messaging",
        "request_url": linkedin_message_url,
        "name": "ngrok_new_message",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["message_received"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    # Set up 'account_status' webhook for LinkedIn connection status updates
    account_status_url = base_url + '/api/users/unipile/linkedin/status-update'
    payload = {
        "source": "account_status",
        "request_url": account_status_url,
        "name": "ngrok_linkedin_status_update",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["credentials"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    # # yjymksrzqjcapsvs
    # # ngrok.set_auth_token("30xH2qJWfJ49rSdWHjAqBzfvr8j_JToxg3mnRbEYbn64fmP4") # Rakesh Old
    # # ngrok.set_auth_token("31SAfxwqZpPJK7U0KBmUmMTZSQ6_3BjSUjbSjU1LQJnkjZzda") # Rakesh New
    # # ngrok.set_auth_token("318fEYzkivlShqCFyp2JObiTUZO_5qwrPb1DBegB14mLM8FRy") # Rohit
    # # ngrok.set_auth_token("31SExEeYbUWr4xoVGZkB6XskRFh_4DodU3fFYbqAwzmzpTD9r") # Rakesh New Account
    # ngrok.set_auth_token("31SGCkSRH3LvSKdBL1ujKjui7bI_3JdnNrrgwxbougXwmq5em") # AuthToken
    # public_url = ngrok.connect(port)
    # print("ngrok tunnel \"{}\" -> \"http://localhost:{}/\"".format(public_url, port))    
    # actual_url = str(public_url).replace('"','').split(' ')[1]
    # delete_ngrok_webhooks()
    # set_ngrok_webhooks(actual_url.strip())
    # logger.info(actual_url)
    # logger.info(actual_url)
    # logger.info(actual_url)

    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment
