from schema.user_schema import Sign<PERSON>ser
from schema.token_schema import Token
from schema.role_schema import Role
from config import settings

from typing import Any
import jwt
import secrets
import time


def sign_jwt(user: SignUser):
    """
    Generates a JWT access token for a given user.

    Args:
        user (SignUser): The user information to include in the token payload.
                         It's expected to be a dictionary-like object with 'user_id' and 'role'.

    Returns:
        Token: A Token object containing the access token and its type.
    """
    print(user)
    # Prepare the payload for the JWT
    payload = {
        "user_id": user["user_id"],
        "role": user["role"],
        "exp": time.time() + settings.ACCESS_TOKEN_EXPIRE_MINUTES
    }

    # Encode the payload into a JWT access token
    access_token = jwt.encode(payload, settings.JWT_SECRET, settings.JWT_ALGORITHM)
    # Return the token in the standard format
    return token_auth(token = access_token)


def decode_jwt(token:str):
    """
    Decodes a JWT token and verifies its expiration.

    Args:
        token (str): The JWT token to decode.

    Returns:
        dict: The decoded token payload if the token is valid and not expired, otherwise an empty dictionary.
    """
    try:
        # Attempt to decode the token using the secret and algorithm from settings
        decoded_token = jwt.decode(token, settings.JWT_SECRET, algorithms=[settings.JWT_ALGORITHM])
        # Check if the token has expired
        return decoded_token if decoded_token['exp'] >= time.time() else {}
    except:
        # Return an empty dictionary if decoding fails for any reason (e.g., invalid signature, expired)
        return {}


def token_auth(token: str) -> Token:
    """
    Wraps an access token in a Token schema object.

    Args:
        token (str): The access token string.

    Returns:
        Token: A Token object with the access token and "bearer" token type.
    """
    return Token(
        access_token = token,
        token_type = "bearer"
    )


def random_password(password_length=8):
    """
    Generates a random, URL-safe password.

    Args:
        password_length (int, optional): The desired length of the password. Defaults to 8.

    Returns:
        str: A randomly generated password string.
    """
    return secrets.token_urlsafe(password_length)


def generate_request_header(token):
    """
    Generates a header for downstream service requests containing the user ID.

    Args:
        token (dict): The decoded JWT payload containing the user's ID.

    Returns:
        dict: A dictionary with the 'request-user-id' header.
    """
    return {'request-user-id': str(token['user_id'])}


def is_admin_user(token):
    """
    Checks if the user associated with the token has an admin role.

    Args:
        token (dict): The decoded JWT payload.

    Returns:
        bool: True if the user is an admin, False otherwise.
    """
    return token['role'] == int(Role.ADMIN.value)


def is_default_user(token):
    """
    Checks if the user associated with the token has a default user or admin role.

    Args:
        token (dict): The decoded JWT payload.

    Returns:
        bool: True if the user has a user or admin role, False otherwise.
    """
    return token['role'] in [int(Role.USER.value), int(Role.ADMIN.value)]
