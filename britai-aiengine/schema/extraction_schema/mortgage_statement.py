from pydantic import BaseModel, Field
from typing import Optional, List

class TransactionDetail(BaseModel):
    """Details for a single transaction within a mortgage statement period."""
    effective_date: Optional[str] = Field(None, description="The date on which the transaction officially took effect. Format as YYYY-MM-DD.")
    post_date: Optional[str] = Field(None, description="The date the transaction was recorded or posted by the lender, if different from the effective date. Format as YYYY-MM-DD.")
    description: str = Field(..., description="A text description of the transaction (e.g., 'MINT', 'MRP', 'ADV', 'DDP', 'Capitalised Interest', 'Arrears Charge').")
    debit_amount: Optional[float] = Field(None, description="The amount of money debited from the account (e.g., charges, fees, new advances).")
    credit_amount: Optional[float] = Field(None, description="The amount of money credited to the account (e.g., repayments, overpayments, refunds).")
    balance: float = Field(..., description="The outstanding principal balance of the loan after this transaction.")

class AccountStatementRecord(BaseModel):
    """A record of the mortgage statement for a single account within a specific period."""
    mortgage_account_number: str = Field(..., description="The unique identifier for this specific mortgage account (sub-account).")
    account_type_or_product_name: Optional[str] = Field(None, description="The type of account or product name associated with this mortgage (e.g., 'Together Mortgage', 'Loan', 'Main Account').")
    
    opening_balance: Optional[float] = Field(None, description="The outstanding principal balance of this account at the beginning of the statement period.")
    closing_balance: Optional[float] = Field(None, description="The outstanding principal balance of this account at the end of the statement period.")
    total_charges_for_period: Optional[float] = Field(None, description="The cumulative sum of all charges (e.g., interest, fees, advances) applied to this account during the statement period.")
    total_payments_for_period: Optional[float] = Field(None, description="The cumulative sum of all payments (e.g., repayments, overpayments) made to this account during the statement period.")
    
    current_interest_rate: Optional[float] = Field(None, description="The interest rate currently applied to this account in percentage (e.g., 6.0 for 6.0%).")
    interest_rate_effective_date: Optional[str] = Field(None, description="The date from which the current interest rate became effective. Format as YYYY-MM-DD.")
    interest_rate_type: Optional[str] = Field(None, description="The type of interest rate (e.g., 'Fixed Rate', 'Standard Variable Rate (SVR)', 'Base Mortgage Rate (BMR)', 'Tracker').")
    
    monthly_payment_amount: Optional[float] = Field(None, description="The required regular monthly payment amount for this account.")
    arrears_balance: Optional[float] = Field(None, description="The total amount of overdue payments for this account, if applicable.")
    
    transactions: List[TransactionDetail] = Field(default_factory=list, description="A list of individual financial transactions that occurred for this account within this statement period.")

class StatementPeriodRecord(BaseModel):
    """Details for a specific statement period, containing records for all associated accounts."""
    statement_period_start_date: Optional[str] = Field(None, description="The start date of the statement period. Format as YYYY-MM-DD.")
    statement_period_end_date: Optional[str] = Field(None, description="The end date of the statement period. Format as YYYY-MM-DD.")
    period_description: Optional[str] = Field(None, description="A textual description of the statement period (e.g., 'Annual Statement 2023', 'Quarter 3 2022').")
    
    account_statements: List[AccountStatementRecord] = Field(default_factory=list, description="A list of mortgage statement records, each pertaining to a distinct account (sub-account) within this period.")

class MortgageStatementData(BaseModel):
    """
    Pydantic schema for extracting comprehensive details from UK mortgage statements,
    organized by statement period and then by individual accounts (sub-accounts).
    """
    account_holder_name: Optional[str] = Field(None, description="The name of the primary account holder for these statements, typically found at the document level.")
    statement_report_date: Optional[str] = Field(None, description="The date the overall mortgage statement report was generated or extracted. Format as YYYY-MM-DD.")
    
    statement_periods: List[StatementPeriodRecord] = Field(default_factory=list, description="A list of distinct statement periods found in the document, each containing records for associated accounts.")


class StatementPeriod(BaseModel):
    """Details for a specific statement period, containing records for all associated accounts."""
    statement_period_start_date: Optional[str] = Field(None, description="The start date of the statement period. Format as YYYY-MM-DD.")
    statement_period_end_date: Optional[str] = Field(None, description="The end date of the statement period. Format as YYYY-MM-DD.")
    period_description: Optional[str] = Field(None, description="A textual description of the statement period (e.g., 'Annual Statement 2023', 'Quarter 3 2022').")