import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import {OnboardingHeaderPart, LargeImagePlaceholder} from '../../header/OnboardingHeader';
import video2 from '../../assets/page2_female_openai_v2.mp4'

const OnboardingInputUserData = () => {
  const [nickname, setNickname] = useState('');
  const [companyType, setCompanyType] = useState('Partnership');
  const [linkedinUrl, setLinkedinUrl] = useState('');
  const navigate = useNavigate();

  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  const fetchUserDataFromAPI = async (access_token) => {
    /**
     * Fetch user data from the API using the provided access token.
     * If successful, return the user data.
     * If failed, return null.
     */
    try {
      const response = await fetch(`${roboman_api}/users/me`, {
          method: 'GET',
          headers: {
              'Content-Type': 'application/json',
              'Authorization': access_token,
          },

      });

      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }
      const data = await response.json();
      // console.log(data.user_info)
      return data.user_info;
    } catch (error) {
      console.error('Error fetching user data:', error);
      return null;
    }
  };

  useEffect(() => {

    const fetchData = async () => {
      /**
       * Fetch user data from API and update state.
       * If API fetch is successful, use the data
       * If API fetch fails, load data from localStorage.
       * Save data to localStorage whenever it changes.
       */

      // Assume we have the user's email from somewhere (e.g., context, props, or localStorage)
      const access_token = localStorage.getItem('access_token')
      // Fetch data from API
      const userData = await fetchUserDataFromAPI(access_token);
      
      if (userData) {
        console.log("API fetch is successful")
        localStorage.setItem('nickname', userData.nick_name);
        localStorage.setItem('companyType', userData.company_type);
        localStorage.setItem('linkedinUrl', userData.linkedin_address);
        
        const savedNickname = localStorage.getItem('nickname');
        const savedCompanyType = localStorage.getItem('companyType');
        const savedLinkedinUrl = localStorage.getItem('linkedinUrl');

        if (savedNickname) setNickname(savedNickname);
        if (savedCompanyType) setCompanyType(savedCompanyType);
        if (savedLinkedinUrl) setLinkedinUrl(savedLinkedinUrl);

      } else {
        console.log("API fetch is fail")
        const savedNickname = localStorage.getItem('nickname');
        const savedCompanyType = localStorage.getItem('companyType');
        const savedLinkedinUrl = localStorage.getItem('linkedinUrl');

        if (savedNickname) setNickname(savedNickname);
        if (savedCompanyType) setCompanyType(savedCompanyType);
        if (savedLinkedinUrl) setLinkedinUrl(savedLinkedinUrl);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    // Save data to localStorage whenever it changes
    localStorage.setItem('nickname', nickname);
    localStorage.setItem('companyType', companyType);
    localStorage.setItem('linkedinUrl', linkedinUrl);
  }, [nickname, companyType, linkedinUrl]);

  const handlingUploadData = async () => {
    /**
     * Upload user data to the API.
     * If successful, navigate to the next page.
     * If failed, show error message.
     */
    try {
      const access_token = localStorage.getItem('access_token');
      const response = await fetch(`${roboman_api}/users/me/update-info`, {
          method: 'PUT',
          headers: {
              'Content-Type': 'application/json',
              'Authorization': access_token,
          },
          body: JSON.stringify({
            user_type: "",
            nick_name: nickname,
            company_type: companyType,
            linkedin_address: linkedinUrl,
            billing_plan: "None",
          }),
      });

      if (response.ok) {
          const data = await response.json();
          console.log(data.message);
          // navigate('/campinitselect');
          localStorage.setItem('havedata',JSON.stringify(true))
      } else {
          Swal.fire({
            title: 'Error!',
            text: 'Please check again the information or reload the page',
            icon: 'error',
            confirmButtonText: 'Back'
          });
      }
    } catch (error) {
        console.error('Error:', error);
        Swal.fire({
          title: 'Error!',
          text: 'An error occurred. Please try again.',
          icon: 'error',
          confirmButtonText: 'Back'
        });
    }
  };

  // const handlingFetchLinkedIn = async (apikey, id) => {
  //   try {
  //     const response = await fetch(`https://api.humantic.ai/v1/user-profile?apikey=${apikey}&id=${id}`);
  //     if (!response.ok) {
  //       throw new Error('Network response was not ok');
  //     }
  //     const data = await response.json();
  //     return data;
  //   } catch (error) {
  //     console.error('Error fetching LinkedIn data:', error);
  //     return { data: { status: 'error' } };
  //   }
  // };

  // const handlingCreateLinkedIn = async (apikey, id, firstname, lastname) => {
  //   try {
  //     const response = await fetch(`https://api.humantic.ai/v1/user-profile/create?apikey=${apikey}&id=${id}&firstname=${firstname}&lastname=${lastname}`);
  //     if (!response.ok) {
  //       throw new Error('Network response was not ok');
  //     }
  //     const data = await response.json();
  //     return data;
  //   } catch (error) {
  //     console.error('Error creating LinkedIn profile:', error);
  //     return { status: 'error' };
  //   }
  // };

  // const handlingLinkedIn = async () => {
  //   try {
  //     const apikey = process.env.REACT_APP_HUMANTICAI_KEY;
  //     const fetchResult = await handlingFetchLinkedIn(apikey, linkedinUrl);

  //     if (fetchResult.data && fetchResult.data.status === 'error') {
  //       console.log("linkedin fetch fail, create new acc then fetch again")
  //       const firstname = nickname.split(' ')[0];
  //       const lastname = nickname.split(' ').slice(1).join(' ') || nickname;
  //       const createResult = await handlingCreateLinkedIn(apikey, linkedinUrl, firstname, lastname);

  //       if (createResult.status === '200') {
  //         const newFetchResult = await handlingFetchLinkedIn(apikey, linkedinUrl);
  //         if (newFetchResult && newFetchResult.status === '200') {
  //           localStorage.setItem('linkedInResult', JSON.stringify(newFetchResult.results));
  //         }
  //         console.log("linkedin fetch success")
  //       }
  //     } else if (fetchResult && fetchResult.status === '200') {
  //       console.log("linkedin fetch success")
  //       localStorage.setItem('linkedInResult', JSON.stringify(fetchResult.results));
  //     }
  //   } catch (error) {
  //     console.error('Error handling LinkedIn:', error);
  //   }
  // };

  const authorizeLinkedInConnection = async () => {
    /**
     * Request authorization for LinkedIn connection.
     * If successful, redirect to the provided URL.
     * If failed, navigate to the next page.
     * If error occurs, navigate to the next page.
     */
    const access_token = localStorage.getItem('access_token');
    const headers = {
      'accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': access_token, // Replace with the actual token
    };

    try {
      const response = await fetch(`${roboman_api}/users/unipile/linkedin/auth-url`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
          success_redirect_url: "https://roboman.intuicon.ai/dashboard",
          failure_redirect_url: "https://roboman.intuicon.ai/dashboard"
        }),
      });

      if (response.ok) {
        const data = await response.json();
        // console.log(data)
        if (data.object === 'HostedAuthUrl' && data.url) {
          // Redirect to the URL provided in the response
          window.location.href = data.url;
        } else {
          // Navigate to "/campinit" if response does not contain the expected data
          console.log("Unipile not return any linkedIn URL")
          navigate('/campinitselect');
        }
      } else {
        // Handle non-OK responses
        console.log("Unipile not return any linkedIn URL")
        navigate('/campinitselect');
      }
    } catch (error) {
      console.error('Error during authorization:', error);
      navigate('/campinitselect'); // Navigate to fallback page in case of error
    }
  };

  const handleContinue = async () => {
    /**
     * Handle continue button click in UI.
     * If nickname or linkedinUrl is empty, show error message.
     * If both are filled, call handlingUploadData and authorizeLinkedInConnection.
     * If error occurs, show error message.
     */
    if ((!nickname || !linkedinUrl)||(nickname.trim() === "" || linkedinUrl.trim() === "")) {
      Swal.fire({
        title: 'Error!',
        text: 'Please fill in the blank field',
        icon: 'error',
        confirmButtonText: 'Back'
      });
    } else {
      console.log('upload data')
      await handlingUploadData();
      console.log('get linkedin data');
      await authorizeLinkedInConnection();
    }
  };

  const handleBack = () => {
    /**
     * Handle back button click in UI.
     */

    navigate('/dashboard');
  };
  
  return (
    <div className="flex flex-col min-h-screen bg-white relative">
      <OnboardingHeaderPart enableLogout={false}/>

      <div className="flex justify-center items-start flex-grow pt-10 overflow-auto bg-gray-100">
      <div className="flex flex-col lg:flex-row w-full max-w-[1200px] min-h-[600px] lg:justify-center items-center">
          <LargeImagePlaceholder videomain={video2}/>

          <div className="lg:w-[600px] lg:h-[600px] bg-gray-100 lg:mr-10">
            <div className="bg-white p-8 rounded-lg shadow-md w-full h-full">

              {/* Heading moved outside of tab content */}
              <h2 className="text-[#223F9E] font-bold text-2xl mb-10 mt-5">
                Help us to personalize your experience better on <strong> ROBOMAN </strong>
              </h2>

              <div className="relative h-[300px] overflow-hidden">
                <div className="space-y-6">
                  <div>
                    <label className="block text-gray-700 mb-2">First name</label>
                    <input
                      type="text"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={nickname}
                      onChange={(e) => setNickname(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2">What kind of company do you work for?</label>
                    <select
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={companyType}
                      onChange={(e) => setCompanyType(e.target.value)}
                    >
                      <option value="Partnership">Partnership</option>
                      <option value="Limited Liability Partnership (LLP)">Limited Liability Partnership (LLP)</option>
                      <option value="Private Limited Company (Ltd)">Private Limited Company (Ltd)</option>
                      <option value="Public Limited Company (PLC)">Public Limited Company (PLC)</option>
                      <option value="Umbrella Company">Umbrella Company</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-gray-700 mb-2">Linkedin address</label>
                    <input
                      type="url"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      value={linkedinUrl}
                      onChange={(e) => setLinkedinUrl(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-center space-x-5">
                <button
                  className="w-[100px] h-[40px] bg-gray-200 text-Black font-bold rounded-full hover:bg-gray-300 transition-colors"
                  onClick={handleBack}
                >
                  Back
                </button>
                <button
                  className="w-[100px] h-[40px] bg-[#223F9E] text-white font-bold rounded-full hover:bg-blue-700 transition-colors"
                  onClick={handleContinue}
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default OnboardingInputUserData;
