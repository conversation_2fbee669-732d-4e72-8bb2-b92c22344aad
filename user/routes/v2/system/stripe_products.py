from database.database import get_db
from routes.v2.system_error import BAD_REQUEST, USER_NOT_FOUND
from database.models import User, UserCredit, CreditTransaction, StripePrice, StripeProduct
from schema.stripe_product_schema import StripeProductCreate, StripePriceCreate, StripePriceUpdate, StripeProductUpdate
from utils.unipile_utils.unipile_client import LinkedInClient


from sqlalchemy import update
from sqlalchemy.future import select
from sqlalchemy.orm import Session
from fastapi import Header, Depends, status, APIRouter, HTTPException
from loguru import logger
import stripe

# Create an API router
router = APIRouter(
    prefix="/api",
    tags=['Stripe Products Management'],
    responses={404: {'description': 'Not found'}},
)

@router.post("/stripe/products/create", status_code=status.HTTP_201_CREATED)
async def create_stripe_product(
    product_create: StripeProductCreate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        product = stripe.Product.create(
            description=product_create.product_description,
            active=True,
            name=product_create.product_name,
            unit_label=product_create.unit_label,
        )

        new_product = StripeProduct(
            stripe_product_id = product["id"],
            product_name = product_create.product_name,
            product_description = product_create.product_description,
            product_details = product_create.product_details,
        )
        db.add(new_product)
        db.commit()
        db.refresh(new_product)
        return {
            "status": "success",
            "product": new_product
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    

@router.put("/stripe/products/{product_id}/update", status_code=status.HTTP_200_OK)
async def update_stripe_product(
    product_id: str,
    product_update: StripeProductUpdate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripeProduct).filter(StripeProduct.stripe_product_id==product_id)
        results = db.execute(query)
        product = results.scalars().first()

        updated_name = product.product_name
        updated_description = product.product_description

        if product_update.product_name is not None:
            updated_name = product_update.product_name
        if product_update.product_description is not None:
            updated_description = product_update.product_description

        stripe_product = stripe.Product.modify(
            product_id,
            name=updated_name,
            description=updated_description,
        )

        stmt = (
            update(StripeProduct)
            .where(StripeProduct.stripe_product_id==product_id)
            .values(product_name=updated_name, product_description=updated_description)
        )
        db.execute(stmt)
        db.commit()
        db.refresh(product)
        return {
            "status": "success",
            "product": product
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/stripe/products/{product_id}", status_code=status.HTTP_200_OK)
async def get_stripe_product(
    product_id: str,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripeProduct).filter(StripeProduct.stripe_product_id==product_id)
        results = db.execute(query)
        product = results.scalars().first()
        return product
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    
@router.get("/stripe/products", status_code=status.HTTP_200_OK)
async def get_stripe_products(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripeProduct)
        results = db.execute(query)
        products = results.scalars().all()
        return products
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

    
# @router.delete("/stripe/products/{product_id}/delete", status_code=status.HTTP_200_OK)
# async def delete_stripe_product(
#     product_id: str,
#     request_user_id: str = Header(None),
#     db: Session = Depends(get_db)
# ):
#     try:
#         query = select(StripeProduct).filter(StripeProduct.stripe_product_id==product_id)
#         results = db.execute(query)
#         product = results.scalars().first()
#         if product:
#             stripe.Product.delete(product_id)
#             stmt = delete(StripeProduct).where(StripeProduct.stripe_product_id==product_id)
#             db.execute(stmt)
#             db.commit()
#             return {
#                 "status": "success",
#                 "product": product
#             }
#         else:
#             raise HTTPException(
#                 status_code=404,
#                 detail="product not found"
#             )
#     except Exception as e:
#         db.rollback()
#         raise HTTPException(
#             status_code=500,
#             detail=str(e)
#         )





@router.post("/stripe/prices/create", status_code=status.HTTP_201_CREATED)
async def create_stripe_price(
    price_create: StripePriceCreate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripeProduct).filter(StripeProduct.product_id==price_create.product_id)
        results = db.execute(query)
        product = results.scalars().first()

        price = stripe.Price.create(
            product=product.stripe_product_id,
            unit_amount=int(price_create.price_amount* 100),
            currency="gbp",
            active=True,
            nickname=price_create.price_name,
        )

        new_price = StripePrice(
            stripe_price_id = price["id"],
            stripe_product_id = product.stripe_product_id,
            product_id = price_create.product_id,
            price_name = price_create.price_name,
            price_description = price_create.price_description,
            price_currency = "gbp",
            price_amount = price_create.price_amount,
        )
        db.add(new_price)
        db.commit()
        db.refresh(new_price)
        return {
            "status": "success",
            "price": new_price
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/stripe/prices/{price_id}/update", status_code=status.HTTP_200_OK)
async def update_stripe_price(
    price_id: str,
    price_update: StripePriceUpdate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripePrice).filter(StripePrice.stripe_price_id==price_id)
        results = db.execute(query)
        price = results.scalars().first()

        if price_update.price_amount is None:
            return {
                "status": "success",
                "price": price
            }
        stripe_price = stripe.Price.modify(
            price_id,
            active=True,
            unit_amount=price_update.price_amount * 100,
        )
        stmt = (
            update(StripePrice)
            .where(StripePrice.stripe_price_id==price_id)
            .values(price_amount=price_update.price_amount)
        )
        db.execute(stmt)
        db.commit()
        db.refresh(price)

        return {
            "status": "success",
            "price": price
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/stripe/prices/{price_id}", status_code=status.HTTP_200_OK)
async def get_stripe_price(
    price_id: str,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripePrice).filter(StripePrice.stripe_price_id==price_id)
        results = db.execute(query)
        price = results.scalars().first()
        return price
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/stripe/prices", status_code=status.HTTP_200_OK)
async def get_stripe_prices(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripePrice)
        results = db.execute(query)
        prices = results.scalars().all()

        returned_prices = []
        for price in prices:
            returned_prices.append(
                {
                    "price_id": price.stripe_price_id,
                    "price_name": price.price_name,
                    "price_description": price.price_description,
                    "price_currency": price.price_currency,
                    "price_amount": price.price_amount,
                    "product_id": price.product_id
                }
            )

        return prices
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )




# Export the router
stripe_products_routes = router
