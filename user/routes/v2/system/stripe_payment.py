from database.database import get_db
from routes.v2.system_error import BAD_REQUEST, USER_NOT_FOUND
from database.models import User, UserCredit, CreditTransaction, StripePrice, StripeProduct
from schema.stripe_payment_schema import CheckoutSessionCreate, CheckoutSessionObject, EventObject
from schema.stripe_product_schema import StripeProductCreate, StripePriceCreate, StripePriceUpdate, StripeProductUpdate
from utils.unipile_utils.unipile_client import LinkedInClient
from core.config import settings

from sqlalchemy import update
from sqlalchemy.future import select
from sqlalchemy.orm import Session
from fastapi import Header, Depends, status, APIRouter, HTTPException
from loguru import logger
import stripe

stripe.api_key = settings.STRIPE_SECRET_KEY

# Create an API router
router = APIRouter(
    prefix="/api",
    tags=['Stripe Payment Management'],
    responses={404: {'description': 'Not found'}},
)

@router.post("/stripe/checkout/session/create", status_code=status.HTTP_201_CREATED)
async def create_checkout_session(
    checkout_session_create: CheckoutSessionCreate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(StripePrice).filter(StripePrice.price_id==checkout_session_create.price_id)
        results = db.execute(query)
        price = results.scalars().first()

        stripe_checkout_session = stripe.checkout.Session.create(
            mode="payment",
            ui_mode="hosted",
            cancel_url=checkout_session_create.cancel_url,
            success_url=checkout_session_create.success_url,
            client_reference_id=str(request_user_id),
            # billing_address_collection="required",
            currency="gbp",
            line_items=[{"price": price.stripe_price_id, "quantity": 1}],
            adaptive_pricing={"enabled": True},
            automatic_tax={"enabled": True},
            payment_method_types=["card"],
            metadata={
                "stripe_price_id": price.stripe_price_id
            }
        )

        return CheckoutSessionObject(**stripe_checkout_session)

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/stripe/checkout/session/success", status_code=status.HTTP_200_OK)
async def handle_checkout_session_success(
    event_object: EventObject,
    db: Session = Depends(get_db)
):
    try:
        data = event_object.data
        checkout_session_object = CheckoutSessionObject(**data["object"])

        stripe_checkout_session_id = checkout_session_object.id
        user_id = checkout_session_object.client_reference_id
        amount_total = checkout_session_object.amount_total / 100
        amount_subtotal = checkout_session_object.amount_subtotal / 100
        amount_tax = 0 if checkout_session_object.total_details.get("amount_tax") is None else checkout_session_object.total_details["amount_tax"] / 100
        currency = checkout_session_object.currency
        stripe_price_id = checkout_session_object.metadata["stripe_price_id"]


        query = select(StripePrice).filter(StripePrice.stripe_price_id==stripe_price_id)
        results = db.execute(query)
        price = results.scalars().first()

        query = select(StripeProduct).filter(StripeProduct.stripe_product_id==price.stripe_product_id)
        results = db.execute(query)
        product = results.scalars().first()
        
        credit_amount = product.product_details["amount"]

        new_transaction = CreditTransaction(
            stripe_checkout_session_id = stripe_checkout_session_id,
            user_id = user_id,
            credit_amount = credit_amount,
            currency = currency,
            amount_subtotal = amount_subtotal,
            amount_tax = amount_tax,
            amount_total = amount_total,
            transaction_description = product.product_description,
        )
        db.add(new_transaction)

        stmt = (
            update(UserCredit)
            .where(UserCredit.user_id==user_id)
            .values(current_credits=UserCredit.current_credits + credit_amount, bought_credits=UserCredit.bought_credits + credit_amount)
        )
        db.execute(stmt)

        db.commit()

        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/stripe/checkout/session/{checkout_session_id}", status_code=status.HTTP_200_OK)
async def get_checkout_session(
    checkout_session_id: str,
):
    try:
        checkout_session = stripe.checkout.Session.retrieve(checkout_session_id)
        return checkout_session
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )



# Export the router
stripe_payment_routes = router