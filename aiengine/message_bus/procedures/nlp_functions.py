import json
import requests
import async<PERSON>
from typing import Dict, List
from loguru import logger
from bs4 import BeautifulSoup
from llama_index.llms.openai import OpenAI
from llama_index.program.openai import OpenAIPydanticProgram
from llama_index.core.tools import FunctionTool, ToolMetadata
from llama_index.embeddings.openai import OpenAIEmbedding

import numpy as np

from schema.ai_config_schema import EMBEDDING
from message_bus.task_publisher import publish_task
from schema.ai_config_schema import EMAIL_GENERATION_V2, RESPOND_PROMPT
from llama_config.aiservice import run_agent, embed_model
from agent.tools.email_postprocess import postprocess
from message_bus.rpc_client import R<PERSON><PERSON>lient
from schema.input_message_schema import GenerateResponseOutput
from core.config import settings


async def embed_company_info(company_info: Dict[str, list]):
    """
    Generate embeddings for company information using OpenAI's embedding model.
    
    Args:
        company_info (Dict[str, list]): Dictionary with company data where keys are field names
                                        and values are lists of text to embed
    
    Returns:
        dict: Dictionary containing embeddings for each text item, organized by field name
    """
    # Initialize embedding model with specific dimensions
    test_embed_model = OpenAIEmbedding(
        model=EMBEDDING['embedv3-small']['model'],
        dimensions=384,
        api_key=settings.OPENAI_API_KEY
    )

    # Process each field in the company info
    embeddings = {}
    for key, value in company_info.items():
        # Generate embeddings for all texts in this field
        embeddings_list = test_embed_model._get_text_embeddings(texts=value)
        # Store embeddings with their corresponding text
        embeddings[key+"_embedding"] = {}
        for i in range(len(value)):
            embeddings[key+"_embedding"][value[i]] = embeddings_list[i]

    return embeddings

async def embed_company_info_test(company_info: Dict[str, list], embedding_dim: int):
    """
    Test version of embed_company_info with configurable embedding dimensions.
    
    Args:
        company_info (Dict[str, list]): Dictionary with company data where keys are field names
                                        and values are lists of text to embed
        embedding_dim (int): Dimension size for the embeddings
    
    Returns:
        dict: Dictionary containing embeddings for each text item, organized by field name
    """
    # Initialize embedding model with custom dimensions
    test_embed_model = OpenAIEmbedding(
        model=EMBEDDING['embedv3-small']['model'],
        dimensions=embedding_dim,
        api_key=settings.OPENAI_API_KEY
    )

    # Process each field in the company info
    embeddings = {}
    for key, value in company_info.items():
        # Generate embeddings for all texts in this field
        embeddings_list = test_embed_model._get_text_embeddings(texts=value)
        # Store embeddings with their corresponding text
        embeddings[key+"_embedding"] = {}
        for i in range(len(value)):
            embeddings[key+"_embedding"][value[i]] = embeddings_list[i]

    return embeddings    

async def get_text_embedding(text_list: List[str]):
    """
    Generate embeddings for a list of text strings.
    
    Args:
        text_list (List[str]): List of text strings to embed
    
    Returns:
        dict: Dictionary containing the list of embeddings
    """
    # Initialize embedding model with fixed dimensions
    test_embed_model = OpenAIEmbedding(
        model=EMBEDDING['embedv3-small']['model'],
        dimensions=384,
        api_key=settings.OPENAI_API_KEY
    )

    # Return embeddings in a structured format
    return {
        "embeddings_list": test_embed_model._get_text_embeddings(texts=text_list)
    }

async def get_text_embedding_test(text_list: List[str], embedding_dim: int):
    """
    Test version of get_text_embedding with configurable embedding dimensions.
    
    Args:
        text_list (List[str]): List of text strings to embed
        embedding_dim (int): Dimension size for the embeddings
    
    Returns:
        dict: Dictionary containing the list of embeddings
    """
    # Initialize embedding model with custom dimensions
    test_embed_model = OpenAIEmbedding(
        model=EMBEDDING['embedv3-small']['model'],
        dimensions=embedding_dim,
        api_key=settings.OPENAI_API_KEY
    )

    # Return embeddings in a structured format
    return {
        "embeddings_list": test_embed_model._get_text_embeddings(texts=text_list)
    }