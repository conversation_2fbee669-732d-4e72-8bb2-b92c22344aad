from pydantic import BaseModel
from typing import List, Optional
import uuid


# System prompt
class InputDataCreate(BaseModel):
    request_message: str
    model_id: int = 1
    history: List[dict] = None
    conversation_id: uuid.UUID


class InputDocCreate(InputDataCreate):
    chosen_documents: Optional[List[str]] = None
    chosen_csvs: Optional[List[str]] = None


class InputImageCreate(BaseModel):
    content: str
    history: List[dict] = None
    image_id: str


class ModelCreate(BaseModel):
    model_name: str
    display_name: str 
    model_type: str = 'text completion'
    provider: str = 'OpenAI'
    reference: str = None
    max_token: int = None
    price_in: float = None
    price_out: float = None


class ModelUpdate(ModelCreate):
    model_id: int = 1


class Campaign(BaseModel):
    "Data model for a Campaign"
    campaign_name: str
    core_service: str
    unique_selling_proposition: str
    target_audience: str
    problem_solved: str
    key_benefits: List[str]
    primary_goal_of_outreach_campaign:str
    ideal_client: List[str]
    success_measurement: str
    suggest_unique_selling_point: str
    # industry: List[str]
    # location: str

class TextCampaign(BaseModel):
    input_str: str

class GenerateEmail(BaseModel):
    input_str: str

class AudioCreate(BaseModel):
    text_input: str
    is_summary: bool

class GenerateResponse(BaseModel):
    input_str: str

class GenerateResponseOutput(BaseModel):
    "Output schema model for generating response"
    sentiment: str
    emotions: List[str]
    key_takeaways: str
    response_email_subject: str
    response_email_body: str    
    explanation: str

class FollowUp(BaseModel):
    "Output schema model for parsing email outreach follow-up strategy"
    advice_for_emails: List[str]
    day_of_emails: List[int]


class HtmlToText(BaseModel):
    html_content: str

class SimilarityCheck(BaseModel):
    text1: str
    text2: str
    embedding_dim: int