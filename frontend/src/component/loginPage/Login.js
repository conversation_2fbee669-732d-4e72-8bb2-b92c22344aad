import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Swal from "sweetalert2";
import { useAuth } from "../../authorize/AuthContext";
import AuthHeader from "./AuthHeader"; // ✅ Header

const Login = () => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const navigate = useNavigate();
  const { login } = useAuth();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;

  useEffect(() => {
    const loggedIn = localStorage.getItem("isLoggedIn");
    if (loggedIn === "true") {
      navigate("/dashboard");
    }
  }, [navigate]);

  const handleLogin = async (event) => {
    event.preventDefault();
    if (!username || !password) {
      Swal.fire({
        title: "Error!",
        text: "Please input your username and password",
        icon: "error",
        confirmButtonColor: "#2563eb",
        confirmButtonText: "Back",
      });
      return;
    }

    try {
      const response = await fetch(`${roboman_api}/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: username, password }),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem("access_token", data.access_token);
        login();
        navigate("/dashboard");
      } else {
        const errorData = await response.json();
        const messages = Array.isArray(errorData.detail)
          ? errorData.detail.map((item) => item.msg).join(", ")
          : errorData.detail;

        Swal.fire({
          title: "Error!",
          text: messages,
          icon: "error",
          confirmButtonColor: "#2563eb",
          confirmButtonText: "Close",
        });
      }
    } catch (error) {
      Swal.fire({
        title: "Error!",
        text: "An error occurred. Please try again.",
        icon: "error",
        confirmButtonColor: "#2563eb",
        confirmButtonText: "Back",
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* ✅ Header */}
      <AuthHeader />

      <div className="flex flex-1">
        {/* Left Section */}
        <div className="hidden md:flex w-1/2 bg-gradient-to-br from-blue-700 to-purple-600 text-white flex-col justify-center items-center p-12">
          <h1 className="text-4xl font-bold mb-4">
            Welcome to <span className="text-yellow-400">Roboman</span>
          </h1>
          <p className="text-lg max-w-md text-center">
            Power your workflow with AI automation. Sign in to access your dashboard and tools.
          </p>
          <img
            src="https://cdn-icons-png.flaticon.com/512/906/906343.png"
            alt="Illustration"
            className="mt-10 w-72"
          />
        </div>

        {/* Right Section (Form) */}
        <div className="flex w-full md:w-1/2 justify-center items-center bg-white shadow-lg">
          <div className="w-full max-w-md p-8">
            {/* Logo + Heading */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex justify-center mb-6">
                <div className="w-16 h-16 flex items-center justify-center rounded-full bg-blue-600 overflow-hidden">
                  <img 
                    src="/roboman-icon.jpg"
                    alt="Roboman Logo"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h2 className="text-2xl font-bold mt-4">Sign In</h2>
              <p className="text-gray-500">Access your account</p>
            </div>

            {/* Login Form */}
            <form className="space-y-4" onSubmit={handleLogin}>
              <input
                type="email"
                placeholder="Email Address"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-purple-500"
              />

              <div className="text-right">
                <button
                  type="button"
                  onClick={() => navigate("/forgotpw")}
                  className="text-purple-600 text-sm hover:underline"
                >
                  Forgot password?
                </button>
              </div>

              <button
                type="submit"
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg shadow-md hover:opacity-90 transition"
              >
                Sign In
              </button>
            </form>

            {/* Divider */}
            <div className="flex items-center my-6">
              <div className="flex-1 h-px bg-gray-300"></div>
              <span className="px-4 text-gray-500 text-sm">or continue with</span>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            {/* ✅ Social Login - Circular + Horizontal */}
            <div className="flex justify-center space-x-6">
              {/* Google */}
              <button disabled
                onClick={() => {
                  const url = `${roboman_api}/auth/google`;
                  console.log("Redirecting to Google:", url);
                  window.location.href = url; // browser will handle redirect
                }}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
                >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/300/300221.png"
                  alt="Google"
                  className="w-6 h-6"
                />
              </button>



              {/* LinkedIn */}
              <button disabled
                onClick={() => (window.location.href = `${roboman_api}/login/linkedin`)}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
              >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/174/174857.png"
                  alt="LinkedIn"
                  className="w-6 h-6"
                />
              </button>

              {/* Microsoft */}
              <button disabled
                onClick={() => (window.location.href = `${roboman_api}/login/microsoft`)}
                className="w-12 h-12 flex items-center justify-center rounded-full border border-gray-300 shadow-md hover:bg-gray-100 transition"
              >
                <img
                  src="https://cdn-icons-png.flaticon.com/512/732/732221.png"
                  alt="Microsoft"
                  className="w-6 h-6"
                />
              </button>
            </div>

            {/* Redirect to Signup */}
            <p className="text-center text-gray-600 mt-6">
              Don’t have an account?{" "}
              <button
                type="button"
                onClick={() => navigate("/signup")}
                className="text-purple-600 font-semibold hover:underline"
              >
                Sign Up
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
