import React from "react";
import { SectionWrapper } from "./SectionWrapper";

const reviews = [
  {
    name: "<PERSON>",
    text: "I uploaded my mortgage PDF and BritAI’s AI found an overpayment I never knew about. The summary was clear, and paying for the full report was totally worth it!",
  },
  {
    name: "<PERSON>",
    text: "Super easy process—just uploaded my loan agreement, got a summary in minutes, and the detailed report explained everything. Highly recommended for anyone with old finance docs.",
  },
  {
    name: "<PERSON>riya S<PERSON>",
    text: "I was impressed by how fast BritAI analyzed my documents. I only paid when I wanted the full results, and the insights were detailed and actionable.",
  },
];

export function ReviewSection() {
  return (
    <SectionWrapper id="reviews" className="py-16 bg-[#f8f8f8]">
      <h2 className="text-3xl md:text-4xl font-bold text-[#3d4b3d] mb-10 text-center">
        What Our Clients Say
      </h2>
      <div className="flex flex-col md:flex-row gap-8 justify-center items-stretch">
        {reviews.map((review, idx) => (
          <div
            key={idx}
            className="bg-white rounded-xl shadow-md p-8 flex flex-col items-center max-w-sm mx-auto"
          >
            <div className="text-[#6ca16e] text-4xl mb-4">“</div>
            <p className="text-[#4d5c4d] text-lg mb-6 text-center">{review.text}</p>
            <div className="font-semibold text-[#3d4b3d]">{review.name}</div>
          </div>
        ))}
      </div>
    </SectionWrapper>
  );
}
