import boto3
from botocore.exceptions import NoCredentialsError
from botocore.client import Config
from loguru import logger


class S3ClientSingleton:
    _instance = None

    def __new__(cls, aws_access_key_id, aws_secret_access_key, region_name):
        """
        Create or return the singleton instance of S3Client.
        
        Args:
            aws_access_key_id: AWS access key ID for authentication
            aws_secret_access_key: AWS secret access key for authentication
            region_name: AWS region name for S3 operations
            
        Returns:
            S3ClientSingleton: The singleton instance
        """
        # Create a new instance if one doesn't exist
        if cls._instance is None:
            cls._instance = super(S3ClientSingleton, cls).__new__(cls)
            cls._instance._initialize(aws_access_key_id, aws_secret_access_key, region_name)
        return cls._instance


    def _initialize(self, aws_access_key_id, aws_secret_access_key, region_name):
        """
        Initialize the S3 client with AWS credentials.
        
        Args:
            aws_access_key_id: AWS access key ID for authentication
            aws_secret_access_key: AWS secret access key for authentication
            region_name: AWS region name for S3 operations
        """
        # Create a new boto3 session with provided credentials
        self.session = boto3.Session(
            aws_access_key_id=aws_access_key_id,
            aws_secret_access_key=aws_secret_access_key,
            region_name=region_name  # Ensure this is 'ap-south-1'
        )
        # Initialize S3 client with signature version configuration
        self.s3_client = self.session.client(
            's3',
            config=Config(signature_version='s3v4')
        )


    def upload_file(self, file_name, bucket_name, object_name):
        """
        Upload a file to an S3 bucket.
        
        Args:
            file_name: Path to the local file to upload
            bucket_name: Name of the S3 bucket
            object_name: S3 object name (key) to use for the uploaded file
        """
        # Upload the file to the specified S3 bucket
        self.s3_client.upload_file(file_name, bucket_name, object_name)


    def generate_url(self, bucket_name, object_key, expiration=7200):
        """
        Generate a presigned URL for an S3 object.
        
        Args:
            bucket_name: Name of the S3 bucket
            object_key: S3 object key to generate URL for
            expiration: URL expiration time in seconds (default: 2 hours)
            
        Returns:
            str or None: Presigned URL if successful, None if credentials error
        """
        try:
            # Generate a presigned URL for temporary access to the object
            response = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': bucket_name, 'Key': object_key},
                ExpiresIn=expiration
            )

            return response
        
        except NoCredentialsError:
            # Log error if AWS credentials are not available
            logger.error("Credentials not available")

            return None
