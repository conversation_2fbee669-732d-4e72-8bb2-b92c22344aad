from functools import lru_cache
import secrets

from dotenv import load_dotenv
from pydantic import AnyHttpUrl, validator
from pydantic_settings import BaseSettings
# load_dotenv(".env")

class Settings(BaseSettings):
    """
    Application configuration settings loaded from environment variables.
    
    Contains all configuration parameters for the AI engine service including:
    - Server settings
    - Security settings
    - Database connection parameters
    - Message queue settings
    - External service URLs and credentials
    """
    # Server port configuration
    SERVER_PORT: int = 8000
    
    # API and security settings
    API_V2_STR: str = ""
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # Token expiration settings (8 days)
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8

    # Cache expiration settings (24 hours)
    AIENGINE_CACHE_EXPIRE_SECONDS: int = 60 * 60 * 24

    # Redis cache configuration
    AIENGINE_CACHE_KEY: str = "message_data:json_struct"

    # CORS configuration
    BACKEND_CORS_ORIGINS: list = ["*"]

    # Project identification
    PROJECT_NAME: str

    # Database connection parameters
    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    # RabbitMQ connection parameters
    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    EXCHANGE_NAME: str
    QUEUE_NAME: str

    # JWT authentication settings
    JWT_ALGORITHM: str
    JWT_SECRET: str
    
    # OpenAI API credentials
    OPENAI_API_KEY: str

    # Service URLs for microservice communication
    CHAT_URL: str = "http://campaign-svc/api/conversation/history"
    CAMPAIGN_SERVICE_URL:str = "http://campaign-svc"    
    # CAMPAIGN_SERVICE_URL: str = "http://roboman-campaign:8000"    

    # AWS S3 credentials and configuration
    S3_ACCESS_ID: str
    S3_SECRET_KEY: str
    S3_BUCKET_NAME: str

    # Humantic AI integration settings
    HUMANTIC_AI_BASE_URL: str = "https://api.humantic.ai/v1/user-profile"
    HUMANTIC_AI_API_KEY: str
    
    class Config:
        """Configuration for the Settings class"""
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    """
    Create and cache a Settings instance to avoid reloading environment variables.
    
    Returns:
        Settings: Application configuration settings
    """
    return Settings()

# Create a singleton instance of Settings
settings = get_settings()