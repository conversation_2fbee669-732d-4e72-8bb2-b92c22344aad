import asyncio
import aio_pika
from aio_pika import ExchangeType, Message
from loguru import logger

class RabbitMQManager:
    def __init__(self, dsn: str):
        """
        Initialize a RabbitMQ manager with the given AMQP DSN.
        """
        self.dsn = dsn
        self.connection = None
        self.channel = None

    async def connect(self):
        """
        Establish connection and create a channel.
        """
        self.connection = await aio_pika.connect_robust(self.dsn)
        self.channel = await self.connection.channel()
        logger.info("[RABBITMQ] Connected successfully.")

    async def close(self):
        """
        Close the RabbitMQ connection.
        """
        if self.connection:
            await self.connection.close()
            logger.info("[RABBITMQ] Connection closed.")

    async def declare_exchange(self, name: str, exchange_type: str = "direct", durable: bool = True):
        """
        Declare an exchange with the given name and type.
        """
        exchange = await self.channel.declare_exchange(
            name, type=ExchangeType(exchange_type), durable=durable
        )
        logger.info(f"[RABBITMQ] Exchange '{name}' declared.")
        return exchange

    async def declare_queue(self, name: str, durable: bool = True):
        """
        Declare a queue with the given name.
        """
        queue = await self.channel.declare_queue(name, durable=durable)
        logger.info(f"[RABBITMQ] Queue '{name}' declared.")
        return queue

    async def bind_queue(self, queue_name: str, exchange_name: str, routing_key: str):
        """
        Bind a queue to an exchange with a routing key.
        """
        exchange = await self.channel.get_exchange(exchange_name)
        queue = await self.channel.get_queue(queue_name)
        await queue.bind(exchange, routing_key)
        logger.info(f"[RABBITMQ] Queue '{queue_name}' bound to exchange '{exchange_name}' with key '{routing_key}'.")

    async def delete_queue(self, name: str):
        """
        Delete a queue by name.
        """
        try:
            await self.channel.queue_delete(name)
            logger.info(f"[RABBITMQ] Queue '{name}' deleted.")
        except Exception as e:
            logger.warning(f"[RABBITMQ] Could not delete queue '{name}': {e}")

    async def purge_queue(self, name: str):
        """
        Purge all messages from a queue.
        """
        try:
            await self.channel.queue_purge(name)
            logger.info(f"[RABBITMQ] Queue '{name}' purged.")
        except Exception as e:
            logger.warning(f"[RABBITMQ] Could not purge queue '{name}': {e}")

    async def publish_message(self, exchange_name: str, routing_key: str, body: bytes):
        """
        Publish a message to a specific exchange with a routing key.
        """
        exchange = await self.channel.get_exchange(exchange_name)
        message = Message(body)
        await exchange.publish(message, routing_key)
        logger.info(f"[RABBITMQ] Published message to '{exchange_name}' with key '{routing_key}'.")

