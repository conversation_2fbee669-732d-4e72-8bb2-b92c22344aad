from core.config import settings
from schema.ai_config_schema import PARSING_PROMPT
from schema.input_message_schema import Campaign

from llama_index.llms.openai import OpenAI
from llama_index.program.openai import OpenAIPydanticProgram
from llama_index.core.tools import FunctionTool, ToolMetadata


def campaign_creator(input):
    """
    Parse user input to extract campaign information using LLM.
    
    Args:
        input (str): User input text describing campaign details
        
    Returns:
        Campaign: Structured campaign data object
    """
    # Initialize OpenAI model with specific parameters
    llm = OpenAI(
        model='gpt-4o-mini',
        temperature=0,
        api_key=settings.OPENAI_API_KEY
    )
    # Create program to convert unstructured text to Campaign object
    program = OpenAIPydanticProgram.from_defaults(
        output_cls=Campaign,
        verbose=False,
        llm=llm,
        prompt_template_str=PARSING_PROMPT
    )
    # Process input and return structured campaign data
    output = program(query=input)

    return output


# Create a tool wrapper for the campaign_creator function
campaign_creator_tool = FunctionTool.from_defaults(
    fn=campaign_creator,
    tool_metadata=ToolMetadata(
        name="campaign_creator",
        description=(
            f"Used to gather campaign status based conversation between User and Assistant"
        ),
    ),
)