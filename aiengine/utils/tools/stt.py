from core.config import settings

import uuid
from openai import OpenAI
from loguru import logger


def text_to_speech(object_id: uuid.UUID, text_input: str) -> bool:
    """
    Convert text to speech using OpenAI's TTS API and save as MP3 file.
    
    Args:
        object_id: Unique identifier used for the output filename
        text_input: Text content to convert to speech
        
    Returns:
        bool: True if conversion successful, False if an error occurred
    """
    # Initialize OpenAI client with API key from settings
    client = OpenAI(api_key=settings.OPENAI_API_KEY)
    try:
        # Call OpenAI's text-to-speech API with specified model and voice
        response = client.audio.speech.create(
            model="tts-1",
            voice="nova",
            input=text_input
        )
        # Save the audio response to a file named with the object_id
        response.stream_to_file(f'./{object_id}.mp3')

        return True
    except Exception as e:
        # Log any errors that occur during the API call or file saving
        logger.error(e)
        return False
