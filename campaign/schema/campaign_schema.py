from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import uuid
from pydantic.networks import EmailStr

class CampaignCreate(BaseModel):
    campaign_name: str
    core_service: str
    unique_selling_proposition: str
    target_audience: str
    problem_solved: str
    key_benefits: List[str]
    primary_goal_of_outreach_campaign:str
    ideal_client: List[str]
    success_measurement: str
    must_have_info: str
    email_format : str = "Write an email of 300 words"
    linkedin_msg_format : str = "Write a message of 100 words" 

class CampaignDetails(BaseModel):
    campaign_name: str
    core_service: str
    unique_selling_proposition: str
    target_audience: str
    problem_solved: str
    key_benefits: List[str]
    primary_goal_of_outreach_campaign:str
    ideal_client: List[str]
    success_measurement: str
    industry: List[str]
    location: str

# class CompanyMatching(BaseModel):
#     industry: List[str]
#     location: str
#     industry_similarity_threshold: Optional[float] = Field(default=0.4, ge=0.0, le=1.0)
#     location_similarity_threshold: Optional[float] = Field(default=0.4, ge=0.0, le=1.0)
#     search_size: Optional[int] = Field(default=100, ge=1, le=300)
#     industry_weight: Optional[float] = Field(default=0.5, ge=0.0, le=1.0)
#     location_weight: Optional[float] = Field(default=0.5, ge=0.0, le=1.0)

class CompanyMatching(BaseModel):
    industries: List[str]
    countries: Optional[List[str]]
    cities_or_states: Optional[List[str]]
    continents: Optional[List[str]]
    search_size: Optional[int] = Field(default=100, ge=1, le=300)

class CampaignDetailsUpdate(BaseModel):
    campaign_name: Optional[str] = None
    core_service: Optional[str] = None
    unique_selling_proposition: Optional[str] = None
    target_audience: Optional[str] = None
    problem_solved: Optional[str] = None
    key_benefits: Optional[List[str]] = None
    primary_goal_of_outreach_campaign: Optional[str] = None
    ideal_client: Optional[List[str]] = None
    success_measurement: Optional[str] = None
    must_have_info: Optional[str] = None 
    email_format: Optional[str]
    linkedin_msg_format: Optional[str]    
    class Config:
        orm_mode = True

class CampaignSendLinkedInMessage(BaseModel):
    recipient_address: str
    subject: Optional[str]
    content: str 

class CampaignSendLinkedInInvitation(BaseModel):
    recipient_address: str
    subject: Optional[str]
    content: str 


class CampaignContentUpdate(BaseModel):
    email_format: Optional[str]
    linkedin_msg_format: Optional[str]
    class Config:
        orm_mode = True

class TestScheduleJob(BaseModel):
    test_number: int
    delay_seconds: int

class CampaignContact(BaseModel):
    calendly_link: Optional[str] = None
    class Config:
        orm_mode = True