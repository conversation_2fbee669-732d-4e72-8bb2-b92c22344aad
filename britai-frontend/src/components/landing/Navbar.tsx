"use client"
import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

const navLinks = [
  { name: "Home", href: "/" },
  { name: "Mortgage Miscalculation", href: "#mortgage" },
  { name: "Plevin PPI", href: "#plevin" },
  { name: "Mis-Sold Vehicle Finance", href: "#vehicle" },
];

export function Navbar() {
  const pathname = usePathname();
  const [scrolled, setScrolled] = React.useState(false);

  React.useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 0);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={
        (scrolled
          ? "fixed z-20 top-2 left-1/2 -translate-x-1/2 w-[90%] max-w-[950px] rounded-xl bg-[#e6efe3]/80 backdrop-blur-sm shadow-lg"
          : "w-full") + " transition-all duration-300"
      }
      style={{
        background: !scrolled
          ? "linear-gradient(90deg, #e6efe3 0%, #d3e2d0 100%)"
          : undefined,
      }}
    >
      <div className='flex items-center justify-between px-8 py-4'>
        {/* Logo */}
        <Link href='/' className='flex items-center gap-2 select-none'>
          <span className='text-2xl font-bold'>
            <span className='text-[#6ca16e]'>Brit</span>
            <span className='text-[#f26c2a]'>AI</span>
          </span>
        </Link>
        {/* Nav Links */}
        <div className='flex gap-8'>
          {navLinks.map((link) => {
            // For demo, highlight Home if pathname is "/"
            const isActive =
              (link.href === "/" && pathname === "/") ||
              (link.href !== "/" &&
                pathname.includes(link.href.replace("#", "")));
            return (
              <a
                key={link.name}
                href={link.href}
                className={
                  "font-medium text-lg transition-colors duration-200" +
                  (isActive
                    ? " text-[#f26c2a]"
                    : " text-[#3d4b3d] hover:text-[#f26c2a]")
                }
                style={{
                  fontFamily: "inherit",
                }}
              >
                {link.name}
              </a>
            );
          })}
        </div>
      </div>
    </nav>
  );
}
