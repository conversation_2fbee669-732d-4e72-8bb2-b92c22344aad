<h1 style="font-size: 36px; font-family: <PERSON><PERSON>, sans-serif; text-align: center;">
  ROBOMAN - Gateway Service
</h1>

Welcome to the *Gateway Service* documentation of the *ROBOMAN* project. This document provides details on the service's architecture, features, and API endpoints.

# Table of Contents

1. [Introduction](#introduction)
    - 1.1. [Overview](#overview)
    - 1.2. [Goals](#goals)

2. [Features](#features)

3. [Code Structure](#code-structure)

4. [Technologies](#technologies)

5. [API Reference](#api-reference)

6. [Setup and Installation](#setup-and-installation)

7. [Contribution](#contribution)

# Introduction

## Overview

The Gateway Service is the central entry point for all client requests to the ROBOMAN platform. It acts as a reverse proxy, routing incoming traffic to the appropriate downstream microservices, including the User Service, Campaign Service, and AI Engine. It is responsible for handling cross-cutting concerns such as authentication, authorization, request logging, and CORS. Built with FastAPI, it provides a unified and secure API for all frontend interactions.

## Goals

The primary goals of the Gateway Service are:

- **Single Entry Point**: Provide a single, consistent API endpoint for all microservices.
- **Centralized Authentication**: Offload authentication and authorization logic from individual services.
- **Dynamic Routing**: Intelligently forward requests to the correct backend service based on the request path.
- **Enhanced Security**: Enforce security policies and manage access control at the edge of the system.
- **Simplified Client Interaction**: Abstract the complexity of the microservice architecture from the client.

# Features

- **Dynamic API Routing**: Utilizes a custom decorator (`@route`) to dynamically create and forward API endpoints to downstream services.
- **JWT-Based Authentication**: Secures endpoints by validating JWTs and decoding user information.
- **Role-Based Access Control (RBAC)**: Restricts access to certain endpoints based on user roles (e.g., user, admin).
- **Request Forwarding**: Forwards requests with appropriate headers (including user ID) to backend services using `aiohttp`.
- **File Upload Proxying**: Handles `multipart/form-data` requests and streams file uploads to the relevant service using a custom `@file_route` decorator.
- **Streaming Responses**: Supports streaming data from services back to the client, ideal for real-time applications like AI chat.
- **CORS Management**: Configures Cross-Origin Resource Sharing (CORS) policies via FastAPI middleware.
- **Health and Maintenance Endpoints**: Provides `/api/health` for health checks and `/api/maintenance` to toggle maintenance mode.

# Code Structure

<pre>
.
│   .env
│   auth.py
│   config.py
│   core.py
│   Dockerfile
│   exceptions.py
│   main.py
│   network.py
│   README.md
│   requirements.txt
│
└───schema
    ├───campaign_schemas
    │       outreach.py
    │
    ├───user_schemas
    │       credit_schemas.py
    │       stripe_payment_schema.py
    │       stripe_product_schema.py
    │
    │   chat_schema.py
    │   input_data_schema.py
    │   message_schema.py
    │   role_schema.py
    │   token_schema.py
    │   user_schema.py
</pre>

# Technologies

The Gateway Service leverages several key technologies:

- **FastAPI**: Modern, high-performance web framework for building APIs.
- **Uvicorn**: ASGI server for running FastAPI applications.
- **Pydantic**: For data validation and settings management.
- **PyJWT**: For handling JSON Web Tokens for authentication.
- **AIOHTTP**: For making asynchronous HTTP requests to downstream services.
- **Loguru**: For advanced logging.
- **Docker**: For containerization.
- **python-multipart**: For handling file uploads.

# API Reference

The Gateway service exposes endpoints from all downstream services.

## App Management
- **GET /api/health**: Returns the health status of the application.
- **POST /api/maintenance**: Sets the maintenance status of the application.
- **GET /api/maintenance**: Retrieves the current maintenance status.

## Authentication
- **POST /api/signup**: Handles user registration by forwarding credentials to the user service.
- **POST /api/login**: Authenticates a user and returns a JWT access token.
- **POST /api/users/email/forgot_password**: Initiates the password reset process by sending a reset link.
- **PUT /api/users/password/change/{forgot_token}**: Changes a user's password using a verification token.
- **POST /api/users/unipile/linkedin/auth-url**: Generates a Unipile authentication URL for connecting a LinkedIn account.
- **POST /api/users/unipile/linkedin/reconnect-auth-url**: Generates a Unipile URL to reconnect a LinkedIn account.

## Personal
- **POST /api/users/unipile/linkedin/connect**: Handles the callback from Unipile to connect a LinkedIn account.
- **POST /api/users/unipile/linkedin/reconnect**: Handles the callback from Unipile to reconnect a LinkedIn account.
- **POST /api/users/unipile/linkedin/status-update**: Receives and processes status updates for a LinkedIn connection from Unipile.
- **PUT /api/users/unipile/linkedin/disconnect**: Allows an authenticated user to disconnect their LinkedIn account.
- **PUT /api/users/me/password/change**: Allows an authenticated user to change their own password.
- **GET /api/users/me**: Retrieves the profile information for the currently authenticated user.
- **PUT /api/users/me/update-info**: Allows an authenticated user to update their personal information.

## Admin
### Elasticsearch Management
- **GET /api/campaign/elastic-search/records/{page_size}/{from_}**: Retrieves records from Elasticsearch with pagination.
- **POST /api/campaign/elastic-search/ingest**: Ingests company data from an uploaded file into Elasticsearch.
- **DELETE /api/campaign/elastic-search/remove-index**: Removes the Elasticsearch index.

### Email Senders
- **POST /api/admin/senders/add**: Adds new sender email accounts from an uploaded CSV file.
- **GET /api/admin/senders**: Retrieves a list of all configured email sender accounts.
- **DELETE /api/admin/senders/{sender_id}/delete**: Deletes a sender email account.

### General Admin
- **POST /api/nlp/faqs/upload**: Uploads a FAQs file to the NLP service.
- **GET /api/nlp/prompts/email-gen**: Retrieves email generation prompts from the AI service.
- **PUT /api/nlp/prompts/email-gen/enable**: Enables a specific email generation prompt.
- **GET /api/users**: Retrieves a list of all users (admin only).

## Conversation
- **POST /api/conversation/new**: Creates a new conversation for the authenticated user.
- **GET /api/conversation-list**: Retrieves the list of conversations for the authenticated user.
- **GET /api/conversation/{conversation_id}**: Retrieves the message history for a specific conversation.
- **POST /api/conversation/message**: Sends a message in a conversation and streams the AI-generated response.
- **POST /api/openai/text-to-speech**: Converts text to speech using the AI service.

## Campaign Management
- **POST /api/campaign/initiation**: Initiates a campaign based on text input via the AI service.
- **POST /api/llm/campaign/parser**: Parses campaign information from text using the AI service.
- **POST /api/campaign/new**: Creates a new campaign.
- **GET /api/campaign/personal**: Retrieves all campaigns belonging to the authenticated user.
- **GET /api/campaign/{campaign_id}**: Retrieves the details of a specific campaign.
- **GET /api/campaign/status/**: Gets the status of all campaigns for the authenticated user.
- **GET /api/campaign/status/{campaign_id}**: Gets the status of a single campaign.
- **PUT /api/campaign/{campaign_id}/update-content-format**: Updates the content format for a specific campaign.
- **PUT /api/campaign/{campaign_id}/update-details**: Updates the general details for a specific campaign.
- **PUT /api/campaign/{campaign_id}/update-calendly**: Updates the contact (e.g., Calendly link) for a specific campaign.
- **DELETE /api/campaign/{campaign_id}/delete**: Deletes a campaign by its ID.
- **POST /api/campaign/companies/match**: Finds companies matching the provided criteria.

## Email Outreach
- **POST /api/campaign/emails/generate**: Asynchronously generates email drafts for a list of companies.
- **PUT /api/campaign/emails/draft/update**: Updates the content of a previously generated draft email.
- **GET /api/campaign/emails/draft/{company_id}**: Retrieves all draft emails for a specific company.
- **POST /api/campaign/emails/send**: Queues emails to be sent to a list of companies.
- **POST /api/campaign/{campaign_id}/outreach/auto**: Starts an automated email outreach sequence for a campaign.
- **PUT /api/campaign/{campaign_id}/outreach/stop-auto**: Stops an ongoing automated email outreach sequence.
- **GET /api/campaign/{campaign_id}/outreach/get-auto-status**: Gets the status of the automated outreach for a campaign.
- **POST /api/campaign/emails/reply-mails/respond**: Sends a reply to a received email within a campaign.
- **GET /api