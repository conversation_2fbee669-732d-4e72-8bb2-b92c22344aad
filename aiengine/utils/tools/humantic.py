from core.config import settings 
import requests


def create_humantic_profile(rep_linkedin):
    """
    Create a new personality profile in Humantic AI using a LinkedIn profile ID.
    
    Args:
        rep_linkedin: LinkedIn profile identifier for the representative
        
    Returns:
        int: HTTP status code from the Humantic AI API response
    """
    # Construct API endpoint for profile creation
    url = f"{settings.HUMANTIC_AI_URL}/create"
    # Set up API parameters with authentication and profile ID
    params = {
        "apikey": settings.HUMANTIC_AI_API_KEY,
        "id": rep_linkedin
    }

    # Make API request to create profile
    response = requests.get(url, params=params)

    # Return status code to indicate success/failure
    return response.status_code


def fetch_humantic_profile(rep_linkedin):
    """
    Retrieve personality analysis for a LinkedIn profile from Humantic AI.
    
    Args:
        rep_linkedin: LinkedIn profile identifier for the representative
        
    Returns:
        tuple: Two strings containing formatted OCEAN and DISC personality traits
               (OCEAN = Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism)
               (DISC = Dominance, Influence, Steadiness, Compliance)
    """
    # Construct API endpoint for profile retrieval
    url = f"{settings.HUMANTIC_AI_URL}"
    # Set up API parameters with authentication and profile ID
    params = {
        "apikey": settings.HUMANTIC_AI_API_KEY,
        "id": rep_linkedin
    }

    # Make API request to fetch profile
    response = requests.get(url, params=params)
    # Parse JSON response
    profile = response.json()
    # Extract personality traits from response
    disc_info = profile['results']['personality_analysis']['summary']['disc']['label']
    ocean_info = profile['results']['personality_analysis']['summary']['ocean']['label']

    # Format and return both personality models as uppercase comma-separated strings
    return ', '.join(ocean_info).upper(), ', '.join(disc_info).upper()
