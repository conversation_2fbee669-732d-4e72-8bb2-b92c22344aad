# Use a Node 20 base image for building
FROM node:20-alpine AS builder

# Set environment variable


ARG REACT_APP_ROBOMAN_API=https://api.roboman.ai/api

ENV REACT_APP_ROBOMAN_API=https://api.roboman.ai/api

ENV REACT_APP_HUMANTICAI_KEY=chrexec_441c7b188ffb6abcbf74f65f1ef4914e
ARG REACT_APP_HUMANTICAI_KEY=chrexec_441c7b188ffb6abcbf74f65f1ef4914e
ARG REACT_APP_SIMLI_API_KEY=cj5q5cdlvka14k7nyj26qji
ARG REACT_APP_SIMLI_FACEID_FEMALE=46ac60f5-1a8d-4364-8d03-de937b17beb5

ENV REACT_APP_SIMLI_API_KEY=cj5q5cdlvka14k7nyj26qji
ENV REACT_APP_SIMLI_FACEID_FEMALE=46ac60f5-1a8d-4364-8d03-de937b17beb5

ARG REACT_APP_ROBOMAN_API=https://api.roboman.ai/api
ENV REACT_APP_ROBOMAN_API=https://api.roboman.ai/api

ARG REACT_APP_MAIN_WEBSITE=https://outreach.roboman.ai/
ENV REACT_APP_MAIN_WEBSITE=https://outreach.roboman.ai/


# ENV REACT_APP_HUMANTICAI_KEY=chrexec_441c7b188ffb6abcbf74f65f1ef4914e
# ARG REACT_APP_HUMANTICAI_KEY=chrexec_441c7b188ffb6abcbf74f65f1ef4914e
# ARG REACT_APP_SIMLI_API_KEY=cj5q5cdlvka14k7nyj26qji
# ARG REACT_APP_SIMLI_FACEID_FEMALE=46ac60f5-1a8d-4364-8d03-de937b17beb5

# ENV REACT_APP_SIMLI_API_KEY=cj5q5cdlvka14k7nyj26qji
# ENV REACT_APP_SIMLI_FACEID_FEMALE=46ac60f5-1a8d-4364-8d03-de937b17beb5

# ARG REACT_APP_ROBOMAN_API=http://localhost:8881/api
# ENV REACT_APP_ROBOMAN_API=http://localhost:8881/api

# ARG REACT_APP_MAIN_WEBSITE=http://localhost:3000/
# ENV REACT_APP_MAIN_WEBSITE=http://localhost:3000/

# Set the working directory to /app inside the container
WORKDIR /frontend

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install --global cross-env && npm ci

# Copy the rest of the application code
COPY . .

# Build the application
RUN npm run build

# Use a smaller base image for the production environment
FROM node:20-alpine AS production

# Set the working directory to /app inside the container
WORKDIR /frontend

# Copy only the build output and necessary files from the builder stage
COPY --from=builder /frontend/build ./build
COPY --from=builder /frontend/node_modules ./node_modules
COPY --from=builder /frontend/package.json ./package.json

# Install serve globally
RUN npm install -g serve

# Expose the application port
EXPOSE 3000

# Start the application
CMD ["serve", "-s", "build", "-l", "3000"]




