import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import HeaderMainPage, {newCampaign} from '../../header/HeaderMainPage';
import IconMail from '../../assets/icon_mail.svg';
import IconCampaign from '../../assets/icon_campaign.svg';
import IconHeart from '../../assets/icon_heart.svg';

const roboman_api = process.env.REACT_APP_ROBOMAN_API;
const Button = ({ onClick, className, text }) => (
  <button onClick={onClick} className={`bg-blue-800 ${className}`}>
    {text}
  </button>
);

const getCampaignsStats = async (setCampaigns) => {
  /*
  * Function to fetch campaigns data from the API and set it in local storage.
  * Returns the number of campaigns.
  * If there's an error, it returns null.
  */

  try {
    const response = await fetch(`${roboman_api}/campaign/personal`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': localStorage.getItem('access_token'), // Replace 'code' with your actual bearer token
      },
    });

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    setCampaigns(data); // Save the data into state
    // console.log('Campaign data set in local storage:', data);
    return data.length; // Return the number of campaigns
  } catch (error) {
    console.error('Error fetching campaigns:', error);
    return null; // Return 0 if there's an error
  }
};

const StatisticPanel = ({ campaignsData }) => {
  // This component render the statistics of the campaigns

  const navigate = useNavigate();
  const [emailStats, setEmailStats] = useState({ sent: 0, total: 0 });
  const [campaignsStats, setCampaignsStats] = useState(0);
  const [companyStats, setCompanyStats] = useState(0);
  const [campaigns, setCampaigns] = useState([]);

  // Function to sum the values of email_status_count
  const calculateEmailStats = (emailStatusCount) => {
    const total = Object.values(emailStatusCount).reduce((acc, count) => acc + count, 0);
    return {
      sent: emailStatusCount["sent"] || 0, // Assuming "Sent" is the sent count
      total: total,
    };
  };

  useEffect(() => {
    // Update email stats from campaignsData (example)
    if (campaignsData && campaignsData.email_status_count) {
      const emailStats = calculateEmailStats(campaignsData.email_status_count) || 0;
      setEmailStats(emailStats);
    }

    // Fetch and set campaigns data
    if (campaignsData) {
      // setCampaigns(campaignsData.num_of_campaigns || 0);
      setCampaignsStats(campaignsData.num_of_campaigns || 0);
      setCompanyStats(campaignsData.num_of_companies ||0)
      // setSubscribersStats(campaignsData.num_of_subscribers || 0);
    }
  }, [campaignsData]); // Re-run when campaignsData changes

return (
  <div className="px-4 md:px-20 lg:px-40 py-6 bg-gray-50">
    {/* Header Section */}
    <div className="py-4 md:py-8 flex flex-col md:flex-row justify-between items-center mb-6">
      <h1 className="font-bold text-2xl md:text-3xl text-gray-800">📊 Dashboard</h1>
      <Button
        onClick={() => newCampaign(navigate)}
        className="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 
                  hover:from-blue-600 hover:to-blue-700 text-white font-semibold 
                  px-6 py-2 rounded-full shadow-md hover:shadow-xl transform 
                  hover:-translate-y-0.5 transition-all duration-300"
        text={
          <>
            <span className="text-xl font-bold">+</span>
            <span>Create New Campaign</span>
          </>
        }
      />

    </div>

    {/* Stats Section */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Emails Sent */}
      <div className="flex items-center p-5 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
        <img src={IconMail} alt="Email Icon" className="w-16 h-16 mr-5" />
        <div>
          <p className="text-sm opacity-80">Email Sent</p>
          <p className="font-bold text-3xl">{`${emailStats.sent}/${emailStats.total}`}</p>
        </div>
      </div>

      {/* Campaigns */}
      <div className="flex items-center p-5 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
        <img src={IconCampaign} alt="Campaign Icon" className="w-16 h-16 mr-5" />
        <div>
          <p className="text-sm opacity-80">Campaigns</p>
          <p className="font-bold text-3xl">{campaignsStats}</p>
        </div>
      </div>

      {/* Company */}
      <div className="flex items-center p-5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300">
        <img src={IconHeart} alt="Subscribers Icon" className="w-16 h-16 mr-5" />
        <div>
          <p className="text-sm opacity-80">Company</p>
          <p className="font-bold text-3xl">{companyStats}</p>
        </div>
      </div>
    </div>
  </div>
);


};

const DonutCharts = ({ sortBy, campaignsData  }) => {
  // This component renders the donut charts for progress and campaign status

  const getChartData = (sort, campaignsDataSum) => {
    // Dummy data based on sort type
    if (sort === 'date') {
      return {
        progress: [
          { name: 'Sent', value: campaignsDataSum.email_status_count['sent'] || 0},
          { name: 'In Progress', value: campaignsDataSum.email_status_count['reviewed'] + campaignsDataSum.email_status_count['waiting to review'] || 0},
          { name: 'Not Started', value: campaignsDataSum.email_status_count['not started']},
        ],
        status: [
          { name: 'Replied', value: campaignsDataSum.email_status_count['replied']},
          { name: 'Read', value: campaignsDataSum.email_status_count['reviewed']},
          { name: 'Waiting to Review', value: campaignsDataSum.email_status_count['waiting to review']},
        ]
      };
    } else {
      return {
        progress: [
          { name: 'Sent', value: campaignsDataSum.email_status_count['sent'] || 0},
          { name: 'In Progress', value: campaignsDataSum.email_status_count['reviewed'] + campaignsDataSum.email_status_count['waiting to review'] || 0},
          { name: 'Not Started', value: campaignsDataSum.email_status_count['not started']},
        ],
        status: [
          { name: 'Replied', value: campaignsDataSum.email_status_count['replied']},
          { name: 'Read', value: campaignsDataSum.email_status_count['reviewed']},
          { name: 'Waiting to Review', value: campaignsDataSum.email_status_count['waiting to review']},
        ]
      };
    }
  };

  const data = getChartData(sortBy, campaignsData);
  const ProgressColor = ['#6ceac0', '#ea6c6c', '#0da5e9'];
  const CampaignStatusColor = ['#00b503', '#e9001c', '#ffdb20'];

return (
  <div className="py-6 md:py-10 grid grid-cols-1 md:grid-cols-2 gap-8 
                  bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-lg 
                  border border-gray-100">
    
    {[
      { title: "📊 Progress", data: data.progress, colors: ProgressColor },
      { title: "📈 Campaign Status", data: data.status, colors: CampaignStatusColor }
    ].map((chart, i) => (
      <div 
        key={i} 
        className="flex flex-col items-center w-full bg-white/60 backdrop-blur-md 
                   rounded-xl shadow-sm p-5 border border-gray-100 hover:shadow-lg 
                   transition-all duration-300"
      >
        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
          {chart.title}
        </h3>

        {/* Chart */}
        <ResponsiveContainer width="100%" height={250}>
          <PieChart>
            <Pie
              data={chart.data}
              cx="50%"
              cy="50%"
              outerRadius="80%"
              innerRadius="55%"
              dataKey="value"
              animationBegin={200}
              animationDuration={800}
            >
              {chart.data.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={chart.colors[index % chart.colors.length]} 
                />
              ))}
            </Pie>
            <Tooltip 
              contentStyle={{ 
                backgroundColor: "rgba(255,255,255,0.95)", 
                borderRadius: "8px", 
                border: "1px solid #eee" 
              }} 
            />
          </PieChart>
        </ResponsiveContainer>

        {/* Legend under chart */}
        <div className="mt-4 flex flex-wrap justify-center gap-4">
          {chart.data.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <span 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: chart.colors[index % chart.colors.length] }} 
              />
              <span className="text-gray-700">{entry.name}</span>
            </div>
          ))}
        </div>
      </div>
    ))}
  </div>
);


};

const SortByDropdown = ({ onSortChange }) => {
  // This component renders a dropdown for sorting options
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSort, setSelectedSort] = useState('Date');

  const handleSort = (sort) => {
    setSelectedSort(sort);
    setIsOpen(false);
    onSortChange(sort.toLowerCase());
  };

return (
  <div className="relative inline-block text-left w-44">
    {/* Button */}
    <button
      type="button"
      onClick={() => setIsOpen(!isOpen)}
      className="inline-flex justify-between items-center w-full 
                 rounded-xl border border-gray-300 px-4 py-2 
                 bg-white text-sm font-medium text-gray-700 shadow-sm 
                 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-400 
                 transition-all duration-200"
    >
      Sort by
      <svg
        className={`ml-2 h-4 w-4 text-gray-500 transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
      >
        <path d="M6 8l4 4 4-4" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    </button>

    {/* Dropdown Menu */}
    {isOpen && (
      <div
        className="origin-top-left absolute left-0 mt-2 w-full rounded-xl shadow-lg 
                   bg-white border border-gray-200 z-20 animate-fadeIn"
      >
        <div className="py-1" role="menu" aria-orientation="vertical">
          {["Date", "Campaign name"].map((option) => (
            <button
              key={option}
              onClick={() => {
                handleSort(option);
                setIsOpen(false);
              }}
              className="block w-full px-4 py-2 text-sm text-gray-700 
                         hover:bg-blue-50 hover:text-blue-600 
                         text-left transition-colors duration-150"
              role="menuitem"
            >
              {option}
            </button>
          ))}
        </div>
      </div>
    )}
  </div>
);


};

const ChartPanel = ({campaignsData}) => {
  // This component renders the donut charts for progress and campaign status
  const [sortBy, setSortBy] = useState('date');

  return (
    <div className="px-4 md:px-20 lg:px-40 bg-grey py-5">
      <div className="mb-4 flex justify-start">
        <SortByDropdown onSortChange={setSortBy} />
      </div>
      <DonutCharts sortBy={sortBy} campaignsData={campaignsData} />
    </div>
  );
};

const MainBody = ({ campaignsData }) => {
  //  This component renders the main body of the dashboard
  return (
    <div className="flex flex-col space-y-1 flex-grow">
      {/* Pass campaignsData to StatisticPanel and ChartPanel */}
      <StatisticPanel campaignsData={campaignsData} />
      <ChartPanel campaignsData={campaignsData} />
    </div>
  );
};

const Dashboard = () => {
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const [campaignsData, setCampaignsData] = useState({
    num_of_campaigns: 0,
    num_of_companies: 0,
    email_status_count: {}
  });

  const fetchUserDataFromAPI = async (access_token) => {
    /*
    * Function to fetch user data from the API.
    * Returns the user data or null if there's an error.
    * 
    */

    try {
      const response = await fetch(`${roboman_api}/users/me`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: access_token,
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching user data:', error);
      return null;
    }
  };

  const fetchCampaignsSummaryData = async (access_token) => {
    /*
    * Function to fetch campaigns summary data from the API.
    * Returns the campaigns summary data or null if there's an error.
    */  
    try {
      const response = await fetch(`${roboman_api}/campaign/status/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: access_token,
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        return null;
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching campaigns data:', error);
      return null;
    }
  };

  const fetchCampaignData = async () => {
    /*
    * Function to fetch campaign data and user data from the API.
    * Sets the campaigns data in local storage.
    * Returns the campaigns data or null if there's an error.
    * */
    const access_token = localStorage.getItem('access_token');
    const userData = await fetchUserDataFromAPI(access_token);

    if (userData.user_info.nick_name) {
      
      console.log('Account has data');
      // localStorage.setItem('email', userData.email);
      localStorage.setItem('nickname', userData.user_info.nick_name);
      // localStorage.setItem('companyType', userData.user_info.company_type);
      // localStorage.setItem('linkedinUrl', userData.user_info.linkedin_address);
      localStorage.setItem('havedata', JSON.stringify(true));
      if (
        userData.linkedin_connection_status === null ||
        userData.linkedin_connection_status === "NOT_CONNECTED" ||
        userData.linkedin_connection_status === "DISCONNECTED"
      ) {
        localStorage.setItem('lnk_act', JSON.stringify(false));
      } else {
        localStorage.setItem('lnk_act', JSON.stringify(true));
      }
 
    } else {
      console.log('Account has no data');
      localStorage.setItem('havedata', JSON.stringify(false));
      localStorage.setItem('lnk_act', JSON.stringify(false));
    }

    // Fetch campaigns data
    const campaignsDataSum = await fetchCampaignsSummaryData(access_token);
    if (campaignsDataSum) {
      setCampaignsData({
        num_of_campaigns: campaignsDataSum.num_of_campaigns,
        num_of_companies: campaignsDataSum.num_of_companies,
        email_status_count: campaignsDataSum.email_status_count,
      });
    }
    // console.log("data from API", campaignsData)

  };

  // This function is called when the component mounts. Fetches the campaigns data from the API and sets it in local storage.
  useEffect(() => {
    fetchCampaignData();
  }, [])

  return (
    <div className="bg-gray-50 min-h-screen flex flex-col font-sans">
      <HeaderMainPage />
      <MainBody campaignsData={campaignsData} />
    </div>
  );
};

export default Dashboard;
