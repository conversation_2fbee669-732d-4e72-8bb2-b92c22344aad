import React, { useState, useEffect } from "react";
import <PERSON><PERSON> from "sweetalert2";

// ⬇ Modernized Profile Section
const UserProfileSection = ({ onClose }) => {
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const access_token = localStorage.getItem("access_token");

  const [userData, setUserData] = useState({
    nickname: "",
    email: "",
    userType: "",
    linkedinUrl: "",
    companyType: "",
    lnk_info: null,
    unipile_id: null,
    linkedin_connection_status: null,
  });

  const [originalData, setOriginalData] = useState(null);
  const [loading, setLoading] = useState(true);

  const USER_TYPES = ["Business", "Individual"];
  const COMPANY_TYPES = [
    "Partnership",
    "Limited Liability Partnership (LLP)",
    "Private Limited Company (Ltd)",
    "Public Limited Company (PLC)",
    "Umbrella Company",
  ];

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const res = await fetch(`${roboman_api}/users/me`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: access_token,
          },
        });

        if (!res.ok) {
          Swal.fire({
            icon: "error",
            title: "No Information Found",
            text: "This account does not have any information. Please create a campaign first.",
            confirmButtonText: "Close",
          }).then(() => onClose?.());
          return;
        }

        const data = await res.json();
        const mapped = {
          nickname: data.user_info?.nick_name || "",
          email: data.email || "",
          userType: data.user_info?.user_type || "",
          linkedinUrl: data.user_info?.linkedin_address || "",
          companyType: data.user_info?.company_type || "",
          lnk_info: data.user_linkedin_info || null,
          unipile_id: data.unipile_linkedin_id || null,
          linkedin_connection_status: data.linkedin_connection_status || null,
        };

        setUserData(mapped);
        setOriginalData(mapped);
      } catch (err) {
        console.error("Fetch error:", err);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: "Unable to fetch your data. Please try again later.",
        }).then(() => onClose?.());
      } finally {
        setLoading(false);
      }
    };
    fetchUserData();
  }, []);

  const hasChanges =
    originalData && JSON.stringify(userData) !== JSON.stringify(originalData);

  // ⬇ Modern Save logic unchanged
  const handleSubmit = async () => {
    try {
      const payload = {
        nick_name: userData.nickname,
        user_type: userData.userType,
        billing_plan: "None",
        company_type: userData.companyType,
        linkedin_address: userData.linkedinUrl,
      };

      const res = await fetch(`${roboman_api}/users/me/update-info`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: access_token,
        },
        body: JSON.stringify(payload),
      });

      const result = await res.json();

      if (result.status === "success") {
        Swal.fire({
          icon: "success",
          title: "Updated Successfully",
          text: "Your account details have been updated.",
        });

        Object.entries(userData).forEach(([k, v]) => {
          localStorage.setItem(k, v);
        });

        setOriginalData(userData);
        onClose?.();
      } else {
        Swal.fire({
          icon: "error",
          title: "Update Failed",
          text: result.message || "Something went wrong. Please try again.",
        });
      }
    } catch (err) {
      console.error("Update error:", err);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Unable to update your data. Please try again later.",
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-10 text-blue-700 font-medium text-lg animate-pulse">
        Loading your profile...
      </div>
    );
  }

  // ⬇ Classy Card Layout
  return (
    <div className="max-w-xl mx-auto px-6 py-8 bg-white rounded-3xl shadow-2xl border border-blue-50
        transition-all duration-400
        flex flex-col gap-6
        ring-1 ring-blue-200/15
      ">
      <h2 className="text-3xl font-bold tracking-tight text-blue-900 mb-2 underline underline-offset-4 decoration-blue-300/60">
        Profile
      </h2>

      {/* Nickname */}
      <Field
        label="Nickname"
        value={userData.nickname}
        onChange={(val) => setUserData({ ...userData, nickname: val })}
        placeholder="Enter your nickname"
      />

      {/* Email */}
      <Field
        label="Email"
        value={userData.email}
        readOnly
        disabled
        bg="bg-gray-50"
      />

      {/* User Type */}
      <SelectField
        label="User Type"
        value={userData.userType}
        onChange={(val) => setUserData({ ...userData, userType: val })}
        options={USER_TYPES}
      />

      {/* Company Type */}
      <SelectField
        label="Company Type"
        value={userData.companyType}
        onChange={(val) => setUserData({ ...userData, companyType: val })}
        options={COMPANY_TYPES}
      />

      {/* LinkedIn URL */}
      <Field
        label="LinkedIn URL"
        value={userData.linkedinUrl}
        onChange={(val) => setUserData({ ...userData, linkedinUrl: val })}
        placeholder="https://linkedin.com/in/username"
      />

      <div className="flex justify-between items-center mt-8 gap-2">
        <button
          onClick={onClose}
          className="px-5 py-2 rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 text-gray-700 font-medium
            hover:bg-gray-400 transition shadow-sm"
        >
          Cancel
        </button>
        <button
          onClick={handleSubmit}
          disabled={!hasChanges}
          className={`px-5 py-2 rounded-lg font-bold text-white shadow transition
            ${hasChanges
              ? "bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800"
              : "bg-gray-400 cursor-not-allowed"
            }`}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

// ⬇ Modern input field with focus states
const Field = ({ label, value, onChange, placeholder, readOnly, disabled, bg }) => (
  <div className="flex flex-col gap-1">
    <label className="text-base font-semibold text-gray-700">{label}</label>
    <input
      type="text"
      value={value}
      readOnly={readOnly}
      disabled={disabled}
      onChange={(e) => onChange?.(e.target.value)}
      className={`w-full border border-gray-200 rounded-xl px-4 py-2 
        focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-300
        transition ${bg || "bg-white"} ${readOnly ? "cursor-not-allowed" : ""}`}
      placeholder={placeholder}
    />
  </div>
);

// ⬇ Modern select field
const SelectField = ({ label, value, onChange, options }) => (
  <div className="flex flex-col gap-1">
    <label className="text-base font-semibold text-gray-700">{label}</label>
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className="w-full border border-gray-200 rounded-xl px-4 py-2
        focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-blue-300 transition bg-white"
    >
      <option value="">Select</option>
      {options.map((opt) => (
        <option key={opt} value={opt}>{opt}</option>
      ))}
    </select>
  </div>
);

export default UserProfileSection;
