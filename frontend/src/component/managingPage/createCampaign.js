import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import HeaderMainPage from '../../header/HeaderMainPage';
import Swal from 'sweetalert2';
import { FaQuestionCircle } from 'react-icons/fa';
import { handleAddCompany } from './CompanyDataUtils';

const NewCampaign = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [mustHaveInfo, setMustHaveInfo] = useState('');
  const [outputFormat, setOutputFormat] = useState('');
  // const [targetIndustry, setTargetIndustry] = useState(() => {
  //   const savedTargetIndustry = localStorage.getItem('targetIndustry');
  //   return savedTargetIndustry ? JSON.parse(savedTargetIndustry) : [];
  // });
  const [targetIndustry, setTargetIndustry] = useState([]);
  // const [targetIndustry2, setTargetIndustry2] = useState('');

  const [targetRegion, setTargetRegion] = useState([]);
  const [regionInput, setRegionInput] = useState("");
  const [showPopup, setShowPopup] = useState(false);
  const [isConfirmChecked, setConfirmChecked] = useState(false);
  const [industryInput, setIndustryInput] = useState('');
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const [numberOfCustomers, setNumberOfCustomers] = useState(250);

  useEffect(() => {
    const campaignId = localStorage.getItem('campaign_id');

    if (campaignId) {
      // If campaign_id exists in localStorage, navigate to the next page
      navigate('/email-status');
    }
  }, [navigate]);

  useEffect(() => {
    // Load saved data from localStorage
    const savedName = localStorage.getItem('campaign_name');
    const savedDescription = localStorage.getItem('core_service');
    const savedMustHaveInfo = localStorage.getItem('campaignMustHaveInfo');
    const savedOutputFormat = localStorage.getItem('campaignOutputFormat');
    // const savedTargetIndustry = JSON.parse(localStorage.getItem("selectedIndustries")) || [];
    const savedLocation = JSON.parse(localStorage.getItem('selectedCountries')) || [];
    const storedIndustries = JSON.parse(localStorage.getItem("selectedIndustries")) || [];
    
    if (savedName) setName(savedName);
    if (savedDescription) setDescription(savedDescription);
    if (savedMustHaveInfo) setMustHaveInfo(savedMustHaveInfo);
    if (savedOutputFormat) setOutputFormat(savedOutputFormat);
    // if (savedTargetIndustry) setTargetIndustry2(savedTargetIndustry);
    if (savedLocation) setTargetRegion(savedLocation);
    if (storedIndustries) setTargetIndustry(storedIndustries);
  }, []);

  useEffect(() => {
    // Save targetIndustry to localStorage whenever it changes
    localStorage.setItem('selectedIndustries', JSON.stringify(targetIndustry));
    localStorage.setItem('selectedCountries', JSON.stringify(targetRegion));
  }, [targetIndustry, targetRegion]);

  const handleExit = () => {
    setShowPopup(false)
  };

  const handleTargetIndustryKeyDown = (e) => {
    // Function to handle the Enter key press for adding target industries
    if (e.key === 'Enter' && industryInput.trim() !== '') {
      setTargetIndustry([...targetIndustry, industryInput.trim()]);
      setIndustryInput('');
    }
  };

  const removeTargetIndustry = (indexToRemove) => {
    setTargetIndustry(targetIndustry.filter((_, index) => index !== indexToRemove));
  };

  const handleRegionKeyDown = (e) => {
    // Function to handle the Enter key press for adding target region
    if (e.key === 'Enter' && regionInput.trim() !== '') {
      setTargetRegion([...targetRegion, regionInput.trim()]);
      setRegionInput('');
    }
  };

  const removeRegion = (indexToRemove) => {
    setTargetRegion(targetRegion.filter((_, index) => index !== indexToRemove));
  };

  const handlingUploadCampaign = async () => {
    /*
    * Function to handle the upload of a new campaign.
    * It gathers data from localStorage and sends it to the API.
    * Displays an error message if the campaign cannot be created.
    * Returns the response from the API.
    * */
    try {
      const access_token = localStorage.getItem('access_token');
      const unique_selling_proposition = localStorage.getItem('select_usp') !== null
        ? localStorage.getItem('select_usp')
        : localStorage.getItem('unique_selling_proposition');

      // Gather all data into a variable
      // const campaignData = {
      //   campaign_name: localStorage.getItem('campaign_name'),
      //   core_service: localStorage.getItem('core_service'),
      //   unique_selling_proposition: unique_selling_proposition,
      //   target_audience: localStorage.getItem('target_audience'),
      //   problem_solved: localStorage.getItem('problem_solved'),
      //   key_benefits: JSON.parse(localStorage.getItem('key_benefits')),
      //   primary_goal_of_outreach_campaign: localStorage.getItem('primary_goal_of_outreach_campaign'),
      //   ideal_client: JSON.parse(localStorage.getItem('ideal_client')),
      //   success_measurement: localStorage.getItem('success_measurement'),
      // };

      // Log all values
      // console.log("Campaign Data being sent:", campaignData);

      const response = await fetch(`${roboman_api}/campaign/new`, {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': access_token,
        },
        body: JSON.stringify({
          campaign_name: localStorage.getItem('campaign_name'),
          core_service: localStorage.getItem('core_service'),
          unique_selling_proposition: unique_selling_proposition,
          target_audience: localStorage.getItem('target_audience'),
          problem_solved: localStorage.getItem('problem_solved'),
          key_benefits: JSON.parse(localStorage.getItem('key_benefits')),
          primary_goal_of_outreach_campaign: localStorage.getItem('primary_goal_of_outreach_campaign'),
          ideal_client: JSON.parse(localStorage.getItem('ideal_client')),
          success_measurement: localStorage.getItem('success_measurement'),
          must_have_info: localStorage.getItem('campaignMustHaveInfo') || "",
          email_format: localStorage.getItem('campaignOutputFormat') || "",
          linkedin_msg_format: "Write a message of 100 words"
          // suggest_unique_selling_point: unique_selling_proposition,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        console.log("campaign created successfully", data.status);
        await fetchAndSaveCampaignId()

      } else {
        console.error('Check again the information or reload the page')
        Swal.fire({
          icon: 'error',
          title: 'Oops...',
          text: 'There was an error creating the campaign. Please check your information or try again later.',
          confirmButtonText: 'Try Again', // Text for the button
          allowOutsideClick: false, // Prevent closing the alert by clicking outside
        });
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handlingAddCompany = async () => {
    /*
    * Function to add multiple companies to a campaign.
    * Displays an alert if the number of companies is not an integer.
    * */
    const campaignId = localStorage.getItem('campaign_id'); // Retrieve campaign_id from local storage

    if (!campaignId) {
      console.error('Campaign ID not found in local storage.');
      return;
    }

    const url = `${roboman_api}/campaign/${campaignId}/companies/add-multiple/v2`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': localStorage.getItem('access_token'), // Replace 'code' with the actual token
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        return;
      }

      const data = await response.json();
      console.log('Successfull Add Company', data);
    } catch (error) {
      console.error('Error adding companies:', error);
    }
  };

  const fetchAndSaveCampaignId = async () => {
    /*
    * Function to fetch the campaign ID from the API and save it to local storage.
    * Displays an error message if the campaign ID cannot be retrieved.
    * Returns the fetched campaign ID.
    * */
    try {
      // Make the API call
      const response = await fetch(`${roboman_api}/campaign/personal`, {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'Authorization': localStorage.getItem("access_token"), // Replace with your actual token
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);

      }

      // Parse the JSON response
      const data = await response.json();

      // Extract the campaign_id from the first item in the array
      const campaignId = data[0]?.campaign_id;

      if (campaignId) {
        // Save the campaign_id in local storage
        localStorage.setItem('campaign_id', campaignId);
        // console.log('campaign_id saved to localStorage');
      } else {
        console.error('An Error occurred while creating new campaign');
      }
    } catch (error) {
      console.error('Error fetching campaign data:', error);
    }
  };

  const handleNext = async () => {
    /*
    * Function to handle the "Next" button click.
    * Validates the number of customers and shows a warning if no required info is provided.
    * If everything is valid, it proceeds to show the popup and call the handlingUploadCampaign function.
    * Finally, it calls the handleAddCompany function.
    * Handles errors and shows an alert if any unexpected error occurs.
    */

    try {
      // Validate the number of customers
      if (numberOfCustomers < 1 || numberOfCustomers > 250) {
        Swal.fire({
          title: 'Error',
          text: 'Please select a number of prospects between 1 and 250.',
          icon: 'error',
          confirmButtonText: 'OK',
        });
        return;
      }

      // Show warning if no required info, otherwise proceed directly
      if (mustHaveInfo.trim() === '') {
        await Swal.fire({
          title: 'Caution',
          text: 'If you do not provide the required information and desired format, the mail will be initialized as default but the results may not be as expected.',
          icon: 'warning',
          confirmButtonText: 'Understood'
        });
      }

      // Common steps for both paths
      setShowPopup(true);
      await handlingUploadCampaign();
      await handleAddCompany(numberOfCustomers);

      // Commented out fetching logic, but keeping it structured
      // const campaignId = localStorage.getItem("campaign_id");
      // const data = await fetchCampaignCompanies(campaignId, 1, 10, localStorage.getItem("access_token"));
      // storeCompaniesData(data);

    } catch (error) {
      // Add error handling
      console.error('Error in handleNext:', error);
      Swal.fire({
        title: 'Error',
        text: 'An unexpected error occurred.',
        icon: 'error'
      });
    }
  };

  const handleContinue = () => {
    // This function will be called when the user clicks "Continue" in the popup
    navigate('/email-status');
  };

  const handleCheckboxChange = (event) => {
    // This function updates the state of the checkbox in UI before user go to the next page
    setConfirmChecked(event.target.checked);
  };

  // Update state setters to also save to localStorage
  const updateName = (value) => {
    // This function updates the campaign name and saves it to localStorage
    setName(value);
    localStorage.setItem('campaign_name', value);
  };

  // const updateSelectedData = (value) => {
  //   setSelectedData(value);
  //   localStorage.setItem('campaignSelectedData', value);
  // };

  const updateDescription = (value) => {
    // This function updates the description and saves it to localStorage
    setDescription(value);
    localStorage.setItem('campaignDescription', value);
  };

  const updateMustHaveInfo = (value) => {
    // This function updates the must-have information and saves it to localStorage
    setMustHaveInfo(value);
    localStorage.setItem('campaignMustHaveInfo', value);
  };

  const updateOutputFormat = (value) => {
    // This function updates the output format and saves it to localStorage
    setOutputFormat(value);
    localStorage.setItem('campaignOutputFormat', value);
  };

  const IconWithPopup = ({ message }) => {
    // This component will show the icon and the popup message on hover
    const [isHovered, setIsHovered] = useState(false);

    return (
      <div
        className="relative inline-block"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="text-lg cursor-pointer ml-1">
          <FaQuestionCircle /> {/* Replace 🔔 with FaQuestionCircle */}
        </div>
        {isHovered && (
          <div className="absolute left-1/2 transform -translate-x-1/2 mt-2 w-[300px] p-2 bg-gray-800 text-white text-sm rounded shadow-lg z-10">
            {message}
          </div>
        )}
      </div>
    );
  };

  const must_have_info = "This is the field that you will insert some sensitive information, which must be shown exactly when generating mail such as Name, Email, Telephone, Address, etc."
  const desire_output_format = "This is the field that you will define the the output format of the email with some criteria such as the length, the tone, etc."

  return (
    <div className="bg-white min-h-screen flex flex-col items-center">

      <HeaderMainPage />

      <div className="w-4/6 h-full items-start rounded-lg shadow-md my-5 mt-[30px] rounded-md">
        <div className="w-full bg-gray-100 h-[50px] border rounded-md flex items-center justify-center">
          <div className="w-full flex justify-between items-center pl-8 pr-8">
            <h2 className="text-xl font-bold">New Campaign</h2>
            <button onClick={handleExit} className="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
          </div>
        </div>

        <form className="mt-4 mr-8 ml-8 mb-4 space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-0">Name:</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => updateName(e.target.value)}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-100"
              placeholder='Input your campaign name'
            />
          </div>
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Description:</label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => updateDescription(e.target.value)}
              // required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none bg-gray-100"
              placeholder='Give some description'
            />
          </div>
          <div>
            <div className="flex items-center">
              <label htmlFor="mustHaveInfo" className="block text-sm font-medium text-gray-700 mb-1">Must have information:</label>
              <IconWithPopup message={must_have_info} />
            </div>

            <textarea
              id="mustHaveInfo"
              value={mustHaveInfo}
              onChange={(e) => updateMustHaveInfo(e.target.value)}
              // required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-24 resize-none bg-gray-100"
              placeholder='My contact number is...'
            />
          </div>
          <div>
            <div className="flex items-center">
              <label htmlFor="outputFormat" className="block text-sm font-medium text-gray-700 mb-1">Desired output format:</label>
              <IconWithPopup message={desire_output_format} />
            </div>

            <textarea
              type="text"
              id="outputFormat"
              value={outputFormat}
              onChange={(e) => updateOutputFormat(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 h-20 resize-none bg-gray-100"
              placeholder='Write an email which have length of 200 words, have professional and business tone...'
            />
          </div>

          <div>
            <label htmlFor="targetIndustry" className="block text-sm font-medium text-gray-700 mb-1">Target industry:</label>
            <div className="flex flex-wrap gap-2 p-3 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 bg-gray-100">
              {targetIndustry.map((industry, index) => (
                <span key={index} className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm flex items-center">
                  {industry}
                  <button type="button" onClick={() => removeTargetIndustry(index)} className="ml-1 text-white hover:text-gray-200">×</button>
                </span>
              ))}
              <input
                type="text"
                id="targetIndustry"
                value={industryInput}
                onChange={(e) => setIndustryInput(e.target.value)}
                onKeyDown={handleTargetIndustryKeyDown}
                className="flex-grow outline-none bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            {/* Target Region Section */}
            <div>
              <label htmlFor="targetRegion" className="block text-sm font-medium text-gray-700 mb-1">Target Region:</label>
              <div className="flex flex-wrap gap-2 p-3 border border-gray-300 rounded-md focus-within:ring-2 focus-within:ring-blue-500 bg-gray-100">
                {targetRegion.map((region, index) => (
                  <span key={index} className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm flex items-center">
                    {region}
                    <button type="button" onClick={() => removeRegion(index)} className="ml-1 text-white hover:text-gray-200">×</button>
                  </span>
                ))}
                <input
                  type="text"
                  id="targetRegion"
                  value={regionInput}
                  onChange={(e) => setRegionInput(e.target.value)}
                  onKeyDown={handleRegionKeyDown}
                  className="flex-grow outline-none bg-gray-100"
                />
              </div>
            </div>

            {/* Number of Customers Section */}
            <div>
              <label htmlFor="numberOfCustomers" className="block text-sm font-medium text-gray-700 mb-1">Number of Prospect:</label>
              <div className="flex items-center space-x-4">
                {/* Number input box */}
                <input
                  type="number"
                  id="numberOfCustomers"
                  min="1"
                  max="250"
                  value={numberOfCustomers}
                  onChange={(e) => setNumberOfCustomers(Number(e.target.value))}
                  className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-100"
                />

                {/* Quick selection buttons */}
                <button
                  type="button"
                  onClick={() => setNumberOfCustomers(10)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full border border-transparent hover:border-gray-500 transition"
                >
                  10
                </button>
                <button
                  type="button"
                  onClick={() => setNumberOfCustomers(100)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full border border-transparent hover:border-gray-500 transition"
                >
                  100
                </button>
                <button
                  type="button"
                  onClick={() => setNumberOfCustomers(250)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-full border border-transparent hover:border-gray-500 transition"
                >
                  250
                </button>
              </div>
            </div>
          </div>
        </form>

        <div className="flex justify-center mt-1 mb-4">
          <button
            className="bg-[#223F9E] text-white py-2 px-8 rounded-full hover:bg-blue-700 transition-colors duration-200"
            onClick={handleNext}
          >
            Next
          </button>
        </div>
      </div>
      {showPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
          <div className="bg-white rounded-lg shadow-lg w-3/5 max-w-2xl mt-1 max-h-[800px]">
            <div className="w-full bg-gray-50 h-[70px] border rounded-md flex items-center justify-center">
              <div className="w-full flex justify-between items-center pl-8 pr-8">
                <h2 className="text-2xl font-bold">Notification</h2>
                <button onClick={handleExit} className="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
              </div>
            </div>
            <div>
              <hr className="border-t-1 border-gray-100 mb-4 w-full" />
            </div>
            <div className="space-y-7 overflow-y-auto flex items-center justify-center flex-col mb-8">
              <p className="text-gray-700 text-left ml-8 mr-8">
                Thank you for sharing your valuable information. Your input proves crucial in refining our understanding of your target audience. Following is the detailed approach we would take to crafting and delivering marketing emails to them.
              </p>

              <div className="flex justify-center">
                <div className="bg-gray-100 p-5 rounded-lg">
                  <div className="space-y-4 text-left ml-3 mr-3">
                    <label className="flex items-center">
                      <span className="font-bold">Step 1:</span> <input type="checkbox" className="mx-2" checked /><p>Get your information</p>
                    </label>
                    <label className="flex items-center">
                      <span className="font-bold text-gray-400">Step 2:</span> <input type="checkbox" className="mx-2" checked /><p className="text-gray-400">Gather advance users' information from Linkedin</p>
                    </label>
                    <div className="flex items-center">
                      <span className="mr-1 font-bold">Step 3:</span><p>Generate email content</p>
                    </div>
                    <div className="flex items-center">
                      <span className="mr-1 font-bold">Step 4:</span><p>Send out emails</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center text-left ml-8 mr-8">
                <input type="checkbox" className="mr-2" checked={isConfirmChecked} onChange={handleCheckboxChange} />
                <span className="text-gray-700">
                  I've read and agreed all the steps that Intuicon will take to help
                  me send emails.
                </span>
              </div>

              <div className="flex justify-center space-x-6">
                <button
                  className={`px-4 py-2 rounded-full ${isConfirmChecked
                      ? 'bg-gray-200 hover:bg-gray-300 text-gray-800'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  disabled={!isConfirmChecked}
                  onClick={handleContinue}
                >
                  {/* Manual */}
                  Continue
                </button>

              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewCampaign;
