import json
from io import StringIO, BytesIO
from pydantic import BaseModel

from procedures.utils import extract_entities_from_file
from schema.extraction_schema.mortgage_statement import MortgageStatementData, StatementPeriodRecord

from core.config import settings



def mortgage_statement_extraction(file_content: BytesIO):
    instruction_mortgage_statement = """You are an advanced financial document analysis system specializing in UK mortgage statement documents. Your core objective is to meticulously extract all specified details from these documents, regardless of their varying layouts or presentation formats, to support the auditing and analysis of mortgage accounts. You must identify distinct statement periods within the document and, for each period, identify all associated mortgage accounts (including sub-accounts) and their respective details.

    **Your task is to accurately and comprehensively extract all relevant entities from the provided mortgage statement document and output them as a JSON object, strictly adhering to the `MortgageStatementData` Pydantic schema.**

    **Key Principles for Extraction:**

    1.  **Semantic Focus:** Understand that different lenders and document versions will have diverse layouts and phrasing. Your goal is to identify the *semantic meaning* of the information corresponding to the fields in the pre-defined JSON schema, rather than relying on exact wording or visual placement. Extract the underlying data even if it's expressed differently or located in unexpected sections.

    2.  **Hierarchical Extraction:**
        * **Document-Level Information:** First, identify `account_holder_name` and the overall `statement_report_date` if these apply to the entire document.
        * **Statement Periods:** Identify distinct `statement_periods` within the document. A document might cover one annual period, multiple quarterly periods, or other specific periods defined by the lender. Create a `StatementPeriodRecord` for each distinct period found. For each `StatementPeriodRecord`, extract:
            * `statement_period_start_date`
            * `statement_period_end_date`
            * `period_description` (e.g., "Annual Statement 2023", "Quarter 3 2022") if available.
        * **Account-Specific Statements (Sub-Accounts):** Within each `StatementPeriodRecord`, identify all individual mortgage `account_statements` (including sub-accounts associated with the main account holder). Create an `AccountStatementRecord` for each unique account within that period. For each `AccountStatementRecord`, extract:
            * `mortgage_account_number`: The unique identifier for this specific account.
            * `account_type_or_product_name`: The type of mortgage product or sub-account if specified.
            * `opening_balance`: The balance at the start of *this account's* period.
            * `closing_balance`: The balance at the end of *this account's* period.
            * `total_charges_for_period`: Sum of all charges for *this account* during the period.
            * `total_payments_for_period`: Sum of all payments for *this account* during the period.
            * `current_interest_rate`: The rate currently applied (e.g., "6.0%" should be `6.0`).
            * `interest_rate_effective_date`: When the `current_interest_rate` became active.
            * `interest_rate_type`: (e.g., "SVR", "Fixed", "BMR", "Tracker").
            * `monthly_payment_amount`: The required regular payment.
            * `arrears_balance`: Overdue amount for this account.
            * `transactions`: A list of `TransactionDetail` objects for *this account* within *this period*. For each `TransactionDetail`:
                * `effective_date`
                * `post_date`
                * `description`
                * `debit_amount`
                * `credit_amount`

    **Instructions for Output:**

    1.  **JSON Format:** Your final output *must* be a single, valid JSON object.
    2.  **Schema Compliance:** Adhere strictly to the `MortgageStatementData` Pydantic schema for field names, data types, and nesting.
    3.  **Handling Missing or Not Available Data:**
        * For any field that is `Optional` in the schema and whose corresponding information is **not found or explicitly stated as not applicable** in the document, you *must* set its value to `null` in the JSON output. Do not omit the field.
        * For fields marked as `required`, you *must* strive to find and extract a valid value. If a required field is genuinely unidentifiable or unequivocally absent, you should indicate this by providing a placeholder value like `0` (for numerical fields) or an empty string `""` (for string fields).
    4.  **Data Transformation:**
        * **Dates:** Convert all dates to the `YYYY-MM-DD` string format (e.g., "20/04/00" becomes "2000-04-20", "4 March 2023" becomes "2023-03-04").
        * **Monetary Values:** Extract all monetary values as pure `float` numbers, strictly without currency symbols or thousands separators (e.g., "£150.75" becomes `150.75`, "1,234.56" becomes `1234.56`).
        * **Percentages:** Convert percentages to float numbers (e.g., "6.0%" becomes `6.0`).
        * **Booleans:** Convert linguistic indicators (e.g., "Yes", "No", "Applies", "Not Applicable", "True", "False") to standard JSON booleans (`true` or `false`).
        * **Lists:** Ensure `statement_periods`, `account_statements`, and `transactions` are correctly represented as JSON arrays (`[]`). If no entities are found for a list, it should be an empty list.
    5.  **Accuracy and Precision:** Ensure that all extracted values are as accurate and precise as indicated in the source document.
    6.  **No Extraneous Content:** Your response must contain *only* the JSON object. Do not include any introductory text, conversational remarks, or markdown outside the JSON block."""

    return extract_entities_from_file(instruction_mortgage_statement, MortgageStatementData, file_content)