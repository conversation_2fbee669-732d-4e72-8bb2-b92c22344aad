import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  PolarAngleAxis,
  ResponsiveContainer,
} from "recharts";
import {
  ArrowDownToLine as Receipt,
  Calendar,
  DollarSign,
  FileText,
} from "lucide-react";

// --- MOCK DATA (unchanged) ---
const currentPlanData = {
  name: "Growth Plan",
  priceUSD: 299,
  priceINR: 25000,
  interval: "month",
  features: [
    "Up to 5 Users",
    "5,000 credits/month",
    "Unlimited Proposals auto-generated",
    "100 Voice Notes in native language",
    "Advanced campaign automation (multi-step sequences)",
    "Smart AI replies (text + voice)",
    "Detailed analytics & reporting",
  ],
};

const usageStatsData = [
  { name: "Users", used: 3, limit: 5, unit: "", fill: "#6366f1" },
  { name: "Credits", used: 3200, limit: 5000, unit: "", fill: "#4f46e5" },
  { name: "Proposals", used: 0, limit: null, unit: "", fill: "#4338ca" }, // unlimited
  { name: "Voice Notes", used: 40, limit: 100, unit: "", fill: "#3730a3" },
];

const paymentHistoryData = [
  {
    id: 1,
    date: "2025-08-01",
    description: "Pro Plan - Monthly",
    amount: 49.0,
    status: "Paid",
    invoiceUrl: "#",
  },
  {
    id: 2,
    date: "2025-07-01",
    description: "Pro Plan - Monthly",
    amount: 49.0,
    status: "Paid",
    invoiceUrl: "#",
  },
  {
    id: 3,
    date: "2025-06-01",
    description: "Pro Plan - Monthly",
    amount: 49.0,
    status: "Paid",
    invoiceUrl: "#",
  },
  {
    id: 4,
    date: "2025-05-01",
    description: "Pro Plan - Monthly",
    amount: 49.0,
    status: "Failed",
    invoiceUrl: "#",
  },
];

// --- ICONS ---
const CheckIcon = () => (
  <svg
    className="w-5 h-5 text-indigo-500"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M5 13l4 4L19 7"
    ></path>
  </svg>
);

// --- COMPONENTS ---
const CurrentPlan = ({ plan }) => (
  <div className="bg-gradient-to-br from-indigo-100/90 via-white to-indigo-50/80 p-8 rounded-3xl shadow-xl border border-indigo-200 flex flex-col min-h-[420px] transition-all duration-300">
    <h3 className="text-xl font-bold text-gray-700 mb-4 border-b pb-2 tracking-tight">
      Current Plan
    </h3>

    <div className="mb-6">
      <p className="text-2xl font-extrabold text-indigo-700">{plan.name}</p>
      <p className="mt-1 text-[1.25rem] font-semibold text-indigo-700 leading-tight">
        ${plan.priceUSD}{" "}
        <span className="text-base font-medium text-gray-500">/ {plan.interval}</span>
      </p>
      <p className="text-sm text-gray-500">
        ({`₹${plan.priceINR.toLocaleString()}`} per {plan.interval})
      </p>
    </div>

    <ul className="space-y-3 mb-6">
      {plan.features.map((feature) => (
        <li key={feature} className="flex items-center text-gray-800 font-medium">
          <CheckIcon />
          <span className="ml-3">{feature}</span>
        </li>
      ))}
    </ul>

    <button className="mt-auto w-full bg-gradient-to-tr from-indigo-600 to-indigo-400 text-white py-2.5 rounded-xl font-bold hover:brightness-110 shadow transition-all">
      Upgrade / Manage Plan
    </button>
  </div>
);

const UsageMetricCard = ({ stat }) => {
  const hasLimit = stat.limit && stat.limit > 0;
  const percentage = hasLimit ? Math.round((stat.used / stat.limit) * 100) : null;
  const data = hasLimit ? [{ name: stat.name, value: percentage }] : [];

  return (
    <div className="bg-white border border-indigo-50 rounded-2xl p-6 shadow-md flex flex-col items-center min-w-[140px]">
      {hasLimit ? (
        <div className="h-20 w-20 relative mb-2">
          <ResponsiveContainer width="100%" height="100%">
            <RadialBarChart
              innerRadius="70%"
              outerRadius="100%"
              data={data}
              startAngle={90}
              endAngle={-270}
              barSize={8}
            >
              <PolarAngleAxis type="number" domain={[0, 100]} angleAxisId={0} tick={false} />
              <RadialBar
                background
                dataKey="value"
                cornerRadius={12}
                fill={stat.fill}
              />
            </RadialBarChart>
          </ResponsiveContainer>
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-bold text-sm text-indigo-700">
            {percentage}%
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center h-20 w-20 mb-2 rounded-full bg-indigo-50 text-indigo-700 font-extrabold text-xl">
          ∞
        </div>
      )}

      <p className="font-semibold text-indigo-900">{stat.name}</p>
      <p className="text-xs text-gray-500 tracking-wide">
        {hasLimit
          ? `${stat.used.toLocaleString()}${stat.unit} / ${stat.limit.toLocaleString()}${stat.unit}`
          : "Unlimited"}
      </p>
    </div>
  );
};

// --- USAGE COMPONENT WITH VERTICAL GRID EXPANSION ---
const Usage = ({ stats }) => (
  <div className="bg-white p-8 rounded-3xl shadow-xl border border-indigo-50 transition duration-300">
    <h3 className="text-xl font-bold text-gray-700 mb-4 border-b pb-2 tracking-tight">
      Current Usage
    </h3>
    <div className="grid grid-cols-2 gap-x-8 gap-y-10 min-h-[420px] py-2">
      {stats.map((stat) => (
        <UsageMetricCard key={stat.name} stat={stat} />
      ))}
    </div>
  </div>
);

const DownloadIcon = () => (
  <svg
    className="w-5 h-5"
    fill="none"
    stroke="currentColor"
    viewBox="0 0 24 24"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0
          0l-4-4m4 4V4"
    ></path>
  </svg>
);

const PaymentHistory = ({ history }) => (
  <div className="bg-white p-8 rounded-3xl shadow-xl border border-indigo-50 transition duration-300">
    <h3 className="text-xl font-bold text-gray-700 mb-4 flex items-center gap-2 border-b pb-2 tracking-tight">
      <Receipt className="w-6 h-6 text-indigo-500" />
      Payment History
    </h3>

    {history.length > 0 ? (
      <div className="overflow-x-auto pt-2">
        <table className="min-w-full text-sm">
          <thead>
            <tr className="bg-indigo-50">
              {["Date", "Description", "Amount", "Status", "Invoice"].map(
                (heading) => (
                  <th
                    key={heading}
                    className="px-8 py-3 text-left text-xs font-semibold text-indigo-600 uppercase tracking-wider"
                  >
                    {heading}
                  </th>
                )
              )}
            </tr>
          </thead>
          <tbody>
            {history.map((item, idx) => (
              <tr
                key={item.id}
                className={`${
                  idx % 2 === 0 ? "bg-white" : "bg-indigo-50"
                } hover:bg-indigo-100 transition`}
              >
                <td className="px-8 py-3 flex items-center gap-2 text-gray-800 whitespace-nowrap">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  {item.date}
                </td>
                <td className="px-8 py-3 text-gray-700 whitespace-nowrap">
                  {item.description}
                </td>
                <td className="px-8 py-3 font-semibold flex items-center gap-1 text-gray-900 whitespace-nowrap">
                  <DollarSign className="w-4 h-4 text-green-500" />
                  ${item.amount.toFixed(2)}
                </td>
                <td className="px-8 py-3">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-bold ${
                      item.status === "Paid"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    {item.status}
                  </span>
                </td>
                <td className="px-8 py-3 text-center">
                  {item.invoiceUrl ? (
                    <a
                      href={item.invoiceUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-indigo-500 hover:text-indigo-700 flex justify-center transition"
                    >
                      <DownloadIcon className="w-5 h-5" />
                    </a>
                  ) : (
                    <span className="text-gray-400 text-xs italic">N/A</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    ) : (
      <div className="text-center py-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
        <p className="text-base font-semibold">No payment history available.</p>
      </div>
    )}
  </div>
);

// --- MAIN PAGE ---
const App = () => {
  const plan = currentPlanData;
  const usage = usageStatsData;
  const history = paymentHistoryData;

  return (
    <div className="bg-gradient-to-br from-indigo-50 via-gray-50 to-white min-h-screen py-12 px-6 md:px-12">
      <div className="max-w-7xl mx-auto space-y-12">
        <h1 className="text-3xl md:text-4xl font-extrabold text-indigo-900 mb-6 tracking-tight drop-shadow-sm">
          Billing & Subscriptions
        </h1>

        {/* --- Top Section: Current Plan & Usage --- */}
        <div className="flex flex-wrap gap-10 items-start">
          <div className="flex-1 min-w-[300px] max-w-[340px]">
            <CurrentPlan plan={plan} />
          </div>
          <div className="flex-[2] min-w-[320px] max-w-[720px]">
            <Usage stats={usage} />
          </div>
        </div>

        {/* --- Payment History --- */}
        <PaymentHistory history={history} />
      </div>
    </div>
  );
};

export default App;
