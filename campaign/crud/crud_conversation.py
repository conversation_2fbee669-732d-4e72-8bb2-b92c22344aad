from crud.crud_base import CRUDBase
from schema.conversation_chatmessage_schema import (
    ConversationBase,
    ConversationResponse,
    ConversationUpdate
)
from database.models import Conversation

from typing import Optional, List
import uuid
from sqlalchemy.future import select
from sqlalchemy.ext.asyncio import AsyncSession


class CRUDConversation(CRUDBase[Conversation, ConversationBase, ConversationUpdate]):
    async def get_by_conversationid(self, db: AsyncSession, *, conversation_id: str) -> Optional[List[Conversation]]:
        result = await db.execute(select(Conversation).filter(Conversation.conversation_id == conversation_id))
        return result.scalar().first()

    async def get_by_user_id(self, db: AsyncSession, *, usr_id: uuid.UUID) -> Optional[List[Conversation]]:
        result = await db.execute(select(Conversation).filter(Conversation.user_id == usr_id))
        return result.scalars().all()
    
    async def get_right_access_conversation(self, db: AsyncSession, *, usr_id: uuid.UUID):

        con_list = await db.execute(
            select(
                Conversation
            ).filter(
                Conversation.user_id == usr_id
            ).order_by(
                Conversation.created_at.desc()
            ).limit(40)
        )
        con_list = con_list.scalars().all()

        if not con_list:
            return []
        
        # Business Logic: do not delete any conversation, just hide from user not have access right 
        con_list_right_access = []
        for con in con_list:
            con_list_right_access.append(
                ConversationResponse(
                    conversation_name = con.conversation_name,
                    conversation_id = con.conversation_id,
                    created_at = con.created_at,
                    updated_at = con.updated_at,
                )
            )
        return con_list_right_access
    
crud_conv = CRUDConversation(Conversation)