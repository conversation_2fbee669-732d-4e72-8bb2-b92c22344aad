from functools import lru_cache
import secrets
from typing import List
import os
from dotenv import load_dotenv
from pydantic import AnyHttpUrl, validator, BaseSettings
import json


load_dotenv(".env")

try:
    # Attempt to load user data from JSON file
    with open("./database/system_data/user.json", 'r') as file:
        user_dict = json.load(file)
except:
    # Set user_dict to None if file doesn't exist or can't be read
    user_dict = None

class Settings(BaseSettings):
    # Server configuration
    SERVER_PORT: int = 8000
    
    # API version prefix
    API_V2_STR: str = ""
    
    # Security settings
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # Token expiration settings
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8

    # Cache expiration settings
    # 60 seconds * 60 minutes * 6 hours = 6 hours
    KEY_CACHE_EXPIRE_SECONDS: int = 60 * 60 * 6

    # CORS configuration
    BACKEND_CORS_ORIGINS: list = ["*"]
    
    # Redis cache keys
    AIENGINE_CACHE_KEY: str = "message_data:json_struct"
    CHAT_CACHE_KEY: str = "usage_data:json_struct"

    # Project identification
    PROJECT_NAME: str

    # Database connection parameters
    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    # RabbitMQ configuration
    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    EXCHANGE_NAME: str
    QUEUE_NAME: str

    # Mailjet API credentials
    MJ_APIKEY_PUBLIC: str 
    MJ_APIKEY_PRIVATE: str

    # JWT authentication settings
    JWT_SECRET: str
    JWT_ALGORITHM: str
    TIMEOUT: int = 99999999
    
    # Service URLs for Kubernetes deployment
    AI_ENGINE_URL = "http://aiengine-svc/api/llm/stream/default"

    # AWS service URLs
    USER_SERVICE_URL = "http://user-svc"
    AI_SERVICE_URL = "http://aiengine-svc"

    # Local development URLs (commented out)
    # USER_SERVICE_URL = "http://roboman-user:8000"
    # AI_SERVICE_URL = "http://roboman-aiservice:8000"  

    # Elasticsearch configuration
    ES_PASSWORD : str
    ES_USERNAME : str
    ES_HOST : str
    ES_INDEX_NAME: str = "roboman-test-index"

    # Chrome driver for web scraping
    CHROME_DRIVER_HOST = "chrome-driver.infrastructure.svc.cluster.local"
    CHROME_DRIVER_PORT = "4444"

    # AWS S3 configuration
    S3_ACCESS_ID : str
    S3_SECRET_KEY : str
    S3_BUCKET_NAME : str = 'roboman-elastic-search'    

    # Unipile API configuration
    UNIPILE_SUBDOMAIN : str
    UNIPILE_PORT : str
    UNIPILE_API_KEY : str

    # Humantic AI configuration
    HUMANTIC_AI_API_KEY: str
    HUMANTIC_AI_BASE_URL: str
    
    class Config:
        """Configuration for environment variables loading"""
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    """
    Create and cache a Settings instance to avoid reloading environment variables.
    
    Returns:
        Settings: Application configuration settings
    """
    return Settings()

# Create a singleton instance of Settings
settings = get_settings()
