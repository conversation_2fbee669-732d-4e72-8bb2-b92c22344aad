from loguru import logger
from sqlalchemy import update, delete
from sqlalchemy.future import select
import asyncio

from database.database import SessionFactory
from database.models import Draft, Company
from aio_pika import IncomingMessage
from schema.outreach_schema import CreateDraftEmail
from message_bus.task_publisher import publish_task
from shared_state import app_state

async def create_draft_emails(
    draft_type: int,
    company_id: str,
    to_address: str,
    to_name: str,
    subject: str,
    body: str,
    body_plain: str,
    day_to_send: int = 0,
    from_address: str = None,
    from_name: str = None,    
) -> str:
    db = SessionFactory()
    async with db.begin():
        try:
            query = select(Draft).filter(Draft.company_id==company_id, Draft.draft_type==draft_type)
            results = await db.execute(query)
            existing_draft = results.scalars().first()

            if existing_draft is not None:
                if existing_draft.email_sent == False:
                    stmt = (
                        update(Draft)
                        .filter(Draft.company_id==company_id, Draft.draft_type==draft_type)
                        .values(
                            to_address = to_address,
                            to_name = to_name,                        
                            subject = subject,
                            body = body,
                            body_plain = body_plain                        
                        )
                    )
                    await db.execute(stmt)

                    stmt = update(Company).where(Company.company_id==company_id).values(
                        email_confirmation_status_id=2,
                        content_subject = subject,
                        content = body
                    )                
                    await db.execute(stmt)

                    await db.commit()
                    return "REGENERATED"
                else:
                    return "FAILED"
            else:
                new_draft = Draft(
                    draft_type = draft_type,
                    company_id = company_id,
                    to_address = to_address,
                    to_name = to_name,
                    subject = subject,
                    body = body,
                    body_plain = body_plain,
                    day_to_send = day_to_send
                )
                db.add(new_draft)

                stmt = update(Company).where(Company.company_id==company_id).values(
                    email_confirmation_status_id=2,
                    content_subject = subject,
                    content = body
                )           
                await db.execute(stmt)
                                
                await db.commit()
                return "GENERATED"
        except Exception as e:
            logger.error(str(e))
            return "FAILED"
        
async def auto_send_email(company_id: str):
    db = SessionFactory()
    async with db.begin():
        try:
            query = (
                select(Draft)
                .filter(Draft.company_id==company_id, Draft.email_sent==False)
                .order_by(Draft.day_to_send.asc())
            )
            result = await db.execute(query)
            drafts = result.scalars().all()
            if len(drafts) > 0:
                draft = drafts[0]
                message = {
                    "name_from": "Robert Mandev",
                    "draft_id": str(draft.draft_id),
                    "follow_up": True
                }            
                await publish_task(task_type="send_email_v2", message_body=message, connection_pool=app_state["rabbitmq_conn_pool"]) 
                               
                return {
                    "draft_id": str(draft.draft_id)
                }
            else:
                return None
        except Exception as e:
            logger.error(str(e))
            return None