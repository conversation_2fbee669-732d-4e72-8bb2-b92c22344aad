from database.database import POSTGRES_DATABASEURL

from sqlalchemy import create_engine, text
from alembic.config import Config
from alembic import command
from loguru import logger
import os


def run_migration() -> None:
    """
    Execute the complete database migration process.
    
    This function orchestrates the entire migration workflow by:
    1. Dropping existing alembic version table
    2. Removing old migration version files
    3. Enabling the pgvector extension
    4. Creating a new migration revision
    5. Upgrading the database to the latest version
    """
    drop_alembic_table()

    remove_migration_versions()

    enable_vector_extension()
    
    alembic_cfg = Config("./alembic.ini")
    command.revision(alembic_cfg, message="ROBOMAN AI Engine DB Migration", autogenerate=True)
    command.upgrade(alembic_cfg, "head")
    
    logger.success("AI Engine DB Migrated")


def enable_vector_extension() -> None:
    """
    Enable the pgvector extension in PostgreSQL database.
    
    Creates the vector extension if it doesn't already exist,
    which is required for storing and querying vector embeddings.
    """
    logger.info('Enabling pgvector extension.....')
    try:
        engine = create_engine(POSTGRES_DATABASEURL)
        with engine.connect() as connection:
            connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            connection.commit()
        logger.success('Create vector extension success!')
    except Exception as e:
        logger.error('Create vector extension failed!')
        logger.error(e)    

def drop_alembic_table() -> None:
    """
    Drop the alembic_version table from the database.
    
    This allows for a clean migration by removing the table that
    tracks which migrations have been applied.
    """
    logger.info('Dropping alembic table.....')
    try:
        engine = create_engine(POSTGRES_DATABASEURL)
        with engine.connect() as connection:
            connection.execute(text("DROP TABLE alembic_version"))
            connection.commit()
        logger.success('Drop success!')
    except Exception as e:
        logger.error('Delete failed!')
        logger.error(e)


def remove_migration_versions() -> None:
    """
    Remove all migration version files from the versions directory.
    
    Deletes all files except __init__.py from the migrations/versions folder
    to ensure a clean slate for generating new migrations.
    """
    folder_path = './migrations/versions'
    files = os.listdir(folder_path)
    logger.info(files)
    migration_files = [file for file in files if file != '__init__.py']
    logger.info(migration_files)
    if migration_files:
        logger.info('True')
        for migration_file in migration_files:
            migration_file_path = f'{folder_path}/{migration_file}'
            os.remove(migration_file_path)
            logger.info(f"Removed: {migration_file_path}")
    else:
        logger.info('False')
