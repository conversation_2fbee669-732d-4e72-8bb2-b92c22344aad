services:  
  roboman-servicebus:
    container_name: roboman-servicebus
    image: roboman-servicebus:latest
    build:
      context: ./rabbitmq_volumes
      dockerfile: Dockerfile
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS:-admin}
    ports:
      - 5673:5672
      - 15673:15672
    restart: unless-stopped
    volumes:
      - /absolute/path/to/rabbitmq_volumes:/var/lib/rabbitmq   
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      retries: 5
      timeout: 5s

  brit-aiengine-svc:
    container_name: brit-aiengine-svc
    image: brit-aiengine-svc:latest
    command: sh -c "python main.py"
    build:
      context: ./britai-aiengine
      dockerfile: Dockerfile
    env_file:
        - ./britai-aiengine/.env
    volumes:
      - ./britai-aiengine:/app
    ports:
      - 8886:8000
    depends_on:
      roboman-servicebus:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/api/health || exit 1"]
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 120s

  brit-aiengine-worker:
    container_name: brit-aiengine-worker
    image: brit-aiengine-svc:latest
    command: sh -c "celery -A celery_app.celery_worker worker --loglevel=info --concurrency=20 --pool=threads"
    build:
      context: ./britai-aiengine
      dockerfile: Dockerfile
    env_file:
      - ./britai-aiengine/.env
    volumes:
      - ./britai-aiengine:/app
    depends_on:
      brit-aiengine-svc:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pgrep -f 'celery' || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  flower:
    image: mher/flower
    container_name: celery-flower
    command: celery --broker=******************************************/ flower
    ports:
      - 5555:5555
    depends_on:
      - brit-aiengine-worker