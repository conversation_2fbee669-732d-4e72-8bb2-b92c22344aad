from fastapi import <PERSON><PERSON><PERSON>
from loguru import logger
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Security, FastAPI, status, Request, Response
from fastapi.security.api_key import APIKeyHeader
import aiohttp
import asyncio
import uvicorn
import asyncio
from contextlib import asynccontextmanager

from core.config import settings
from routes.entity_extraction_router import entity_extraction_routes
from core.shared_state import app_state
from message_bus.rabbitmq_setup import setup_rabbitmq_infrastructure

RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

async def periodic_health_check():
    pass

async def tasks_gatherer():
    tasks = [periodic_health_check()]
    return await asyncio.gather(*tasks)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # logger.info("Setting up RabbitMQ exchange and queues...")
    # await setup_rabbitmq_infrastructure()  # Ensure RabbitMQ infrastructure is set up before starting the app.

    print("Starting task consumers...")
    consumer_task = asyncio.create_task(tasks_gatherer())
    yield
    consumer_task.cancel()
    try:
        await consumer_task
    except asyncio.CancelledError:
        pass 

app = FastAPI(lifespan=lifespan)
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)


# Add routes
# app.include_router(chat_routes)
app.include_router(entity_extraction_routes)


@app.get('/api/health')
async def health_check():
    return {'status': 'ok'}

if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    logger.success("Done!")
    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment
