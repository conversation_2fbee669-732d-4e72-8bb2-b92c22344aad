from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    CampaignSendLinkedInInvitation,
                                    TestScheduleJob)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail, WebhooksNewMessage)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, SendLinkedInRequest, 
                                    RespondEmail)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile, LinkedInConnection, SentMessage, ReceivedMessage)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json
import re
from bs4 import BeautifulSoup

router = APIRouter(
    prefix = "/api",
    tags=['Outreach Management'],
    responses={404: {'description': 'Not found'}},
)

#region WEBHOOKS
@router.post("/campaign/emails/webhooks/sent", status_code=status.HTTP_200_OK)
async def webhooks_sent_email(
    webhooks_new_email: WebhooksNewEmail,
    db: AsyncSession = Depends(get_session)
):
    """
    Handles webhooks for successfully sent emails from Unipile.

    This endpoint updates the `SentEmail` record with details from the webhook
    and updates the corresponding company's outreach status to "Sent".

    Args:
        webhooks_new_email: The webhook payload containing details of the sent email.
        db: The database session.
    """
    try:
        stmt = ( # Update the SentEmail record with information from the webhook.
            update(SentEmail)
            .where(SentEmail.tracking_id==webhooks_new_email.tracking_id)
            .values(
                unipile_id = webhooks_new_email.email_id,
                subject = webhooks_new_email.subject,
                from_address = webhooks_new_email.from_attendee["identifier"],
                from_info = webhooks_new_email.from_attendee,
                message_id = webhooks_new_email.message_id
            )
        )
        await db.execute(stmt)
        
        # Get the company_id associated with the sent email.
        query = select(SentEmail.company_id).where(SentEmail.tracking_id==webhooks_new_email.tracking_id)
        results = await db.execute(query)
        company_id = results.scalars().first()

        # Update the company's outreach status to "Sent" (4) if it's not already in a later stage.
        stmt = update(Company).filter(Company.company_id==company_id, Company.email_confirmation_status_id.notin_([4,5,6])).values(email_confirmation_status_id=4)
        await db.execute(stmt)
        await db.commit()
        logger.info(f"Email {webhooks_new_email.email_id} was sent successfully")

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    pass


@router.post("/campaign/emails/webhooks/received", status_code=status.HTTP_200_OK)
async def webhooks_received_email(
    webhooks_new_email: WebhooksNewEmail
):
    """
    Receives webhooks for new incoming emails and queues them for processing.

    This endpoint acts as a lightweight receiver. It takes the webhook payload,
    converts it to a dictionary, and publishes it as a task to a message queue
    for asynchronous processing by a worker.

    Args:
        webhooks_new_email: The webhook payload for the received email.
    """
    try:
        message = webhooks_new_email.dict()
        await publish_task(task_type="receive_reply_webhooks", message_body=message, connection_pool=app_state["rabbitmq_conn_pool"])
        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) 


@router.post("/campaign/emails/webhooks/tracking")
async def webhooks_track_email(
    webhook_email_tracking: WebhooksEmailTracking,
    db: AsyncSession = Depends(get_session)
):
    """
    Handles email tracking webhooks for events like 'mail_opened'.

    This endpoint updates the status of a company and the corresponding sent email
    when a tracking event (e.g., an email open) is received.

    Args:
        webhook_email_tracking: The webhook payload containing tracking information.
        db: The database session.
    """
    try:
        if webhook_email_tracking.event == "mail_opened":
            # Find the company associated with the tracking ID.
            tracking_id = webhook_email_tracking.tracking_id
            query = select(SentEmail.company_id).where(SentEmail.tracking_id==tracking_id)
            results = await db.execute(query)
            company_id = results.scalars().first()

            stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(unipile_id=webhook_email_tracking.email_id)
            await db.execute(stmt)

            if company_id:
                # Get the company record to check its current status.
                query = select(Company).where(Company.company_id==company_id)
                results = await db.execute(query)
                company = results.scalars().first()  
                if company.email_confirmation_status_id == 4:
                    stmt = update(Company).where(Company.company_id==company_id).values(email_confirmation_status_id=5)
                    await db.execute(stmt)
                    await db.commit()
                    
                # Mark the sent email as opened.
                stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(email_opened=True, unipile_id=webhook_email_tracking.email_id)
                await db.execute(stmt)
                await db.commit()

                query = select(SentEmail).filter(SentEmail.tracking_id==tracking_id)
                results = await db.execute(query)
                sent_email = results.scalars().first()

                # If the message_id is missing, retrieve it from Unipile to complete the record.
                if sent_email.message_id == None:
                    email_client = EmailClient()
                    email_client.set_unipile_account_id(sent_email.account_id)
                    response = email_client.retrieve_email(sent_email.unipile_id)
                    if response.status_code == 200:
                        email_data = response.json()
                        message_id = email_data["message_id"]
                        from_info = email_data["from_attendee"]
                        stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(
                            message_id=message_id, 
                            from_info=from_info
                        )
                        await db.execute(stmt)

                await db.commit()
                logger.info(f"Email sent to company_id: {company_id} has been opened")
        return {
            "status": "success"
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
#endregion


#region LINKEDIN WEBHOOKS
async def handle_webhooks_message_sent_v2(
    webhooks_new_message: WebhooksNewMessage,
    client: LinkedInClient,
    own_provider_id: str,
    own_profile: dict,
    db: AsyncSession = Depends(get_session)
):
    """
    Handles webhooks for LinkedIn messages sent *by the user*.

    This function processes a webhook for an outgoing message, identifies the
    recipient, and updates the corresponding `SentMessage` record in the database.
    It handles both initial outreach messages and subsequent messages in an existing chat.

    Args:
        webhooks_new_message: The webhook payload for the new message.
        client: An initialized LinkedInClient.
        own_provider_id: The Unipile provider ID of the user's own account.
        own_profile: The user's own LinkedIn profile data.
        db: The database session.
    """
    recipient_public_id_list = []
    recipient_list = [] # Identify the recipient(s) of the message.
    for attendee in webhooks_new_message.attendees:
        profile = client.retrieve_profile(attendee["attendee_profile_url"]).json()
        public_id = profile["public_identifier"]
        if attendee["attendee_provider_id"] != own_provider_id:                
            recipient_public_id_list.append(public_id)
            recipient_list.append(attendee)

    if len(recipient_public_id_list) <= 0:
        logger.error("This message is not sent by the user")    
        return

    else:
        recipient_username = recipient_public_id_list[0]
        recipient = recipient_list[0]        
        recipient_provider_id = recipient["attendee_provider_id"]

        # Check if a chat already exists for this chat_id.
        chat_id = webhooks_new_message.chat_id
        query = select(SentMessage).filter(SentMessage.chat_id==chat_id).order_by(SentMessage.created_at.desc())
        results = await db.execute(query)
        latest_sent_message = results.scalars().first()
        if latest_sent_message is not None: 
            logger.info("chat exists")
            # Check if this specific message has already been recorded.
            query = select(SentMessage).filter(SentMessage.unipile_id==webhooks_new_message.message_id, SentMessage.chat_id==chat_id)
            results = await db.execute(query)
            sent_message_to_update = results.scalars().first()

            if sent_message_to_update is not None: # If the message is already in the DB, update it.
                stmt = update(SentMessage).where(SentMessage.chat_id==chat_id, SentMessage.unipile_id==webhooks_new_message.message_id).values(                    
                    sender = webhooks_new_message.sender,
                    recipient = recipient,                        
                    attendees = webhooks_new_message.attendees,
                    timestamp = webhooks_new_message.timestamp
                )
                await db.execute(stmt)
                await db.commit()
                logger.info(f"Message {webhooks_new_message.message_id} was sent successfully")
                return
        
            else: # If it's a new message in an existing chat, create a new record.
                new_message = SentMessage(
                    company_id = latest_sent_message.company_id,
                    message_content = webhooks_new_message.message,
                    account_id = webhooks_new_message.account_id,
                    chat_id = webhooks_new_message.chat_id,
                    unipile_id = webhooks_new_message.message_id,
                    sender = webhooks_new_message.sender,
                    sender_username = own_profile["public_identifier"],
                    sender_provider_id = own_provider_id,
                    recipient = recipient,
                    recipient_provider_id = recipient_provider_id,
                    recipient_username = recipient_username,
                    attendees = webhooks_new_message.attendees,
                    timestamp = webhooks_new_message.timestamp,
                )

                db.add(new_message)
                await db.commit()
                logger.info(f"Message {webhooks_new_message.message_id} was sent from an external source successfully")
                return
                

        else: # If no chat exists, this is likely the first message of an outreach sequence.
            # Find the original outreach draft sent to this prospect.
            logger.info("chat doesn't exist")
            query = (
                select(SentMessage)
                .filter(
                    SentMessage.sender_provider_id==own_provider_id, 
                    SentMessage.recipient_provider_id==recipient_provider_id, 
                    SentMessage.account_id==webhooks_new_message.account_id,
                    SentMessage.draft_type==0
                )
                .order_by(SentMessage.created_at.desc())
            )
            results = await db.execute(query)
            sent_message = results.scalars().first()
            
            if sent_message is not None:
                # If the original draft is found, update it with the details from the webhook.
                if sent_message.chat_id is None and sent_message.unipile_id is None:
                    stmt = update(SentMessage).where(SentMessage.company_id==sent_message.company_id).values(
                        chat_id = webhooks_new_message.chat_id,
                        unipile_id = webhooks_new_message.message_id,
                        sender = webhooks_new_message.sender,
                        recipient = recipient,                        
                        attendees = webhooks_new_message.attendees,
                        timestamp = webhooks_new_message.timestamp
                    )
                    await db.execute(stmt)

                    # Record that a new LinkedIn connection has been established.
                    new_connection = LinkedInConnection(
                        provider_id = own_provider_id,
                        connected_provider_id = recipient_provider_id
                    )
                    db.add(new_connection)

                    await db.commit()
                    logger.info(f"Invitation to {recipient_username} was sent successfully")

                    query = select(Company).filter(Company.company_id==sent_message.company_id)
                    results = await db.execute(query)
                    company = results.scalars().first()

                    query = select(Campaign.user_id).filter(Campaign.campaign_id==company.campaign_id)
                    results = await db.execute(query)
                    request_user_id = results.scalars().first()

                    # If the campaign is on auto-outreach, trigger the next step.
                    if company.auto_outreach == True:
                        message = {
                            "company_id": str(company.company_id),
                            "user_id": str(request_user_id)
                        }
                        await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])

                    return
            else:
                logger.warning("Sent message not found")
                return




async def handle_webhooks_message_received(
    webhooks_new_message: WebhooksNewMessage,
    client: LinkedInClient,
    own_provider_id: str,
    own_profile: dict,
    db: AsyncSession = Depends(get_session)
):
    """
    Handles webhooks for LinkedIn messages received *from a prospect*.

    This function processes an incoming message, identifies the original outreach,
    generates a suggested AI response, saves the received message to the database,
    and can trigger an automated response if the campaign is configured to do so.

    Args:
        webhooks_new_message: The webhook payload for the new message.
        client: An initialized LinkedInClient.
        own_provider_id: The Unipile provider ID of the user's own account.
        own_profile: The user's own LinkedIn profile data.
        db: The database session.
    """
    sender = webhooks_new_message.sender
    sender_profile = client.retrieve_profile(sender["attendee_profile_url"]).json()
    sender_username = sender_profile["public_identifier"]    
    sender_provider_id = sender_profile["provider_id"]    
    own_first_name = own_profile["first_name"]
    own_last_name = own_profile["last_name"]
    own_name = own_first_name + " " + own_last_name # Find the last message sent to this prospect in this chat.
    query = ( # Find the last message sent to this prospect in this chat.
        select(SentMessage)
        .filter(
            SentMessage.chat_id==webhooks_new_message.chat_id,
            SentMessage.recipient_provider_id==sender_provider_id,
            SentMessage.account_id==webhooks_new_message.account_id,
            SentMessage.draft_type.in_([0,1,2,3])
        )
        .order_by(SentMessage.created_at.desc())
    )
    results = await db.execute(query)
    sent_message = results.scalars().first()

    if sent_message is None:                
        logger.warning("this is not a replied message")
    else:
        query = select(Company).filter(Company.company_id==sent_message.company_id)
        results = await db.execute(query)
        company = results.scalars().first()

        # Proceed if the company is in a state where a reply is expected.
        if company.linkedin_message_status_id in [2,3,4,5]:
            # query = select(Representative).filter(Representative.company_id==sent_message.company_id)
            # results = await db.execute(query)
            # representative = results.scalars().first()
            # personality_analysis = representative.personality_analysis
            # email_personalization = representative.email_personalization     

            sent_subject = ""

            # Call the AI engine to generate a suggested response.
            service = "aiengine.*"
            function_name = "generate_response"
            params = {
                "sent_email": {
                    "sender_info": {
                        "name": own_name
                    },                    
                    "subject": sent_subject,
                    "body": sent_message.message_content,
                }, 
                "reply_email": {
                    "subject": "Re: " + sent_subject,
                    "body": webhooks_new_message.message,
                },  
                # "personality_analysis": personality_analysis,
                # "email_personalization": email_personalization
            }       
            
            rpc = await RPCClient().connect()
            response = await rpc.call(service=service, function_name=function_name, params=params)
            response_body = json.loads(response)
            result = response_body.get("result")               

            suggested_response = None
            explanation = None
            emotions = None
            sentiment = None
            key_takeaways = None

            if result == "error":
                logger.error(f"Failed to generate response for company_id: {sent_message.company_id}")

            else:
                suggested_subject = result["response_subject"]
                suggested_body = result["response_body"]
                explanation = result["explanation"]
                emotions = result["emotions"]
                sentiment = result["sentiment"]     
                key_takeaways = result["key_takeaways"]
                
                # Clean up the generated response body.
                soup = BeautifulSoup(suggested_body, "html.parser")
                suggested_body_plain = soup.text

                pattern = r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|!)\s'
                sentences = re.split(pattern, suggested_body_plain)
                sentences = [sentence.strip() for sentence in sentences if sentence.strip()]
                final_text = "\n".join(sentences).strip()
                suggested_body_plain = final_text

                suggested_response = {
                    "subject": suggested_subject,
                    "body": suggested_body_plain
                }            

            # Create a record for the received message.
            reply_message = ReceivedMessage(
                message_content = webhooks_new_message.message,            
                account_id = webhooks_new_message.account_id,
                company_id = sent_message.company_id,
                chat_id = webhooks_new_message.chat_id,
                unipile_id = webhooks_new_message.message_id,
                sender = webhooks_new_message.sender,
                sender_username = sender_username,
                sender_provider_id = sender_provider_id,
                recipient = sent_message.sender,
                recipient_username = own_profile["public_identifier"],
                recipient_provider_id = own_provider_id,
                attendees = webhooks_new_message.attendees,
                suggested_response = suggested_response,
                explanation = explanation,
                emotions = emotions,
                sentiment = sentiment,
                key_takeaways = key_takeaways,
                timestamp = webhooks_new_message.timestamp
            )

            db.add(reply_message)

            # Update the company's LinkedIn status to "Replied".
            stmt = update(Company).filter(Company.company_id==sent_message.company_id).values(linkedin_message_status_id=4)
            await db.execute(stmt)

            # If auto-outreach is enabled, send the suggested response automatically.
            if suggested_response is not None and company.auto_outreach == True:
                response = client.send_message(message_body=suggested_body_plain, recipient_profile_url=sender["attendee_profile_url"])
                if response.status_code not in [200,201]:
                    logger.error(f"Failed to send response to {sender_username}")
                else:
                    response_message = SentMessage(
                        message_content = suggested_body_plain,
                        account_id = webhooks_new_message.account_id,
                        company_id = sent_message.company_id,
                        chat_id = webhooks_new_message.chat_id,
                        unipile_id = response.json()["message_id"],
                        sender_username = own_profile["public_identifier"],
                        sender_provider_id = own_provider_id,
                        recipient_username = sender_username,
                        recipient_provider_id = sender_provider_id,
                        timestamp = webhooks_new_message.timestamp
                    )
                    db.add(response_message)

                    # Update the company's LinkedIn status to "Responded".
                    stmt = update(Company).filter(Company.company_id==sent_message.company_id).values(linkedin_message_status_id=5)
                    await db.execute(stmt)
            logger.info(f"Message received successfully from {sender_username}")
        await db.commit()

@router.post("/campaign/linkedin/webhooks/message/new")
async def webhooks_new_linkedin_message(
    webhooks_new_message: WebhooksNewMessage,
    db: AsyncSession = Depends(get_session)
):
    """
    Main entry point for new LinkedIn message webhooks.

    This function determines if the message was sent by the user or received from
    a prospect and calls the appropriate handler function.

    Args:
        webhooks_new_message: The webhook payload for the new message.
        db: The database session.
    """
    try:
        # Initialize the LinkedIn client and get the user's own profile details.
        client = LinkedInClient()
        client.set_unipile_account_id(webhooks_new_message.account_id)
        own_profile = client.retrieve_own_profile().json()
        own_provider_id = own_profile["provider_id"]

        # Determine if the message was sent by the user or received.
        if own_provider_id == webhooks_new_message.sender["attendee_provider_id"]:
            logger.info("Received sent message webhooks") # Handle outgoing message.
            await handle_webhooks_message_sent_v2(
                client=client,
                own_provider_id=own_provider_id,
                own_profile=own_profile,                
                webhooks_new_message=webhooks_new_message,
                db=db
            )

        else:
            logger.info("Received received message webhooks") # Check if this received message has already been processed.
            query = select(ReceivedMessage).filter(ReceivedMessage.unipile_id==webhooks_new_message.message_id)
            results = await db.execute(query)
            received_message = results.scalars().first()
            if received_message is not None:
                logger.info("This message has already been received")
            else:
                # Handle incoming message.
                await handle_webhooks_message_received(
                    client=client, 
                    own_provider_id=own_provider_id, 
                    own_profile=own_profile,                
                    webhooks_new_message=webhooks_new_message, 
                    db=db
                )


        await db.commit()
        return {
            "status": "success"
        }        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion

webhooks_routes = router
