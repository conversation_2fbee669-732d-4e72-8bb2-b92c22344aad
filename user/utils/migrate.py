from database.database import POSTGRES_DATABASEURL

from sqlalchemy import create_engine, text
from alembic.config import Config
from alembic import command
from loguru import logger
import os

def run_migration() -> None:
    """
    Executes a complete, destructive database migration.

    This function orchestrates a migration process that first wipes the
    existing Alembic migration history by dropping the version table and
    deleting old migration files. It then generates a new revision based on
    the current model state and applies it.

    Warning: This is not a standard incremental migration and will reset
    the migration history.
    """
    # Clean up previous migration tracking information.
    drop_alembic_table()

    # Remove all old migration script files.
    remove_migration_versions()
    
    # Configure Alembic from the .ini file.
    alembic_cfg = Config("./alembic.ini")
    # Create a new revision file based on the current state of the models.
    command.revision(alembic_cfg, message="ROBOMAN User DB Migration", autogenerate=True)
    # Apply the latest revision to the database.
    command.upgrade(alembic_cfg, "head")

    logger.success("User DB Migrated")


def drop_alembic_table() -> None:
    """
    Drops the 'alembic_version' table from the database.

    This function connects to the database and executes a raw SQL command
    to remove the table that <PERSON><PERSON><PERSON> uses to track migration history.
    """
    logger.info('Dropping alembic table.....')
    try:
        # Create a database engine from the connection URL.
        engine = create_engine(POSTGRES_DATABASEURL)
        with engine.connect() as connection:
            # Execute the DROP TABLE command.
            connection.execute(text("DROP TABLE alembic_version"))
            connection.commit()
        logger.success('Drop success!')
    except Exception as e:
        # Log any errors that occur, such as the table not existing.
        logger.error('Delete failed!')
        logger.error(e)


def remove_migration_versions() -> None:
    """
    Deletes all migration script files from the versions directory.

    This function scans the './migrations/versions' folder and removes all
    files, effectively clearing the history of generated migration scripts.
    It intentionally leaves the `__init__.py` file untouched.
    """
    folder_path = './migrations/versions'
    files = os.listdir(folder_path)
    logger.info(files)
    # Filter out the __init__.py file to avoid deleting it.
    migration_files = [file for file in files if file != '__init__.py']
    logger.info(migration_files)
    if migration_files:
        logger.info('True')
        # Loop through and delete each migration file.
        for migration_file in migration_files:
            migration_file_path = f'{folder_path}/{migration_file}'
            os.remove(migration_file_path)
            logger.info(f"Removed: {migration_file_path}")
    else:
        logger.info('False')