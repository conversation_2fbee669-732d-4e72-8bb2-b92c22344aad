import json
from io import StringIO, BytesIO
from pydantic import BaseModel

from procedures.utils import extract_entities_from_file
from schema.extraction_schema.mortgage_offer import MortgageOffer
from schema.extraction_schema.mortgage_account import MortgageSubAccountList

from core.config import settings

def mortgage_offer_extraction(file_content: BytesIO):
    instruction = """
    You are a helpful assistant that extracts structured from a mortgage offer document.
    This document provides complete information about the user's loan. This information includes the annual interest rate (APR), the loan term, the beginning repayment date, the total loan amount, the monthly payment, and any additional fees.
    When analysing monthly repayments, look at the repayment periods, which include: the number of repayments (equivalent to months), the monthly repayment amount and the interest rate. There are two types of interest rates: fixed and variable. Typically, there will be periods where users will repay the loan monthly at a fixed rate, then repay at a variable rate. Here is an example.

    59 payments at a fixed rate of 5.69% which includes initial interest: £730.42
    followed by
    60 payments at the Northern Rock standard variable rate, currently 7.09%: £754.70
    followed by
    1 payments at the Northern Rock standard variable rate, currently 7.09%: £754.41

    However, loan agreements often have multiple types of loans or accounts to which the loan must be repaid. Here are some types of information to note:
    1 - Some contracts will have secured loans and unsecured loans, both of which will have separate monthly payments with separate interest rates. Analyze this in the contract
    2 - Some contracts will have multiple sub-accounts that need to be paid, or are listed as part 1 and part 2 payments in each payment period with different interest rates and monthly payments
    3 - If the contract does not have the above information, most will only pay for a single loan, not into sub-accounts. For this case, only show information of one account
    The mortgage document will have first payment with initial interest. If you find it, treat it as an single period of the loan account, not include that if document does not have that information

    Search carefully and find out Repayment beginning day (or loan start time) find down the date including date, month and year. Please search the year at the beginning of the end of the document if you can not find this information. Then the officially repayment state date will be the 1st day of the next month

    At the end of the contract, there will be additional fees from insurance or the lender's service fee. If these fees are labeled "added to your loan", "added to mortgage" or similar terms, recalculate the total amount to be paid by adding these fees.
    From the given file, extract the information and format it as valid JSON.
    Do not include any information that is not related to the loan.
    Do not provide any explanations or commentary in your output.
    The loan term, if it is shown in year, convert to month.
    """

    return extract_entities_from_file(instruction, MortgageOffer, file_content)

def sub_accounts_extraction(file_content: BytesIO):
    instruction = """You are an expert document analysis system specializing in identifying and cataloging mortgage sub-accounts. Your task is to analyze one or more documents (including loan offers, statements, and transaction histories) from a single mortgage deal and extract a comprehensive list of all associated sub-accounts.

    **Your goal is to accurately identify all unique sub-accounts and output them as a JSON object, strictly adhering to the `MortgageSubAccountList` Pydantic schema.**

    **Key Principles for Extraction:**

    1.  **Cross-Document Analysis:** Look for sub-account information across all provided documents. These may be listed explicitly as different "loan parts" in a loan offer, appear as different account numbers in transaction tables, or be detailed in statement summaries.
    2.  **Unique Identification:** Identify sub-accounts by their unique `sub_account_number`. You must search for these numbers in:
        * **Mortgage Offer Documents:** Look for multiple account numbers, loan parts, or loan reference numbers listed for a single applicant.
        * **Mortgage Statements:** Identify any sections that summarize or detail multiple loans or accounts belonging to the same account holder.
        * **Transaction History Documents:** This is a primary source. Look for transaction tables where each entry is associated with a specific, different account number (e.g., a "sub-account").
    3.  **Account Number and Description Association:** For each identified `sub_account_number`, diligently search for its corresponding `sub_account_description`. This descriptive name or product type (e.g., "Secured Loan", "Unsecured Loan", "Main Mortgage", "Buy-to-Let Part", "Loan 1", "Loan 2") is crucial for distinguishing loan components and is often found:
        * Directly adjacent to the account number in headers or summary tables.
        * In the header or summary section specific to that sub-account.
        * Within the loan offer, describing different "parts" or "components" of the mortgage deal.
        * Implicitly, if a document clearly segregates sections for "Mortgage Loan" vs. "Further Advance".
        * **Crucial Fallback:** If, after a thorough search, a clear and explicit `sub_account_description` cannot be found for a given `sub_account_number`, you *must* default its value to the string format "Sub account <sub_account_number>" (e.g., "Sub account *********").

    **Instructions for Output:**

    1.  **JSON Format:** Your final output *must* be a single, valid JSON object.
    2.  **Schema Compliance:** Adhere strictly to the `MortgageSubAccountList` Pydantic schema. The top-level key must be `sub_accounts`, containing a list of `MortgageSubAccount` objects.
    3.  **Uniqueness:** Ensure the final list contains only unique `sub_account_number` entries.
    4.  **Handling Missing Data:**
        * The `sub_account_description` field *must always* have a value. Use the fallback "Sub account <sub_account_number>" if no explicit description is found.
        * All other fields that are not found and are optional (if any were added in future iterations) *must* be set to `null`.
    5.  **No Extraneous Content:** Your response must contain *only* the JSON object. Do not include any introductory text, conversational remarks, or markdown outside the JSON block."""

    return extract_entities_from_file(instruction, MortgageSubAccountList, file_content, include_thoughts=True)