import React, { useEffect, useState } from 'react';
import { OnboardingHeaderPart } from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import industriesData from "../../assets/industries.json";
import countriesData from "../../assets/countries.json";
import { Country, State } from 'country-state-city';

const OnboardingCampaignMatching = () => {
  const navigate = useNavigate();
  //
  const [isLoading, setIsLoading] = useState(false);
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  //

  const WelcomeString = () => {
    // This part is for the welcome string section

    const [showPopup, setShowPopup] = useState(false);
    const [location, setLocation] = useState(localStorage.getItem('location') || '');

    const handleConfirmLocation = () => {
      localStorage.setItem('location', location); // Save the new location to localStorage
      setShowPopup(false); // Close the popup
      window.location.reload(); // Reload the page
    };

    const handleClosePopup = () => {
      setShowPopup(false);
      setLocation(localStorage.getItem('location'))
    }

    return (
      <div>
        <h1 className="text-4xl font-bold text-center mb-3">
          Finding Matching Companies
        </h1>

        {/* Popup for Changing Location */}
        {showPopup && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl w-96">
              <h2 className="text-xl font-bold mb-4">Change Location</h2>
              <input
                type="text"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                placeholder="Enter new location"
                className="w-full px-3 py-2 border rounded mb-4"
              />
              <div className="flex justify-end">
                <button
                  className="bg-gray-300 text-black px-4 py-2 rounded mr-2"
                  onClick={handleClosePopup}
                >
                  Cancel
                </button>
                <button
                  className="bg-blue-500 text-white px-4 py-2 rounded"
                  onClick={handleConfirmLocation}
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const UserInfomationBoard = () => {
    // This part is for the user information board
    return (
      <div className="flex-grow flex flex-col items-center space-y-8 py-4 px-4 md:px-8">
        {/* First Part */}
        <WelcomeString />
        {/* Second Part */}
        <ProfilesPart />
      </div>
    );
  };

  const Footer = () => {
    // This part is for the footer section
    return (
      <div className='py-4 bg-white flex justify-end shadow-2xl'>
        <div className="mb-3 mt-3">
          <button
            className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-[50px] rounded-full"
            onClick={handleNextClick}
          >
            Next
          </button>
        </div>
      </div>
    )
  }

  const ContentBox = ({ company }) => {
    // This part is for the content box in the target client section

    // Check if the company data is "empty"
    const isEmpty = !company.company_name && !company.industry && !company.company_contact.rep_name;

    return (
      <div className="p-4 w-[400px] bg-gray-100 rounded-lg">
        {isEmpty ? (
          // If it's an empty box, show nothing
          <div className="h-full w-full bg-transparent"></div>
        ) : (
          <>
            <h1 className="text-blue-800 text-xl font-bold mb-2">{company.company_name || "N/A"}</h1>
            <ul className="list-disc pl-5">
              <li className="text-md mb-1">Industry: {company.industry || "N/A"}</li>
              <li className="text-md mb-1">
                LinkedIn:{" "}
                <a
                  href={company.company_contact?.company_linkedin || "#"}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 underline"
                >
                  {company.company_linkedin || "N/A"}
                </a>
              </li>
              <li className="text-md mb-1">
                Location: {company.location_name || "N/A"}
              </li>
              {/* <li className="text-md mb-1">
                Email:{" "}
                <a
                  href={`mailto:${company.rep_email || ""}`}
                  className="text-blue-500 underline"
                >
                  {company.rep_email || "N/A"}
                </a>
              </li> */}
            </ul>
          </>
        )}
      </div>
    );
  };

  const ProfilesPart = () => {

    const [selectedIndustries, setSelectedIndustries] = useState([]);
    const [selectedContinents, setSelectedContinents] = useState([]);
    const [selectedCountries, setSelectedCountries] = useState([]);
    const [selectedStatesOrCities, setSelectedStatesOrCities] = useState([]);
    const [result, setResult] = useState([]);

    const findMatchingData = async () => {
      console.log("Finding matching data with the following criteria:");
      console.log("Industries:", selectedIndustries);
      console.log("Continents:", selectedContinents);
      console.log("Countries:", selectedCountries);
      console.log("States or Cities:", selectedStatesOrCities);
      localStorage.setItem("selectedIndustries", JSON.stringify(selectedIndustries));
      localStorage.setItem("selectedContinents", JSON.stringify(selectedContinents));
      localStorage.setItem("selectedCountries", JSON.stringify(selectedCountries));
      localStorage.setItem("selectedStatesOrCities", JSON.stringify(selectedStatesOrCities));
      if (
        selectedIndustries.length === 0 ||
        selectedContinents.length === 0 ||
        selectedCountries.length === 0
      ) {
        Swal.fire({
          icon: "warning",
          title: "Missing Information",
          text: "Please select all fields before searching!"
        });
        setResult([]);
        return;
      }

      try {
        const response = await fetch(`${roboman_api}/campaign/companies/match`, {
          method: "POST",
          headers: {
            "accept": "application/json",
            "Authorization": localStorage.getItem("access_token"),
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            "industries": selectedIndustries,
            "countries": selectedCountries,
            "cities_or_states": selectedStatesOrCities,
            "continents": selectedContinents,
            "search_size": 100
          }),
        });

        const data = await response.json();

        if (!response.ok || !data || data.length === 0) {
          Swal.fire({
            icon: "error",
            title: "No Results",
            text: "No matching companies found. Please try again.",
          });
          setResult([]);
          return;
        }

        setResult(data);

      } catch (error) {
        Swal.fire({
          icon: "error",
          title: "Error",
          text: "Something went wrong while searching.",
        });
        setResult([]);
      }
    };

    // This part is for the profiles section
    return (
      <div className="w-full max-w-[1500px] flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4">
        <div className="w-full lg:w-1/3 min-h-[400px]">
          <TargetIndustry
            selectedIndustries={selectedIndustries}
            setSelectedIndustries={setSelectedIndustries}
            selectedContinents={selectedContinents}
            setSelectedContinents={setSelectedContinents}
            selectedCountries={selectedCountries}
            setSelectedCountries={setSelectedCountries}
            selectedStatesOrCities={selectedStatesOrCities}
            setSelectedStatesOrCities={setSelectedStatesOrCities}
            findMatchingData={findMatchingData}
          />
        </div>
        <div className="w-full lg:w-2/3 min-h-[400px]">
          <TargetClient result={result} />
        </div>
      </div>
    );
  };

  const TargetIndustry = ({
    selectedIndustries,
    setSelectedIndustries,
    selectedContinents,
    setSelectedContinents,
    selectedCountries,
    setSelectedCountries,
    selectedStatesOrCities,
    setSelectedStatesOrCities,
    findMatchingData
  }) => {


    // New states for states/cities dropdown
    const [availableStatesOrCities, setAvailableStatesOrCities] = useState([]);

    // Your existing data
    const industriesList = industriesData.Industries;
    const continentsList = Object.keys(countriesData);

    const getAvailableCountries = () => {
      const allCountries = selectedContinents.flatMap(continent => countriesData[continent] || []);
      return Array.from(new Set(allCountries));
    };

    // Update available states/cities when selectedCountries changes
    useEffect(() => {
      if (selectedCountries.length === 0) {
        setAvailableStatesOrCities([]);
        setSelectedStatesOrCities([]);  // Correct: clear array, not string
        return;
      }

      const allCountries = Country.getAllCountries();
      let statesAccumulator = [];

      selectedCountries.forEach((countryName) => {
        const countryObj = allCountries.find(c => c.name === countryName);
        if (!countryObj) return;

        const states = State.getStatesOfCountry(countryObj.isoCode);
        if (states.length > 0) {
          statesAccumulator = statesAccumulator.concat(
            states.map(s => ({
              name: s.name,
              isoCode: s.isoCode,
              countryCode: countryObj.isoCode
            }))
          );
        }
      });

      const uniqueStates = Array.from(new Map(
        statesAccumulator.map(s => [s.countryCode + s.isoCode, s])
      ).values());

      setAvailableStatesOrCities(uniqueStates);
      setSelectedStatesOrCities([]);  // Correct reset
    }, [selectedCountries]);


    const handleAddSelection = (value, selectedList, setSelectedList) => {
      if (value && !selectedList.includes(value)) {
        setSelectedList([...selectedList, value]);
      }
    };

    const handleRemoveSelection = (index, selectedList, setSelectedList) => {
      setSelectedList(selectedList.filter((_, i) => i !== index));
    };

    return (
      <div className="p-3 h-[800px] bg-white flex flex-col space-y-4 rounded-xl shadow-md items-center overflow-hidden">

        {/* Title */}
        <div className="flex items-center justify-center text-lg font-bold">
          SEARCHING CRITERIA
        </div>

        {/* Scrollable Content */}
        <div className="w-full flex-1 overflow-y-auto flex flex-col space-y-4 items-center p-2">

          {/* Industries Section */}
          <div className="w-full flex flex-col space-y-2">
            <label className="font-semibold">Industries</label>
            <select
              className="w-full border px-3 py-2 rounded"
              onChange={(e) => {
                handleAddSelection(e.target.value, selectedIndustries, setSelectedIndustries);
                e.target.value = "";  // Reset dropdown to placeholder
              }}
              value=""  // Always show placeholder
            >
              <option value="" disabled>Select an Industry</option>
              {industriesList.map((industry, index) => (
                <option key={index} value={industry}>{industry}</option>
              ))}
            </select>
            <div className="p-2 w-full min-h-[150px] border rounded-lg bg-gray-50 flex flex-wrap items-start content-start gap-2 overflow-y-auto">
              {selectedIndustries.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-300 px-2 py-1 rounded-full flex items-center text-sm bg-white"
                >
                  <button
                    className="mr-2 text-gray-500 hover:text-red-500 font-bold"
                    onClick={() => handleRemoveSelection(index, selectedIndustries, setSelectedIndustries)}
                  >
                    ×
                  </button>
                  <span><strong>{item}</strong></span>
                </div>
              ))}
              {selectedIndustries.length === 0 && (
                <div className="text-gray-400">No industries selected</div>
              )}
            </div>
          </div>

          {/* Continents Section */}
          <div className="w-full flex flex-col space-y-2">
            <label className="font-semibold">Continent</label>
            <select
              className="w-full border px-3 py-2 rounded"
              onChange={(e) => {
                handleAddSelection(e.target.value, selectedContinents, setSelectedContinents);
                e.target.value = "";
              }}
              value=""
            >
              <option value="" disabled>Select a Continent</option>
              {continentsList.map((continent, index) => (
                <option key={index} value={continent}>{continent}</option>
              ))}
            </select>
            <div className="p-2 w-full min-h-[80px] max-h-[150px] border rounded-lg bg-gray-50 flex flex-wrap items-start content-start gap-2 overflow-y-auto">
              {selectedContinents.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-300 px-2 py-1 rounded-full flex items-center text-sm bg-white"
                >
                  <button
                    className="mr-2 text-gray-500 hover:text-red-500 font-bold"
                    onClick={() => handleRemoveSelection(index, selectedContinents, setSelectedContinents)}
                  >
                    ×
                  </button>
                  <span>{item}</span>
                </div>
              ))}
              {selectedContinents.length === 0 && (
                <div className="text-gray-400">No continents selected</div>
              )}
            </div>
          </div>

          {/* Countries Section */}
          <div className="w-full flex flex-col space-y-2">
            <label className="font-semibold">Countries</label>
            <select
              className="w-full border px-3 py-2 rounded"
              onChange={(e) => {
                handleAddSelection(e.target.value, selectedCountries, setSelectedCountries);
                e.target.value = "";
              }}
              disabled={selectedContinents.length === 0}
              value=""
            >
              <option value="" disabled>Select a Country</option>
              {getAvailableCountries().map((country, index) => (
                <option key={index} value={country}>{country}</option>
              ))}
            </select>
            <div className="p-2 w-full min-h-[80px] max-h-[150px] border rounded-lg bg-gray-50 flex flex-wrap items-start content-start gap-2 overflow-y-auto">
              {selectedCountries.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-300 px-2 py-1 rounded-full flex items-center text-sm bg-white"
                >
                  <button
                    className="mr-2 text-gray-500 hover:text-red-500 font-bold"
                    onClick={() => handleRemoveSelection(index, selectedCountries, setSelectedCountries)}
                  >
                    ×
                  </button>
                  <span>{item}</span>
                </div>
              ))}
              {selectedCountries.length === 0 && (
                <div className="text-gray-400">No countries selected</div>
              )}
            </div>
          </div>

          {/* New States/Cities Section */}
          <div className="w-full flex flex-col space-y-2">
            <label className="font-semibold">States / Cities</label>
            <select
              className="w-full border px-3 py-2 rounded"
              onChange={(e) => {
                handleAddSelection(e.target.value, selectedStatesOrCities, setSelectedStatesOrCities);
                e.target.value = "";  // reset dropdown to placeholder
              }}
              disabled={selectedCountries.length === 0 || availableStatesOrCities.length === 0}
              value=""
            >
              <option value="" disabled>Select a State or City</option>
              {availableStatesOrCities.map((item, index) => (
                <option key={index} value={item.name}>
                  {item.name} ({item.countryCode})
                </option>
              ))}
            </select>

            <div className="p-2 w-full min-h-[80px] max-h-[150px] border rounded-lg bg-gray-50 flex flex-wrap items-start content-start gap-2 overflow-y-auto">
              {selectedStatesOrCities.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-300 px-2 py-1 rounded-full flex items-center text-sm bg-white"
                >
                  <button
                    className="mr-2 text-gray-500 hover:text-red-500 font-bold"
                    onClick={() => handleRemoveSelection(index, selectedStatesOrCities, setSelectedStatesOrCities)}
                  >
                    ×
                  </button>
                  <span>{item}</span>
                </div>
              ))}
              {selectedStatesOrCities.length === 0 && (
                <div className="text-gray-400">No states or cities selected</div>
              )}
            </div>
          </div>
        </div>

        {/* Searching Button */}
        <div className="w-[30%] pb-2">
          <button className="w-full bg-[#223F9E] text-white px-3 py-1.5 rounded-full hover:bg-blue-800 font-semibold"
            onClick={findMatchingData}
          >
            Searching
          </button>
        </div>
      </div>
    );
  };

  const TargetClient = ({ result }) => {
    // This part show the target prospect for the campaign, by founding the matching company

    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 6; // 2 columns * 3 rows

    const generateBlankObject = () => ({
      // Helper function to generate a single blank object
      company_name: "",
      industry: "",
      company_contact: {
        company_linkedin: "",
        rep_name: "",
        rep_email: "",
      },
    });

    // Fill missing items to match a multiple of 6
    const padResult = (data) => {
      const paddingNeeded = itemsPerPage - (data.length % itemsPerPage || itemsPerPage);
      return [...data, ...Array(paddingNeeded).fill(generateBlankObject())];
    };

    const paddedResult = padResult(result);

    // Paginate the padded result
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = paddedResult.slice(indexOfFirstItem, indexOfLastItem);

    const totalPages = Math.ceil(paddedResult.length / itemsPerPage);

    const handleNextPage = () => {
      if (currentPage < totalPages) {
        setCurrentPage((prev) => prev + 1);
      }
    };

    const handlePrevPage = () => {
      if (currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      }
    };

    return (
      <div className="p-3 bg-white flex flex-col space-y-4 rounded-xl h-full items-center shadow-md">
        {/* Title */}
        <div className="text-center text-lg font-bold">TARGET CLIENTS</div>

        {/* Items Container */}
        <div
          className="w-full grid gap-4 p-2 grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 h-full"
          style={{ gridAutoRows: "minmax(0, 1fr)" }}
        >
          {(!result || result.length === 0) ? (
            // Message spans all columns to center properly
            <div className="col-span-full flex justify-center items-center w-full text-gray-400 italic text-center">
              Select The Criteria and press Searching
            </div>
          ) : (
            currentItems.map((company, index) => (
              <div key={index} className="flex justify-center">
                <ContentBox company={company} />
              </div>
            ))
          )}
        </div>

        {/* Pagination Container */}
        <div className="flex justify-between w-full mt-4">
          <button
            onClick={handlePrevPage}
            disabled={currentPage === 1}
            className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
          >
            Prev
          </button>
          <div className="flex items-center">
            Page {totalPages === 0 ? 0 : currentPage} of {totalPages}
          </div>
          <button
            onClick={handleNextPage}
            disabled={currentPage === totalPages || totalPages === 0}
            className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    );
  };

  const handleNextClick = () => {
    // This function for Next button in the footer
    navigate('/reviewinfo');
  };

  return (

    <div>
      {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white p-6 rounded shadow-lg">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <span className="text-lg font-medium">Loading, please wait...</span>
            </div>
          </div>
        </div>
      )}
      {/* Render user data or other components here */}
      <div className="flex flex-col min-h-screen w-full bg-gray-100">
        {/* This part is for the header section */}
        <OnboardingHeaderPart />
        {/* This part is for the main/body section */}
        <UserInfomationBoard />
        {/* Footer is placed here to ensure it stays at the bottom */}
        <Footer />
      </div>
    </div>

  );
};

export default OnboardingCampaignMatching;
