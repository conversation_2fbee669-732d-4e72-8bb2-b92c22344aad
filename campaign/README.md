<h1 style="font-size: 36px; font-family: <PERSON><PERSON>, sans-serif; text-align: center;">
  ROBOMAN - Campaign Service
</h1>

Welcome to the *Campaign Service* documentation of the *ROBOMAN* project. This document provides details on code structures and API references for the Campaign service.

# Table of Contents

1. [Introduction](#introduction)
    - 1.1. [Overview](#overview)
    - 1.2. [Goals](#goals)

2. [Features](#features)

3. [Code Structure](#code-structure)

4. [Technologies](#technologies)

5. [API Reference](#api-reference)

6. [Setup and Installation](#setup-and-installation)

7. [Contribution](#contribution)

# Introduction

## Overview

The Campaign Service is a core component of the ROBOMAN platform, responsible for managing and automating outreach campaigns. It orchestrates multi-channel communication (Email, LinkedIn), leverages asynchronous task processing via RabbitMQ, and integrates with other microservices like the AI Engine for content generation and the User Service for credit management. The service is built with FastAPI and uses PostgreSQL for data persistence.

## Goals

The primary goals of the Campaign Service are:

- **Centralize Campaign Logic**: Provide a single service for creating, managing, and monitoring outreach campaigns.
- **Automate Outreach**: Implement robust scheduling and automation for sending multi-step email and LinkedIn sequences.
- **Enable Personalization at Scale**: Integrate with the AI Engine and Humantic AI to generate highly personalized messages based on prospect data.
- **Track Performance**: Offer detailed tracking of outreach efforts, including sent, opened, and replied statuses.
- **Ensure Scalability and Reliability**: Utilize a message bus architecture to handle high volumes of asynchronous tasks reliably.

# Features

- **Campaign & Prospect Management**:
    - Create, update, and delete campaigns with specific goals and formats.
    - Add prospects from Elasticsearch, CSV files, or manually.

- **Multi-Channel Outreach**:
    - **Email**: Automated email sequences with open and reply tracking via Unipile webhooks.
    - **LinkedIn**: Automated connection requests and messages with reply tracking via Unipile webhooks.

- **AI-Powered Content Generation**:
    - Integrates with the AI Engine to generate personalized email and LinkedIn drafts.
    - Leverages Humantic AI personality analysis for tailored messaging.

- **Asynchronous Architecture**:
    - Uses RabbitMQ for a task queue system to handle long-running operations like profile analysis, draft generation, and sending outreach.
    - Implements an RPC server and client for synchronous communication with other services.

- **Automated Workflows**:
    - A built-in scheduler (APScheduler) manages daily tasks like resetting email quotas and continuing outreach sequences.
    - Auto-outreach feature to run entire campaigns with minimal manual intervention.

- **Webhook Integration**:
    - Dedicated endpoints to receive and process webhooks from Unipile for real-time event handling (email sent, opened, replied; LinkedIn message sent, received).

- **Database and Migrations**:
    - Uses PostgreSQL with SQLAlchemy ORM for structured data storage.
    - Manages database schema changes with Alembic migrations.

# Code Structure

<pre>
.
│   .env
│   alembic.ini
│   create_db.py
│   Dockerfile
│   main.py
│   README.md
│   requirements.txt
│
├───core
│   │   config.py
│   │   network.py
│
├───crud
│   │   crud_base.py
│   │   crud_chat.py
│   │   crud_conversation.py
│
├───database
│   │   database.py
│   │   models.py
│
├───message_bus
│   │   rabbitmq_connection.py
│   │   rpc_client.py
│   │   rpc_server.py
│   │   task_consumer.py
│   │   task_publisher.py
│   │
│   ├───procedures
│   │   │   outreach.py
│   │
│   └───tasks
│           elasticsearch_tasks.py
│           humantic_ai_task.py
│           linkedin_outreach_tasks.py
│           outreach_tasks.py
│           unipile_tasks.py
│
├───migrations
│   │   env.py
│   │   README
│
├───routes
│   └───v1
│           campaign.py
│           chat.py
│           company.py
│           linkedin_outreach.py
│           outreach.py
│           webhooks.py
│
├───schema
│   │   campaign_schema.py
│   │   company_schema.py
│   │   conversation_chatmessage_schema.py
│   │   outreach_schema.py
│   │   rep_schema.py
│   │   webhooks_schema.py
│
├───shared_state.py
│
├───task_scheduler
│   │   scheduler.py
│   │   tasks.py
│
└───utils
    │   migrate.py
    │
    ├───elasticsearch_utils
    │   │   elastic_query.py
    │   │   get_elasticsearch.py
    │
    ├───humantic_ai_utils
    │   │   humantic_request_wrapper.py
    │
    └───unipile_utils
            unipile_client.py
            unipile_request_wrapper.py
</pre>

# Technologies

The Campaign Service leverages several key technologies:

- **FastAPI**: Modern, high-performance web framework for building APIs.
- **SQLAlchemy**: SQL toolkit and ORM for database operations.
- **Alembic**: Database migration tool.
- **RabbitMQ (aio-pika)**: Message broker for asynchronous tasks and RPC.
- **APScheduler**: For scheduling background jobs.
- **Elasticsearch**: For searching and matching companies.
- **Unipile API**: For sending emails and LinkedIn messages.
- **Humantic AI API**: For personality analysis of prospects.
- **Docker**: For containerization.
- **Loguru**: For advanced logging.
- **Pydantic**: For data validation and settings management.

# API Reference

## Campaign Management Endpoints

- **POST /campaign/new**: Create a new campaign.
- **GET /campaign/personal**: Get all campaigns for the current user with statistics.
- **GET /campaign/{campaign_id}**: Get details for a specific campaign.
- **PUT /campaign/{campaign_id}/update-details**: Update the core details of a campaign.
- **DELETE /campaign/{campaign_id}/delete**: Delete a campaign.

## Company & Prospect Endpoints

- **POST /campaign/companies/match**: Find matching companies from Elasticsearch.
- **POST /campaign/{campaign_id}/companies/add-multiple**: Add multiple companies to a campaign from Elasticsearch.
- **POST /campaign/{campaign_id}/companies/add**: Manually add a single company to a campaign.
- **POST /campaign/{campaign_id}/companies/add/file**: Add companies from an uploaded CSV file.
- **GET /campaign/{campaign_id}/companies/{page}/{page_size}**: Get a paginated list of companies in a campaign.
- **DELETE /campaign/companies/{company_id}/delete**: Delete a company from a campaign.
- **PUT /campaign/companies/{company_id}/representatives/{rep_id}/humantic/update**: Trigger a Humantic AI profile update for a representative.

## Outreach Endpoints

- **POST /campaign/emails/generate**: Asynchronously generate email drafts for a list of companies.
- **POST /campaign/emails/send**: Queue emails to be sent to a list of companies.
- **POST /campaign/linkedin/messages/send**: Queue LinkedIn messages to be sent to a list of companies.
- **POST /campaign/{campaign_id}/outreach/auto**: Start an automated outreach sequence for a campaign.
- **PUT /campaign/{campaign_id}/outreach/stop-auto**: Stop the automated outreach for a campaign.
- **POST /campaign/emails/reply-mails/respond**: Manually send a response to a received email.
- **POST /campaign/linkedin/message/respond**: Manually send a response to a received LinkedIn message.

## Webhook Endpoints

- **POST /campaign/emails/webhooks/sent**: Handles webhooks for successfully sent emails.
- **POST /campaign/emails/webhooks/received**: Handles webhooks for new incoming emails.
- **POST /campaign/emails/webhooks/tracking**: Handles email tracking webhooks (e.g., opens).
- **POST /campaign/linkedin/webhooks/message/new**: Handles webhooks for new LinkedIn messages (sent and received).

## Admin Endpoints

- **POST /admin/senders/add**: Add new sender email accounts from a CSV file.
- **GET /admin/senders**: Retrieve a list of all configured email sender accounts.
- **DELETE /admin/senders/{sender_id}/delete**: Delete a sender email account.

# Setup and Installation

## Prerequisites

- Docker and Docker Compose
- Python 3.10+
- PostgreSQL
- RabbitMQ
- Elasticsearch
- API keys for Unipile and Humantic AI

## Environment Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd ROBOMAN/campaign
   ```

2. Create a `.env` file with the following variables:
   ```
   # Server
   SERVER_PORT=8004
   PROJECT_NAME="ROBOMAN Campaign Service"

   # Database
   DATABASE_NAME=roboman_db
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=postgres
   DATABASE_HOST=postgres
   DATABASE_PORT=5432

   # RabbitMQ
   RABBITMQ_DEFAULT_USER=guest
   RABBITMQ_DEFAULT_PASS=guest
   RABBITMQ_HOST=rabbitmq
   RABBITMQ_PORT=5672
   EXCHANGE_NAME=roboman_exchange
   QUEUE_NAME=campaign_queue

   # JWT
   JWT_SECRET=your_jwt_secret
   JWT_ALGORITHM=HS256

   # Service URLs
   AI_ENGINE_URL="http://aiengine-svc/api"
   USER_SERVICE_URL="http://user-svc/api"
   AI_SERVICE_URL="http://aiengine-svc/api"

   # Elasticsearch
   ES_PASSWORD=your_es_password
   ES_USERNAME=your_es_username
   ES_HOST=elasticsearch
   ES_INDEX_NAME=roboman-test-index

   # AWS S3 (if used)
   S3_ACCESS_ID=your_aws_access_key
   S3_SECRET_KEY=your_aws_secret_key
   S3_BUCKET_NAME=your_bucket_name

   # Unipile API
   UNIPILE_SUBDOMAIN=your_unipile_subdomain
   UNIPILE_PORT=443
   UNIPILE_API_KEY=your_unipile_api_key

   # Humantic AI API
   HUMANTIC_AI_API_KEY=your_humantic_api_key
   HUMANTIC_AI_BASE_URL=https://api.humantic.ai/v1/prospects
   ```

3. Build and start the service (typically via the root `docker-compose.yml`):
   ```
   docker-compose up --build
   ```

4. The service will be available at `http://localhost:8004` with API documentation at `http://localhost:8004/docs`.

# Contribution

Kindly refer to the contribution section in the overview documentation here.