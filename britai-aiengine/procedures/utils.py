from google import genai
from google.genai import types
from google.genai.types import GenerateContentConfig, Content, Part
import json
from io import StringIO, BytesIO
from pydantic import BaseModel

from schema.extraction_schema.mortgage_offer import MortgageOffer
from schema.extraction_schema.mortgage_statement import MortgageStatementData, StatementPeriodRecord
from schema.extraction_schema.mortgage_transaction import MortgageTransactionHistory, PaymentYearList

from core.config import settings

def extract_entities_from_file(instruction: str, response_schema: BaseModel, file_content: BytesIO, include_thoughts: bool = False) -> dict:

    """
    Extract entities from a file using the provided instruction and response schema.

    Args:
        instruction (str): The instruction for the AI model.
        response_schema (BaseModel): The Pydantic model representing the expected response schema.
        file_content (BytesIO): The file content as a BytesIO object.

    Returns:
        dict: The extracted entities as a dictionary.
    """

    client = genai.Client(api_key=settings.GOOGLE_API_KEY)
    mime_type = "application/pdf"
    file = client.files.upload(
        file=file_content,
        config={"mime_type": mime_type}
    )

    response = client.models.generate_content(
        model="gemini-2.5-flash-preview-05-20",
        contents=[instruction, file],
        config=GenerateContentConfig(
            response_mime_type="application/json",
            response_schema=response_schema,
            thinking_config=types.GenerationConfigThinkingConfig(include_thoughts=include_thoughts)
        )
    )
    # Parse JSON result from response
    result = json.loads(response.text)
    return result